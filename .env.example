# Database
DATABASE_PATH=./data/database.sqlite

# Server Configuration
PORT=3001
NODE_ENV=development

# WooCommerce API Configuration
WOOCOMMERCE_URL=https://your-shop.com
WOOCOMMERCE_CONSUMER_KEY=your_consumer_key
WOOCOMMERCE_CONSUMER_SECRET=your_consumer_secret

# External Server Configuration (for XML sync)
EXTERNAL_SERVER_URL=https://your-external-server.com
EXTERNAL_SERVER_USERNAME=your_username
EXTERNAL_SERVER_PASSWORD=your_password

# Sync Configuration
SYNC_INTERVAL_MINUTES=30
ENABLE_AUTO_SYNC=true

# Logging
LOG_LEVEL=info
LOG_DIR=./logs
