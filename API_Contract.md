# RZA-WooCommerce API Vertrag

Dieses Dokument definiert die Verwendung zwischen dem RZA ERP-System und WooCommerce
für den bidirektionalen Datenaustausch. Es beschreibt die XML-Strukturen, <PERSON>lder und den Datenfluss.

**Folgende Features werden umgesetzt:**
- Täglicher Export der Produkte und Kategorien von RZA zu WooCommerce
- 15 minütliche Synchronisation von Lagerstand (stock_export) zu WooCommerce
- Stündlicher Import von Bestellungen und Kunden von WooCommerce zu RZA

**Folgende Features werden NICHT umgesetzt:**
- Synchronisation von Kunden
- Import von Bestellungen aus RZA zu WooCommerce






## Feature: Täglicher Synchronisation von RZA (full_export) zu WooCommerce
- **Zweck**: Synchronisation von Artikeln, Kategorien von RZA zu WooCommerce
- **Format**: RZA XML Export → JSON für WooCommerce REST API
- **Häufigkeit**: Tägliche Synchronisation

Die Datenstrukturen zwischen RZA und WooCommerce werden einmalig initial angelegt.
- Artikel Kategorien
  - RZA: Artikelgruppen und Unterguppen
  - WooCommerce: Kategorien
- Artikel Eigenschaften (Attributes)
  - RZA: Artikel-Felder (max. 100)
  - WooCommerce: Produkt-Eigenschaften (Attributes)

Neue Kategorien oder Eigenschaften werden beim Export von RZA in WooCommerce NICHT angelegt oder aktualisiert.
Neue Einträge aus RZA werden ignoriert.

Sollte ein Produkt ohne passende Kategorien exportiert werden,
wird das Produkt in WooCommerce ohne Kategorie importiert.

### Erweiterte Funktionalität: Staffelpreise
Um Staffelpreise in WooCommerce umzusetzen und diese aus RZA zu importieren benötigen wir
das WooCommerce Plugin "Product Bundles".

[Product Bundles](https://woocommerce.com/de/products/product-bundles/)

### Erweiterte Funktionalität: Versandarten pro Artikelgruppen
Um den Versand pro Artikelgruppe zu steuern (z.b. kein Versand von Waffen),
benötigen wir das WooCommerce Plugin "Conditional Shipping and Payments".

[Conditional Shipping and Payments](https://woocommerce.com/de/products/conditional-shipping-and-payments/)

### Datenstruktur (RZA -> WooCommerce)

#### Haupt-XML-Elemente

```xml
<root>
  <articles>         <!-- Artikel/Produkte -->
  <customers>        <!-- Kunden/Adressen -->
  <orders>           <!-- Bestellstatus (wird ignoriert) -->
  <groupDefinition>  <!-- Artikelgruppen und Untergruppen -->
  <categories>       <!-- Produktkategorien -->
  <countries>        <!-- Länderdefinitionen (wird ignoriert) -->
</root>
```

#### Artikel-Feldmapping (RZA → WooCommerce)

| RZA Feld | WooCommerce Feld | Transformation | Beschreibung |
|----------|------------------|----------------|--------------|
| `ordernumber` | `sku` | Direkt | Eindeutige Artikelnummer |
| `name` | `name` | Direkt | Produktname |
| `description_long` | `description` | HTML-Bereinigung | Produktbeschreibung (CDATA entfernt) |
| `instock` | `stock_quantity` | Max(0, instock) | Verfügbare Menge |
| `weight` | `weight` | String-Konvertierung | Gewicht in kg |
| `active` + `onlineshopstatus` | `status` | Kombinierte Logik | 'publish' wenn beide = 1, sonst 'draft' |
| `tax` | `tax_status` | Steuer-Konfiguration | 'taxable' wenn tax > 0 |
| `tax` | `tax_class` | Steuer-Konfiguration | Standard Steuerklasse in WooCommerce
| `prices` | `refular_price` | Preislogik | Standard Preis in der Standard Preisgruppe |
| `fields.field.[n]` | `attributes[n]` | Direkt | Produkteigenschaften anhand eines vordefinierten Mappings |

#### Felder / Produkt-Eigenschaften

Die Felder in fields sind für alle Artikel gleich.
Daher können bis zu 100 Felder exportiert werden,
um alle Artikeleigenschaften unterschiedlicher Gruppen
wie Waffen und Munition abzubilden.

Nicht verwendete Felder für einzelne Artikel (weil nicht benötigt)
werden in WooCommerce nicht importiert. Es erfolgt keine Prüfung.
Die Felder werden 1:1 in die WooCommerce Attribute importiert.

#### Preislogik
- **Grundpreis**: Preis mit `from: 1` aus der konfigurierten Preisgruppe
- **Aktionspreis**: Niedrigerer Preis bei Mengenrabatten (z.B. `from: 10`)
- **Preisgruppen**: Unterstützung verschiedener Kundenpreisgruppen (VK-Preis, VK-DE-Preis, etc.)

#### Artikel-XML-Struktur
```xml
<article>
  <ordernumber>ARTIKEL-123</ordernumber>
  <rzaArtikelID>774</rzaArtikelID>
  <artGroupID>1</artGroupID>
  <artSubGroupID>1</artSubGroupID>
  <categories>
    <category>271</category>
  </categories>
  <name>Produktname</name>
  <ean>1234567890123</ean>
  <barcode>BARCODE123</barcode>
  <unitID>stk</unitID>
  <instock>50</instock>
  <stock>100</stock>
  <weight>1.500</weight>
  <active>1</active>
  <onlineshopstatus>1</onlineshopstatus>
  <description_long><![CDATA[<p>Produktbeschreibung...</p>]]></description_long>
  <tax>20.00</tax>
  <prices>
    <price>
      <price>19.99</price>
      <pricegroup>VK-Preis</pricegroup>
      <from>1</from>
    </price>
    <price>
      <price>17.99</price>
      <pricegroup>VK-Preis</pricegroup>
      <from>10</from>
    </price>
  </prices>
  <fields>
    <field fieldnumber="1">Zusatzinfo 1</field>
    <field fieldnumber="2">Zusatzinfo 2</field>
  </fields>
  <textilartikel>
    <kennzeichen>1</kennzeichen>
    <textilartikelID>123</textilartikelID>
  </textilartikel>
</article>
```

#### Artikelgruppen-XML-Struktur
```xml
<groupDefinition>
  <artGroups>
    <artGroup>
      <ID>1</ID>
      <number>1</number>
      <name>Hauptkategorie</name>
      <discounts>
        <discount>
          <fromDate>01.01.2024</fromDate>
          <toDate>31.12.2024</toDate>
          <percent>5.00</percent>
          <pricegroup>VK-Preis</pricegroup>
        </discount>
      </discounts>
    </artGroup>
  </artGroups>
  <artSubGroups>
    <artSubGroup>
      <ID>1</ID>
      <number>100</number>
      <name>Unterkategorie</name>
    </artSubGroup>
  </artSubGroups>
</groupDefinition>
```

#### Kategorien-Feldmapping (RZA → WooCommerce)

| RZA Feld | WooCommerce Feld | Transformation | Beschreibung |
|----------|------------------|----------------|--------------|
| `name` | `name` | Direkt | Kategoriename |
| `ID` | `id` | Direkt | Kategorie-ID |
| `ParentID` | `parent` | Hierarchie-Mapping | Eltern-Kind-Beziehungen |
| `number` | `menu_order` | Direkt | Anzeigereihenfolge |

#### Artikel Meta-Daten
- `_rza_artikel_id` - RZA Artikel-ID (rzaArtikelID)
- `_rza_art_group_id` - Artikelgruppen-ID (artGroupID)
- `_rza_art_sub_group_id` - Untergruppen-ID (artSubGroupID)
- `_rza_unit_id` - Einheiten-ID (unitID)
- `_rza_ean` - EAN-Code
- `_rza_barcode` - Strichcode
- `_rza_tax_rate` - Steuersatz
- `_rza_shipping_time` - Lieferzeit
- `_rza_stock` - Tatsächlicher Lagerstand
- `_rza_supplier_numbers` - JSON-Array der Lieferantennummern
- `_rza_field_1` bis `_rza_field_10` - Frei definierbare Felder
- `_rza_textile_info` - JSON-Objekt mit Textilartikel-Informationen

#### Artikel-Validierung
- **Pflichtfelder**: `ordernumber`, `name`, `rzaArtikelID`
- **Preisvalidierung**: Mindestens ein Preis mit `from: 1` muss vorhanden sein
- **Status-Validierung**: `active` und `onlineshopstatus` müssen numerische Werte (0 oder 1) sein
- **Gewicht-Validierung**: Muss numerischer Wert ≥ 0 sein
- **Kategorie-Validierung**: Referenzierte Kategorien müssen existieren

#### Kunden-Validierung
- **Pflichtfelder**: `rzaAddressId`, `Zuname`, `Vorname`
- **E-Mail-Validierung**: Gültiges E-Mail-Format
- **Länder-Validierung**: Ländercode muss in Mapping-Tabelle existieren
- **Telefon-Validierung**: Formatierung und Bereinigung von Sonderzeichen





### Feature: 15 minütliche Synchronisation von Lagerstand (stock_export) zu WooCommerce
- **Zweck**: Synchronisation des Lagerstands von RZA zu WooCommerce
- **Format**: RZA XML Export → JSON für WooCommerce REST API
- **Häufigkeit**: 15 minütlich

Beim Export von RZA Lagerbeständen werden die Lagerbeständer der Artikel
aktualisiert um ein Überverkauf zu vermeiden.

#### Lagerbestand-XML-Struktur
```xml
<articles>
  <article>
    <ordernumber>ABC123</ordernumber>
    <rzaArtikelID>1001</rzaArtikelID>
    <instock>25</instock>
    <stock>30</stock>
    <fields>
      <field fieldnumber="1">Test1</field>
      <field fieldnumber="2"></field>
    </fields>
  </article>
</articles>
```

#### Fieldmapping (RZA → WooCommerce)
| RZA Feld | WooCommerce Feld | Transformation | Beschreibung |
|----------|------------------|----------------|--------------|
| `ordernumber` | `sku` | Direkt | Eindeutige Artikelnummer |
| `instock` | `stock_quantity` | Max(0, instock) | Verfügbare Menge |






### Feature: Stündlicher Import von Bestellungen (WooCommerce → RZA)
- **Zweck**: Übertragung von Bestellungen und Kunden von WooCommerce zu RZA
- **Format**: WooCommerce Daten → RZA XML Import
- **Häufigkeit**: Stündlich

Beim Import von WooCommerce werden alle Bestellungen, die in WooCommerce erstellt wurden,
in RZA importiert. Die Kunden werden im XML IMMER neu angelegt.
Das RZA System kann per E-Mail und Adresse auf bestehende Kunden mappen.

#### Transaktion-XML-Struktur
```xml
<rza:Transaktion xmlns:rza="https://www.rza.at/XML/Fakt/"
  Erstellungsdatum="28.07.2021 10:00"
  Modus="KOMPLETT"
  Verarbeitung="ECHT">

  <rza:Absender Name="WooCommerce Shop"/>

  <rza:Dokument RefID="1001" AdresseRefID="1" Art="Auftrag"
                Datum="28.02.2022" Waehrung="EUR">
    <rza:Summen EndsummeNetto="100.00"
                EndsummeMWSt="20.00"
                EndsummeBrutto="120.00"/>
    <rza:Zahlungskondition Zieltage="30"/>
    <rza:Positionen>
      <rza:Position LfdNummer="1" Menge="2.00" Preis="50.00"
                    Betrag="100.00" MWSt="20">
        <rza:PositionArtikelinfo ArtikelID="774"
                                Artikelnummer="ARTIKEL-123"/>
        <rza:Positionstext>
          <rza:Textzeile Zeile="1" Text="Produktname"/>
        </rza:Positionstext>
      </rza:Position>
    </rza:Positionen>
  </rza:Dokument>

  <rza:Adresse RefID="1" Zuname="Mustermann" Vorname="Max">
    <rza:Anschrift Strasse="Musterstraße 1" PLZ="1010" Ort="Wien"/>
    <rza:Kontakt Telefon1="0664/1234567"
                 Email="<EMAIL>"/>
    <rza:AbweichendeLieferanschrift>
      <rza:Textzeile Zeile="1" Text="Abweichende Lieferadresse"/>
      <rza:Textzeile Zeile="2" Text="Lieferstraße 1"/>
      <rza:Textzeile Zeile="3" Text="1010 Lieferort"/>
    </rza:AbweichendeLieferanschrift>
  </rza:Adresse>
</rza:Transaktion>
```

#### Dokument-Feldmapping (WooCommerce Bestellung → RZA Dokument)

| WooCommerce Feld | RZA Feld | Transformation | Beschreibung |
|------------------|----------|----------------|--------------|
| `order.id` | `Dokument.RefID` | Direkt | Eindeutige Bestellreferenz |
| `order.date_created` | `Dokument.Datum` | DD.MM.YYYY Format | Bestelldatum |
| `order.total` | `Summen.EndsummeBrutto` | Direkt | Gesamtbetrag inkl. MwSt |
| `order.total_tax` | `Summen.EndsummeMWSt` | Direkt | MwSt-Betrag |
| `order.total - order.total_tax` | `Summen.EndsummeNetto` | Berechnet | Nettobetrag |
| `order.currency` | `Dokument.Waehrung` | Direkt | Währungscode (EUR, USD, etc.) |

#### Positionen-Feldmapping (WooCommerce Artikel → RZA Positionen)

| WooCommerce Feld | RZA Feld | Transformation | Beschreibung |
|------------------|----------|----------------|--------------|
| `item.id` | `Position.PositionInterneInformationen.ID` | Direkt | Interne Positions-ID |
| `item.product_id` | `Position.PositionArtikelinfo.ArtikelID` | Direkt | Produktreferenz |
| `item.sku` | `Position.PositionArtikelinfo.Artikelnummer` | SKU oder product_id | Artikelnummer |
| `item.name` | `Position.Positionstext.Textzeile[0].Text` | Direkt | Produktname |
| `item.quantity` | `Position.Menge` | Direkt | Bestellmenge |
| `item.price` | `Position.Preis` | Direkt | Einzelpreis |
| `item.total` | `Position.Betrag` | Direkt | Zeilensumme |
| `item.taxes[0]` | `Position.MWSt` | Prozentsatz berechnet | MwSt-Satz |

#### Adress-Feldmapping (WooCommerce Kunde → RZA Adresse)

| WooCommerce Feld | RZA Feld | Transformation | Beschreibung |
|------------------|----------|----------------|--------------|
| `billing.last_name` | `Adresse.Zuname` | Direkt | Nachname |
| `billing.first_name` | `Adresse.Vorname` | Direkt | Vorname |
| `billing.company` | `Adresse.Firma` | Direkt | Firmenname (optional) |
| `billing.address_1` | `Anschrift.Strasse` | Direkt | Straßenadresse |
| `billing.address_2` | `Anschrift.Strasse` | Kombiniert mit address_1 | Zusätzliche Adresszeile |
| `billing.postcode` | `Anschrift.PLZ` | Direkt | Postleitzahl |
| `billing.city` | `Anschrift.Ort` | Direkt | Ort |
| `billing.country` | `Anschrift.Land` | ISO → Deutsche Namen | Ländercode-Mapping |
| `billing.email` | `Kontakt.Email` | Direkt | E-Mail-Adresse |
| `billing.phone` | `Kontakt.Telefon1` | Formatiert | Telefonnummer |

#### Lieferadresse-Mapping (Abweichende Lieferanschrift)

Wenn Lieferadresse von Rechnungsadresse abweicht:

| WooCommerce Feld | RZA Feld | Transformation | Beschreibung |
|------------------|----------|----------------|--------------|
| `shipping.first_name + last_name` | `AbweichendeLieferanschrift.Textzeile[0]` | Vollständiger Name | Name kombiniert |
| `shipping.company` | `AbweichendeLieferanschrift.Textzeile[1]` | Direkt | Firma (falls vorhanden) |
| `shipping.address_1` | `AbweichendeLieferanschrift.Textzeile[2]` | Direkt | Straßenadresse |
| `shipping.address_2` | `AbweichendeLieferanschrift.Textzeile[3]` | Direkt | Zusätzliche Adresse |
| `shipping.postcode + city` | `AbweichendeLieferanschrift.Textzeile[4]` | Kombiniert | PLZ und Ort |
| `shipping.country` | `AbweichendeLieferanschrift.Textzeile[5]` | Nur wenn ≠ AT | Land (falls abweichend) |

### Import-Validierung (WooCommerce → RZA)

#### Bestellungs-Validierung
- **Pflichtfelder**: `order.id`, `order.date_created`, `order.total`
- **Währungs-Validierung**: Unterstützte Währungen (EUR, USD, CHF)
- **Summen-Validierung**: Netto + MwSt = Brutto
- **Positions-Validierung**: Mindestens eine Position erforderlich

#### Artikel-Positions-Validierung
- **Pflichtfelder**: `product_id` oder `sku`, `name`, `quantity`, `price`
- **Mengen-Validierung**: Menge > 0
- **Preis-Validierung**: Preis ≥ 0
- **MwSt-Validierung**: Gültiger Steuersatz (0-100%)

#### Adressen-Validierung
- **Pflichtfelder**: `first_name`, `last_name`, `address_1`, `postcode`, `city`
- **E-Mail-Validierung**: Gültiges E-Mail-Format
- **Länder-Validierung**: ISO-Code muss in Mapping-Tabelle existieren
- **PLZ-Validierung**: Länder-spezifische Postleitzahl-Formate




## Fehlerbehandlung

### Export-Fehlerbehandlung
- **Ungültige Artikel**: Werden übersprungen, Fehler protokolliert
- **Fehlende Kategorien**: Artikel wird ohne Kategorie in WooCommerce importiert
- **Preisfehler**: Artikel wird übersprungen

### Import-Fehlerbehandlung
- **Ungültige Bestellungen**: Werden abgelehnt und protokolliert