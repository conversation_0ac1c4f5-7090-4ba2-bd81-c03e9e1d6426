# KLab WooCommerce Connect

This project is a connector to synce the RZA ERP-System with WooCommerce.
- Products from RZA to WooCommerce (always full sync)
- Orders from WooCommerce to RZA (only new ones)
- Customers from WooCommerce to RZA (only new ones)

## Quick Start

### Setup

1. **Configure environment:**
   Edit `.env` file with your WooCommerce and external server credentials:
   ```bash
   # WooCommerce API Configuration
   WOOCOMMERCE_URL=https://your-shop.com
   WOOCOMMERCE_CONSUMER_KEY=your_consumer_key
   WOOCOMMERCE_CONSUMER_SECRET=your_consumer_secret
   
   # External Server Configuration
   EXTERNAL_SERVER_URL=https://your-external-server.com
   EXTERNAL_SERVER_USERNAME=your_username
   EXTERNAL_SERVER_PASSWORD=your_password
   ```

### Development

**Option 1: Local Development**
```bash
npm run dev
```
- Backend: http://localhost:3001
- Frontend: http://localhost:3000

## Project Structure

```
├── backend/                 # Backend TypeScript application
│   ├── infrastructure/      # Database, logging, and core services
│   ├── services/           # WooCommerce API and business logic
│   ├── server/             # Express.js server setup
│   └── sync/               # Synchronization logic
├── frontend/               # Vue.js frontend application
│   ├── src/
│   │   ├── components/     # Vue components
│   │   ├── views/          # Page components
│   │   ├── stores/         # Pinia stores
│   │   └── router/         # Vue Router configuration
```

## API Endpoints

### Health & Status
- `GET /api/health` - System health check
- `GET /api/sync/status` - Current sync status
- `GET /api/sync/history` - Sync history

### Sync Operations
- `POST /api/sync/trigger` - Trigger manual sync
- `GET /api/woocommerce/test` - Test WooCommerce connection

### WooCommerce (Planned)
- `GET /api/woocommerce/products` - Get products
- `GET /api/woocommerce/orders` - Get orders

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `3001` |
| `DATABASE_PATH` | SQLite database path | `./data/database.sqlite` |
| `LOG_LEVEL` | Logging level | `info` |
| `SYNC_INTERVAL_MINUTES` | Auto-sync interval | `30` |
| `ENABLE_AUTO_SYNC` | Enable automatic sync | `true` |

### WooCommerce Configuration
- `WOOCOMMERCE_URL`: Your WooCommerce store URL
- `WOOCOMMERCE_CONSUMER_KEY`: WooCommerce REST API consumer key
- `WOOCOMMERCE_CONSUMER_SECRET`: WooCommerce REST API consumer secret

### External Server Configuration
- `EXTERNAL_SERVER_URL`: External XML server URL
- `EXTERNAL_SERVER_USERNAME`: Authentication username
- `EXTERNAL_SERVER_PASSWORD`: Authentication password

## Development

### Backend Development
```bash
cd backend
npm run dev          # Start with hot reload
npm run build        # Build TypeScript
npm run lint         # Run ESLint
npm test            # Run tests
```

### Frontend Development
```bash
cd frontend
npm run dev          # Start Vite dev server
npm run build        # Build for production
npm run preview      # Preview production build
```

## Monitoring

### Health Checks
- Application health: `GET /api/health`
- Docker health checks included
- Database connectivity monitoring

### Logging
- Structured JSON logging with Winston
- Daily log rotation
- Separate error logs
- Configurable log levels

## Troubleshooting

### Common Issues

1. **Database Connection**: Check file permissions on volume directories
2. **WooCommerce API**: Verify API credentials and URL
3. **Port Conflicts**: Ensure ports 3000/3001 are available
4. **Docker Issues**: Check Docker daemon and compose version

### Debug Mode
Set `LOG_LEVEL=debug` in environment for detailed logging.