<template>
  <div class="relative isolate overflow-hidden">
    <!-- <PERSON>er -->
    <PageHeader title="WooCommerce Exports">
      <template #actions>
        <button
          @click="refreshExports"
          :disabled="loading"
          class="flex items-center gap-x-1 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50"
        >
          <ArrowPathIcon class="-ml-1.5 size-5" :class="{ 'animate-spin': loading }" aria-hidden="true" />
          Refresh
        </button>
        <button
          @click="showCreateExportModal = true"
          class="flex items-center gap-x-1 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
        >
          <PlusIcon class="-ml-1.5 size-5" aria-hidden="true" />
          New Export
        </button>
      </template>
    </PageHeader>

  </div>

  <div class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
    <!-- Error Display -->
    <div v-if="exportStore.error" class="mb-6">
      <div class="rounded-md bg-red-50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <XCircleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="mt-2 text-sm text-red-700">
              <p>{{ exportStore.error }}</p>
            </div>
            <div class="mt-4">
              <button
                type="button"
                @click="exportStore.clearError"
                class="rounded-md bg-red-50 px-2 py-1.5 text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 focus:ring-offset-red-50"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Exports List -->
    <div>
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">Export History</h2>
        <div class="text-sm text-gray-500">
          {{ exportStore.exports.length }} total exports
        </div>
      </div>

      <div v-if="loading && exportStore.exports.length === 0" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading exports...</p>
      </div>

      <div v-else-if="exportStore.exports.length === 0" class="text-center py-12">
        <DocumentArrowDownIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-semibold text-gray-900">No exports yet</h3>
        <p class="mt-1 text-sm text-gray-500">Create your first WooCommerce export to get started.</p>
        <div class="mt-6">
          <button
            @click="showCreateExportModal = true"
            class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
          >
            <PlusIcon class="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
            New Export
          </button>
        </div>
      </div>

      <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
          <li v-for="exportItem in exportStore.exports" :key="exportItem.id">
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <DocumentArrowDownIcon class="h-10 w-10 text-gray-400" />
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      Export {{ formatDate(exportItem.date) }}
                    </p>
                    <div class="ml-2 flex-shrink-0">
                      <span :class="getStatusBadgeClass(exportItem.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ getStatusText(exportItem.status) }}
                      </span>
                    </div>
                  </div>
                  <div class="mt-1 flex items-center text-sm text-gray-500">
                    <time :datetime="exportItem.dateTime">{{ formatDate(exportItem.date) }}</time>
                    <span class="mx-2">•</span>
                    <span>{{ exportItem.ordersCount }} orders</span>
                    <span class="mx-2">•</span>
                    <span>{{ exportItem.totalAmount }} {{ exportItem.currency }}</span>
                    <span class="mx-2">•</span>
                    <span>{{ formatDateRange(exportItem.fromDate, exportItem.toDate) }}</span>
                  </div>
                  <div v-if="exportItem.errorMessage" class="mt-1 text-sm text-red-600">
                    {{ exportItem.errorMessage }}
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  v-if="exportItem.status === 'pending'"
                  @click="triggerExport(exportItem.id)"
                  :disabled="loading"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  <PlayIcon class="-ml-0.5 mr-2 h-4 w-4" aria-hidden="true" />
                  Process
                </button>
                <button
                  v-if="exportItem.status === 'success'"
                  @click="downloadXml(exportItem.id)"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <ArrowDownTrayIcon class="-ml-0.5 mr-2 h-4 w-4" aria-hidden="true" />
                  Download XML
                </button>
                <router-link
                  :to="`/export/${exportItem.id}`"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <EyeIcon class="-ml-0.5 mr-2 h-4 w-4" aria-hidden="true" />
                  View
                </router-link>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Create Export Modal -->
  <CreateExportModal
    :show="showCreateExportModal"
    @close="showCreateExportModal = false"
    @created="handleExportCreated"
  />
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from 'vue'
import {
  ArrowPathIcon,
  PlusIcon,
  DocumentArrowDownIcon,
  XCircleIcon,
  PlayIcon,
  ArrowDownTrayIcon,
  EyeIcon
} from '@heroicons/vue/24/outline'

import PageHeader from '../components/PageHeader.vue'
import CreateExportModal from './CreateExportModal.vue'
import { useExportStore } from './export.store'

const exportStore = useExportStore()
const showCreateExportModal = ref(false)

const loading = computed(() => exportStore.loading)

// Actions
const refreshExports = async () => {
  await exportStore.fetchExports()
}

const triggerExport = async (exportId: string) => {
  try {
    await exportStore.triggerExport(exportId)
    // Refresh the exports list
    await exportStore.fetchExports()
  } catch (error) {
    // Error is handled in the store
  }
}

const downloadXml = async (exportId: string) => {
  try {
    const xmlData = await exportStore.downloadXml(exportId)
    // Create a blob and download
    const blob = new Blob([xmlData], { type: 'application/xml' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `export-${exportId}.xml`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    // Error is handled in the store
  }
}

const handleExportCreated = async () => {
  showCreateExportModal.value = false
  await exportStore.fetchExports()
}

// Utility functions
const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    success: 'bg-green-100 text-green-800',
    error: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: 'Pending',
    processing: 'Processing',
    success: 'Success',
    error: 'Error'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatDateRange = (fromDate: string, toDate: string) => {
  const from = new Date(fromDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  const to = new Date(toDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
  return `${from} - ${to}`
}

// Initialize data
onMounted(() => {
  exportStore.fetchExports()
})
</script>