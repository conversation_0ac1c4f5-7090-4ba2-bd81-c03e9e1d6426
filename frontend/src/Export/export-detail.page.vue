<template>
  <div class="relative isolate overflow-hidden">
    <!-- <PERSON>er -->
    <header class="relative isolate pt-16">
      <div class="absolute inset-0 -z-10 overflow-hidden" aria-hidden="true">
        <div class="absolute left-16 top-full -mt-16 transform-gpu opacity-50 blur-3xl xl:left-1/2 xl:-ml-80">
          <div class="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]" style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)" />
        </div>
        <div class="absolute inset-x-0 bottom-0 h-px bg-gray-900/5" />
      </div>

      <div class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
        <div class="mx-auto flex max-w-2xl items-center justify-between gap-x-8 lg:mx-0 lg:max-w-none">
          <div class="flex items-center gap-x-6">
            <DocumentArrowDownIcon class="size-16 flex-none text-gray-400" />
            <h1>
              <div class="text-sm/6 text-gray-500">Export <span class="text-gray-700">#{{ $route.params.id }}</span></div>
              <div class="mt-1 text-base font-semibold text-gray-900">{{ formatDateRange(exportStore.currentExport?.fromDate, exportStore.currentExport?.toDate) }}</div>
            </h1>
          </div>
          <div class="flex items-center gap-x-4 sm:gap-x-6">
            <router-link to="/export" class="hidden text-sm/6 font-semibold text-gray-900 sm:block">
              ← Back to Exports
            </router-link>
            <button
              v-if="exportStore.currentExport?.status === 'pending'"
              @click="triggerExport"
              :disabled="loading"
              class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
            >
              {{ loading ? 'Processing...' : 'Start Export' }}
            </button>
            <button
              v-if="exportStore.currentExport?.status === 'success'"
              @click="downloadXml"
              class="rounded-md bg-green-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-green-600"
            >
              Download XML
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
      <div v-if="loading && !exportStore.currentExport" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading export details...</p>
      </div>

      <div v-else-if="exportStore.error" class="text-center py-12">
        <XCircleIcon class="mx-auto h-12 w-12 text-red-400" />
        <h3 class="mt-2 text-sm font-semibold text-gray-900">Error loading export</h3>
        <p class="mt-1 text-sm text-gray-500">{{ exportStore.error }}</p>
        <div class="mt-6">
          <button
            @click="refreshExport"
            class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500"
          >
            Try Again
          </button>
        </div>
      </div>

      <div v-else-if="exportStore.currentExport" class="mx-auto grid max-w-2xl grid-cols-1 grid-rows-1 items-start gap-x-8 gap-y-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
        <!-- Export Summary -->
        <div class="lg:col-start-3 lg:row-end-1">
          <h2 class="sr-only">Summary</h2>
          <div class="rounded-lg bg-gray-50 shadow-sm ring-1 ring-gray-900/5">
            <dl class="flex flex-wrap">
              <div class="flex-auto pl-6 pt-6">
                <dt class="text-sm/6 font-semibold text-gray-900">Status</dt>
                <dd class="mt-1">
                  <span :class="getStatusBadgeClass(exportStore.currentExport.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ getStatusText(exportStore.currentExport.status) }}
                  </span>
                </dd>
              </div>
              <div class="flex-none self-end px-6 pt-4">
                <dt class="sr-only">Total Amount</dt>
                <dd class="text-sm text-gray-500">{{ exportStore.currentExport.totalAmount }} {{ exportStore.currentExport.currency }}</dd>
              </div>
              <div class="mt-6 flex w-full flex-none gap-x-4 border-t border-gray-900/5 px-6 pt-6">
                <dt class="flex-none">
                  <span class="sr-only">Export Date</span>
                  <CalendarDaysIcon class="h-6 w-5 text-gray-400" aria-hidden="true" />
                </dt>
                <dd class="text-sm/6 text-gray-500">
                  <time :datetime="exportStore.currentExport.dateTime">
                    {{ formatDate(exportStore.currentExport.date) }}
                  </time>
                </dd>
              </div>
              <div v-if="exportStore.currentExport.processedAt" class="mt-4 flex w-full flex-none gap-x-4 px-6">
                <dt class="flex-none">
                  <span class="sr-only">Processed Date</span>
                  <CheckCircleIcon class="h-6 w-5 text-gray-400" aria-hidden="true" />
                </dt>
                <dd class="text-sm/6 text-gray-500">
                  <time :datetime="exportStore.currentExport.processedDateTime">
                    Processed {{ formatDate(exportStore.currentExport.processedAt) }}
                  </time>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Export Details -->
        <div class="-mx-4 px-4 py-8 shadow-sm ring-1 ring-gray-900/5 sm:mx-0 sm:rounded-lg sm:px-8 sm:pb-14 lg:col-span-2 lg:row-span-2 lg:row-end-2 xl:px-16 xl:pb-20 xl:pt-16">
          <h2 class="text-base font-semibold text-gray-900">Export Details</h2>
          
          <!-- Summary Stats -->
          <div v-if="exportStore.currentExport.summary" class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <ShoppingBagIcon class="h-6 w-6 text-blue-400" aria-hidden="true" />
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">Orders</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ exportStore.currentExport.summary.totalOrders }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <CurrencyDollarIcon class="h-6 w-6 text-green-400" aria-hidden="true" />
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">Total Amount</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ exportStore.currentExport.summary.totalAmount }} {{ exportStore.currentExport.summary.currency }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="exportStore.currentExport.errorMessage" class="mt-6 rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <XCircleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Export Error</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>{{ exportStore.currentExport.errorMessage }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Export Info -->
          <div class="mt-8">
            <h3 class="text-sm font-medium text-gray-900">Export Information</h3>
            <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt class="text-sm font-medium text-gray-500">Date Range</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDateRange(exportStore.currentExport.fromDate, exportStore.currentExport.toDate) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Orders Count</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ exportStore.currentExport.ordersCount }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Export Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(exportStore.currentExport.date) }}</dd>
              </div>
              <div v-if="exportStore.currentExport.processedAt">
                <dt class="text-sm font-medium text-gray-500">Processed Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(exportStore.currentExport.processedAt) }}</dd>
              </div>
            </dl>
          </div>

          <!-- XML Preview -->
          <div v-if="exportStore.currentExport.xmlContent" class="mt-8">
            <h3 class="text-sm font-medium text-gray-900">XML Preview</h3>
            <div class="mt-2 bg-gray-50 rounded-lg p-4 overflow-x-auto">
              <pre class="text-xs text-gray-800 whitespace-pre-wrap">{{ exportStore.currentExport.xmlContent }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  DocumentArrowDownIcon,
  XCircleIcon,
  CalendarDaysIcon,
  CheckCircleIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon
} from '@heroicons/vue/24/outline'

import { useExportStore } from './export.store'

const route = useRoute()
const exportStore = useExportStore()

const loading = computed(() => exportStore.loading)

// Actions
const refreshExport = async () => {
  const exportId = route.params.id as string
  await exportStore.fetchExport(exportId)
}

const triggerExport = async () => {
  const exportId = route.params.id as string
  try {
    await exportStore.triggerExport(exportId)
    await refreshExport()
  } catch (error) {
    // Error is handled in the store
  }
}

const downloadXml = async () => {
  const exportId = route.params.id as string
  try {
    const xmlData = await exportStore.downloadXml(exportId)
    // Create a blob and download
    const blob = new Blob([xmlData], { type: 'application/xml' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `export-${exportId}.xml`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  } catch (error) {
    // Error is handled in the store
  }
}

// Utility functions
const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    success: 'bg-green-100 text-green-800',
    error: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: 'Pending',
    processing: 'Processing',
    success: 'Success',
    error: 'Error'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDateRange = (fromDate?: string, toDate?: string) => {
  if (!fromDate || !toDate) return 'N/A'
  const from = new Date(fromDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
  const to = new Date(toDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })
  return `${from} - ${to}`
}

// Initialize data
onMounted(() => {
  const exportId = route.params.id as string
  exportStore.fetchExport(exportId)
})
</script>
