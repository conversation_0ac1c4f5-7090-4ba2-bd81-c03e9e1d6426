import { defineStore } from 'pinia'
import { ref } from 'vue'
import { exportApi } from '../common/api'

export interface ExportItem {
  id: string
  date: string
  dateTime: string
  status: 'pending' | 'processing' | 'success' | 'error'
  ordersCount: number
  totalAmount: string
  currency: string
  fromDate: string
  toDate: string
  errorMessage?: string
  processedAt?: string
  processedDateTime?: string
  xmlFilePath?: string
}

export interface ExportDetail extends ExportItem {
  orders: ExportedOrder[]
  summary: ExportSummary
  xmlContent?: string
}

export interface ExportedOrder {
  id: string
  orderNumber: string
  customerName: string
  customerEmail: string
  total: number
  status: 'exported' | 'skipped' | 'error'
  orderDate: string
  errorMessage?: string
}

export interface ExportSummary {
  totalOrders: number
  exportedOrders: number
  skippedOrders: number
  errorOrders: number
  totalAmount: number
  currency: string
  dateRange: {
    from: string
    to: string
  }
}

export interface ExportRequest {
  fromDate: string
  toDate: string
  includeProcessed?: boolean
  orderStatuses?: string[]
}

export const useExportStore = defineStore('export', () => {
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  const exports = ref<ExportItem[]>([])
  const currentExport = ref<ExportDetail | null>(null)
  const exporting = ref(false)

  // Actions
  const fetchExports = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await exportApi.getExports()
      exports.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch exports'
      console.error('Export fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchExport = async (id: string) => {
    loading.value = true
    error.value = null

    try {
      const response = await exportApi.getExport(id)
      currentExport.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch export details'
      console.error('Export detail fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const createExport = async (request: ExportRequest) => {
    exporting.value = true
    error.value = null

    try {
      const response = await exportApi.createExport(request)
      const newExport = response.data
      exports.value.unshift(newExport)
      return newExport
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create export'
      console.error('Export creation error:', err)
      throw err
    } finally {
      exporting.value = false
    }
  }

  const triggerExport = async (exportId: string) => {
    loading.value = true
    error.value = null

    try {
      const response = await exportApi.triggerExport(exportId)
      // Update the export status
      const exportIndex = exports.value.findIndex(exp => exp.id === exportId)
      if (exportIndex !== -1) {
        exports.value[exportIndex] = { ...exports.value[exportIndex], ...response.data }
      }
      return response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to trigger export'
      console.error('Export trigger error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const downloadXml = async (exportId: string) => {
    try {
      const response = await exportApi.downloadXml(exportId)
      return response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to download XML'
      console.error('XML download error:', err)
      throw err
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    loading,
    error,
    exports,
    currentExport,
    exporting,

    // Actions
    fetchExports,
    fetchExport,
    createExport,
    triggerExport,
    downloadXml,
    clearError
  }
})