<template>
  <TransitionRoot as="template" :show="show">
    <Dialog as="div" class="relative z-10" @close="$emit('close')">
      <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200" leave-from="opacity-100 translate-y-0 sm:scale-100" leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div>
                <div class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                  <DocumentArrowDownIcon class="h-6 w-6 text-green-600" aria-hidden="true" />
                </div>
                <div class="mt-3 text-center sm:mt-5">
                  <DialogTitle as="h3" class="text-base font-semibold leading-6 text-gray-900">
                    Create New Export
                  </DialogTitle>
                  <div class="mt-2">
                    <p class="text-sm text-gray-500">
                      Export WooCommerce orders to RZA XML format for the specified date range.
                    </p>
                  </div>
                </div>
              </div>

              <form @submit.prevent="handleSubmit" class="mt-6">
                <div class="space-y-4">
                  <!-- Date Range -->
                  <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <label for="fromDate" class="block text-sm font-medium leading-6 text-gray-900">From Date</label>
                      <div class="mt-2">
                        <input
                          id="fromDate"
                          v-model="form.fromDate"
                          type="date"
                          required
                          class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </div>
                    </div>
                    <div>
                      <label for="toDate" class="block text-sm font-medium leading-6 text-gray-900">To Date</label>
                      <div class="mt-2">
                        <input
                          id="toDate"
                          v-model="form.toDate"
                          type="date"
                          required
                          class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- Order Statuses -->
                  <div>
                    <label class="block text-sm font-medium leading-6 text-gray-900">Order Statuses</label>
                    <div class="mt-2 space-y-2">
                      <div v-for="status in orderStatuses" :key="status.value" class="flex items-center">
                        <input
                          :id="`status-${status.value}`"
                          v-model="form.orderStatuses"
                          :value="status.value"
                          type="checkbox"
                          class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                        />
                        <label :for="`status-${status.value}`" class="ml-3 text-sm leading-6 text-gray-900">
                          {{ status.label }}
                        </label>
                      </div>
                    </div>
                  </div>

                  <!-- Include Processed -->
                  <div class="flex items-center">
                    <input
                      id="includeProcessed"
                      v-model="form.includeProcessed"
                      type="checkbox"
                      class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600"
                    />
                    <label for="includeProcessed" class="ml-3 text-sm leading-6 text-gray-900">
                      Include previously processed orders
                    </label>
                  </div>
                </div>

                <!-- Error Display -->
                <div v-if="error" class="mt-4 rounded-md bg-red-50 p-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <XCircleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-red-800">Error</h3>
                      <div class="mt-2 text-sm text-red-700">
                        <p>{{ error }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="submit"
                    :disabled="loading"
                    class="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50 sm:col-start-2"
                  >
                    {{ loading ? 'Creating...' : 'Create Export' }}
                  </button>
                  <button
                    type="button"
                    @click="$emit('close')"
                    class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { DocumentArrowDownIcon, XCircleIcon } from '@heroicons/vue/24/outline'
import { useExportStore } from './export.store'
import type { ExportRequest } from './export.store'

interface Props {
  show: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'close': []
  'created': []
}>()

const exportStore = useExportStore()

const loading = ref(false)
const error = ref<string | null>(null)

const form = ref<ExportRequest>({
  fromDate: '',
  toDate: '',
  includeProcessed: false,
  orderStatuses: ['completed', 'processing']
})

const orderStatuses = [
  { value: 'pending', label: 'Pending Payment' },
  { value: 'processing', label: 'Processing' },
  { value: 'on-hold', label: 'On Hold' },
  { value: 'completed', label: 'Completed' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'refunded', label: 'Refunded' },
  { value: 'failed', label: 'Failed' }
]

// Set default dates (last 30 days)
const setDefaultDates = () => {
  const today = new Date()
  const thirtyDaysAgo = new Date(today)
  thirtyDaysAgo.setDate(today.getDate() - 30)
  
  form.value.toDate = today.toISOString().split('T')[0]
  form.value.fromDate = thirtyDaysAgo.toISOString().split('T')[0]
}

const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    await exportStore.createExport(form.value)
    emit('created')
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to create export'
  } finally {
    loading.value = false
  }
}

// Reset form when modal opens
watch(() => props.show, (show) => {
  if (show) {
    setDefaultDates()
    error.value = null
  }
})

// Initialize default dates
setDefaultDates()
</script>
