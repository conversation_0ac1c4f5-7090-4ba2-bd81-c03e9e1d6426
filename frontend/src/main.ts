import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'
import './style.css'

// Import pages
import DashboardPage from './Dashboard/dashboard.page.vue'
import ImportPage from './Import/import.page.vue'
import ImportDetailPage from './Import/import-detail.page.vue'
import ExportPage from './Export/export.page.vue'
import ExportDetailPage from './Export/export-detail.page.vue'

// Router configuration
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Dashboard',
      component: DashboardPage
    },
    {
      path: '/import',
      name: 'Import',
      component: ImportPage
    },
    {
      path: '/import/:id',
      name: 'ImportDetail',
      component: ImportDetailPage
    },
    {
      path: '/export',
      name: 'Export',
      component: ExportPage
    },
    {
      path: '/export/:id',
      name: 'ExportDetail',
      component: ExportDetailPage
    }
  ]
})

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
