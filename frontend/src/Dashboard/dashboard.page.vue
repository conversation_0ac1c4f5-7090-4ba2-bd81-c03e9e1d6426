<template>
  <div class="relative isolate overflow-hidden">
    <!-- <PERSON>er -->
    <PageHeader title="Dashboard">
      <template #actions>
        <button
          @click="refreshData"
          :disabled="loading"
          class="flex items-center gap-x-1 rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
        >
          <ArrowPathIcon class="-ml-1.5 size-5" :class="{ 'animate-spin': loading }" aria-hidden="true" />
          Refresh
        </button>
      </template>
    </PageHeader>

    <!-- Stats -->
    <div class="border-b border-b-gray-900/10 lg:border-t lg:border-t-gray-900/5">
      <Stats :stats="dashboardStore.stats" />
    </div>

    <!-- Background decoration -->
    <div class="absolute left-0 top-full -z-10 mt-96 origin-top-left translate-y-40 -rotate-90 transform-gpu opacity-20 blur-3xl sm:left-1/2 sm:-ml-96 sm:-mt-10 sm:translate-y-0 sm:rotate-0 sm:transform-gpu sm:opacity-50" aria-hidden="true">
      <div class="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]" style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)" />
    </div>
  </div>

  <div class="space-y-16 py-16 xl:space-y-20">
    <!-- Recent Imports -->
    <div>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <h2 class="mx-auto max-w-2xl text-base font-semibold text-gray-900 lg:mx-0 lg:max-w-none">Recent RZA Imports</h2>
          <router-link to="/import" class="text-sm/6 font-semibold text-indigo-600 hover:text-indigo-500">
            View all<span class="sr-only">, imports</span>
          </router-link>
        </div>
      </div>

      <DataTable
        v-if="dashboardStore.recentImports.length > 0"
        :data="recentImportsTableData"
        :columns="importColumns"
        :actions="importActions"
        @action="handleImportAction"
      />

      <div v-else class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mt-6 text-center py-12">
          <DocumentArrowUpIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-semibold text-gray-900">No imports yet</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by uploading an RZA export file.</p>
          <div class="mt-6">
            <router-link
              to="/import"
              class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              <DocumentArrowUpIcon class="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
              Upload Import
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Exports -->
    <div>
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
          <h2 class="mx-auto max-w-2xl text-base font-semibold text-gray-900 lg:mx-0 lg:max-w-none">Recent WooCommerce Exports</h2>
          <router-link to="/export" class="text-sm/6 font-semibold text-indigo-600 hover:text-indigo-500">
            View all<span class="sr-only">, exports</span>
          </router-link>
        </div>
      </div>

      <DataTable
        v-if="dashboardStore.recentExports.length > 0"
        :data="recentExportsTableData"
        :columns="exportColumns"
        :actions="exportActions"
        @action="handleExportAction"
      />

      <div v-else class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="mt-6 text-center py-12">
          <DocumentArrowDownIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-semibold text-gray-900">No exports yet</h3>
          <p class="mt-1 text-sm text-gray-500">Export WooCommerce orders to RZA format.</p>
          <div class="mt-6">
            <router-link
              to="/export"
              class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              <DocumentArrowDownIcon class="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
              Create Export
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- System Health -->
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:mx-0 lg:max-w-none">
        <h2 class="text-base font-semibold text-gray-900">System Health</h2>
        <div class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Database Status -->
          <div class="overflow-hidden rounded-lg bg-white shadow">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <CircleStackIcon
                    :class="[
                      'h-6 w-6',
                      dashboardStore.systemHealth?.database === 'connected' ? 'text-green-500' : 'text-red-500'
                    ]"
                    aria-hidden="true"
                  />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Database</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ dashboardStore.systemHealth?.database === 'connected' ? 'Connected' : 'Disconnected' }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- WooCommerce Status -->
          <div class="overflow-hidden rounded-lg bg-white shadow">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <GlobeAltIcon
                    :class="[
                      'h-6 w-6',
                      dashboardStore.systemHealth?.woocommerce ? 'text-green-500' : 'text-red-500'
                    ]"
                    aria-hidden="true"
                  />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">WooCommerce</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ dashboardStore.systemHealth?.woocommerce ? 'Connected' : 'Disconnected' }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- System Uptime -->
          <div class="overflow-hidden rounded-lg bg-white shadow">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <ClockIcon class="h-6 w-6 text-blue-500" aria-hidden="true" />
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">Uptime</dt>
                    <dd class="text-lg font-medium text-gray-900">
                      {{ formatUptime(dashboardStore.systemHealth?.uptime || 0) }}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  ArrowPathIcon,
  DocumentArrowUpIcon,
  DocumentArrowDownIcon,
  CircleStackIcon,
  GlobeAltIcon,
  ClockIcon
} from '@heroicons/vue/24/outline'
import { ArrowUpCircleIcon, ArrowDownCircleIcon } from '@heroicons/vue/20/solid'

import PageHeader from '../components/PageHeader.vue'
import Stats from '../components/stats.vue'
import DataTable from '../components/DataTable.vue'
import { useDashboardStore } from './dashboard.store'
import type { TableItem } from '../components/DataTable.vue'

const router = useRouter()
const dashboardStore = useDashboardStore()

const loading = computed(() => dashboardStore.loading)

// Table configurations
const importColumns = [
  { key: 'filename', label: 'Filename' },
  { key: 'date', label: 'Date' },
  { key: 'status', label: 'Status' }
]

const exportColumns = [
  { key: 'date', label: 'Date' },
  { key: 'ordersCount', label: 'Orders' },
  { key: 'totalAmount', label: 'Total Amount' }
]

const importActions = [
  { key: 'view', label: 'View' }
]

const exportActions = [
  { key: 'view', label: 'View' },
  { key: 'download', label: 'Download XML' }
]

// Transform data for tables
const recentImportsTableData = computed<TableItem[]>(() => {
  return dashboardStore.recentImports.map(item => ({
    id: item.id,
    filename: item.filename,
    date: item.date,
    status: item.status,
    subtitle: `${item.productsCount} products, ${item.customersCount} customers`,
    icon: ArrowUpCircleIcon
  }))
})

const recentExportsTableData = computed<TableItem[]>(() => {
  return dashboardStore.recentExports.map(item => ({
    id: item.id,
    date: item.date,
    ordersCount: item.ordersCount,
    totalAmount: item.totalAmount,
    status: item.status,
    icon: ArrowDownCircleIcon
  }))
})

// Actions
const refreshData = async () => {
  await dashboardStore.fetchDashboardData()
}

const handleImportAction = (actionKey: string, item: TableItem) => {
  if (actionKey === 'view') {
    router.push(`/import/${item.id}`)
  }
}

const handleExportAction = (actionKey: string, item: TableItem) => {
  if (actionKey === 'view') {
    router.push(`/export/${item.id}`)
  } else if (actionKey === 'download') {
    // Handle XML download
    window.open(`/api/exports/${item.id}/xml`, '_blank')
  }
}

const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`
  } else {
    return `${minutes}m`
  }
}

// Initialize data
onMounted(() => {
  dashboardStore.fetchDashboardData()
})
</script>