import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { dashboardApi, healthApi } from '../common/api'

export interface Stat {
  name: string
  value: string | number
  change?: string
  changeType?: 'positive' | 'negative'
}

export interface RecentImport {
  id: string
  filename: string
  date: string
  dateTime: string
  status: 'success' | 'error' | 'processing'
  productsCount: number
  customersCount: number
}

export interface RecentExport {
  id: string
  date: string
  dateTime: string
  status: 'success' | 'error' | 'processing'
  ordersCount: number
  totalAmount: string
}

export interface SystemHealth {
  status: 'ok' | 'error'
  database: 'connected' | 'disconnected'
  woocommerce: boolean
  uptime: number
  memory: {
    rss: number
    heapTotal: number
    heapUsed: number
    external: number
  }
}

export const useDashboardStore = defineStore('dashboard', () => {
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  const recentImports = ref<RecentImport[]>([])
  const recentExports = ref<RecentExport[]>([])
  const systemHealth = ref<SystemHealth | null>(null)

  // Computed
  const stats = computed<Stat[]>(() => {
    const totalImports = recentImports.value.length
    const totalExports = recentExports.value.length
    const successfulImports = recentImports.value.filter(i => i.status === 'success').length
    const successfulExports = recentExports.value.filter(e => e.status === 'success').length

    return [
      {
        name: 'Total Imports',
        value: totalImports.toString(),
        change: successfulImports > 0 ? `${Math.round((successfulImports / totalImports) * 100)}% success` : undefined,
        changeType: successfulImports === totalImports ? 'positive' : 'negative'
      },
      {
        name: 'Total Exports',
        value: totalExports.toString(),
        change: successfulExports > 0 ? `${Math.round((successfulExports / totalExports) * 100)}% success` : undefined,
        changeType: successfulExports === totalExports ? 'positive' : 'negative'
      },
      {
        name: 'Database Status',
        value: systemHealth.value?.database === 'connected' ? 'Connected' : 'Disconnected',
        changeType: systemHealth.value?.database === 'connected' ? 'positive' : 'negative'
      },
      {
        name: 'WooCommerce Status',
        value: systemHealth.value?.woocommerce ? 'Connected' : 'Disconnected',
        changeType: systemHealth.value?.woocommerce ? 'positive' : 'negative'
      }
    ]
  })

  const isHealthy = computed(() => {
    return systemHealth.value?.status === 'ok' &&
           systemHealth.value?.database === 'connected' &&
           systemHealth.value?.woocommerce === true
  })

  // Actions
  const fetchDashboardData = async () => {
    loading.value = true
    error.value = null

    try {
      const [healthResponse, importsResponse, exportsResponse] = await Promise.all([
        healthApi.getHealth(),
        dashboardApi.getRecentImports().catch(() => ({ data: [] })),
        dashboardApi.getRecentExports().catch(() => ({ data: [] }))
      ])

      systemHealth.value = healthResponse.data
      recentImports.value = importsResponse.data || []
      recentExports.value = exportsResponse.data || []
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch dashboard data'
      console.error('Dashboard fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const refreshHealth = async () => {
    try {
      const response = await healthApi.getHealth()
      systemHealth.value = response.data
    } catch (err) {
      console.error('Health check failed:', err)
    }
  }

  return {
    // State
    loading,
    error,
    recentImports,
    recentExports,
    systemHealth,

    // Computed
    stats,
    isHealthy,

    // Actions
    fetchDashboardData,
    refreshHealth
  }
})