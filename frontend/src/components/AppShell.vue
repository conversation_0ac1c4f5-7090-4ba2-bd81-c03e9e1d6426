<template>
  <header class="absolute inset-x-0 top-0 z-50 flex h-16 border-b border-gray-900/10">
    <div class="mx-auto flex w-full max-w-7xl items-center justify-between px-4 sm:px-6 lg:px-8">
      <div class="flex flex-1 items-center gap-x-6">
        <button type="button" class="-m-3 p-3 md:hidden" @click="mobileMenuOpen = true">
          <span class="sr-only">Open main menu</span>
          <Bars3Icon class="size-5 text-gray-900" aria-hidden="true" />
        </button>
        <div class="flex items-center gap-x-3">
          <img class="h-8 w-auto" src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600" alt="KLab WooCommerce Connect" />
          <h1 class="text-lg font-semibold text-gray-900">KLab WooCommerce Connect</h1>
        </div>
      </div>
      <nav class="hidden md:flex md:gap-x-11 md:text-sm/6 md:font-semibold md:text-gray-700">
        <router-link 
          v-for="item in navigation" 
          :key="item.name" 
          :to="item.to"
          :class="[
            'hover:text-gray-900 transition-colors',
            $route.name === item.name ? 'text-indigo-600' : 'text-gray-700'
          ]"
        >
          {{ item.name }}
        </router-link>
      </nav>
      <div class="flex flex-1 items-center justify-end gap-x-8">
        <div class="flex items-center gap-x-2">
          <div 
            :class="[
              'h-2 w-2 rounded-full',
              connectionStatus.database ? 'bg-green-500' : 'bg-red-500'
            ]"
          />
          <span class="text-xs text-gray-500">DB</span>
        </div>
        <div class="flex items-center gap-x-2">
          <div 
            :class="[
              'h-2 w-2 rounded-full',
              connectionStatus.woocommerce ? 'bg-green-500' : 'bg-red-500'
            ]"
          />
          <span class="text-xs text-gray-500">WooCommerce</span>
        </div>
      </div>
    </div>
    <Dialog class="lg:hidden" @close="mobileMenuOpen = false" :open="mobileMenuOpen">
      <div class="fixed inset-0 z-50" />
      <DialogPanel class="fixed inset-y-0 left-0 z-50 w-full overflow-y-auto bg-white px-4 pb-6 sm:max-w-sm sm:px-6 sm:ring-1 sm:ring-gray-900/10">
        <div class="-ml-0.5 flex h-16 items-center gap-x-6">
          <button type="button" class="-m-2.5 p-2.5 text-gray-700" @click="mobileMenuOpen = false">
            <span class="sr-only">Close menu</span>
            <XMarkIcon class="size-6" aria-hidden="true" />
          </button>
          <div class="-ml-0.5">
            <div class="flex items-center gap-x-3">
              <img class="h-8 w-auto" src="https://tailwindcss.com/plus-assets/img/logos/mark.svg?color=indigo&shade=600" alt="KLab WooCommerce Connect" />
              <span class="text-sm font-semibold text-gray-900">KLab WooCommerce Connect</span>
            </div>
          </div>
        </div>
        <div class="mt-2 space-y-2">
          <router-link 
            v-for="item in navigation" 
            :key="item.name" 
            :to="item.to"
            class="-mx-3 block rounded-lg px-3 py-2 text-base/7 font-semibold text-gray-900 hover:bg-gray-50"
            @click="mobileMenuOpen = false"
          >
            {{ item.name }}
          </router-link>
        </div>
      </DialogPanel>
    </Dialog>
  </header>

  <main class="pt-16">
    <slot />
  </main>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Dialog, DialogPanel } from '@headlessui/vue'
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline'
import { healthApi } from '../common/api'

const mobileMenuOpen = ref(false)

const navigation = [
  { name: 'Dashboard', to: '/' },
  { name: 'Import', to: '/import' },
  { name: 'Export', to: '/export' }
]

const connectionStatus = ref({
  database: false,
  woocommerce: false
})

const checkConnectionStatus = async () => {
  try {
    const [healthResponse, wooResponse] = await Promise.all([
      healthApi.getHealth(),
      healthApi.getWooCommerceStatus()
    ])
    
    connectionStatus.value.database = healthResponse.data.database === 'connected'
    connectionStatus.value.woocommerce = wooResponse.data.connected === true
  } catch (error) {
    console.error('Failed to check connection status:', error)
    connectionStatus.value.database = false
    connectionStatus.value.woocommerce = false
  }
}

onMounted(() => {
  checkConnectionStatus()
  // Check status every 30 seconds
  setInterval(checkConnectionStatus, 30000)
})
</script>
