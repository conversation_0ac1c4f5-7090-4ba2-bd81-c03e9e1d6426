<template>
  <div class="mt-6 overflow-hidden border-t border-gray-100">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:mx-0 lg:max-w-none">
        <table class="w-full text-left">
          <thead class="sr-only">
            <tr>
              <th v-for="column in columns" :key="column.key">{{ column.label }}</th>
              <th v-if="actions">Actions</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(group, groupIndex) in groupedData" :key="groupIndex">
              <tr v-if="group.date" class="text-sm/6 text-gray-900">
                <th scope="colgroup" :colspan="columns.length + (actions ? 1 : 0)" class="relative isolate py-2 font-semibold">
                  <time :datetime="group.dateTime">{{ group.date }}</time>
                  <div class="absolute inset-y-0 right-full -z-10 w-screen border-b border-gray-200 bg-gray-50" />
                  <div class="absolute inset-y-0 left-0 -z-10 w-screen border-b border-gray-200 bg-gray-50" />
                </th>
              </tr>
              <tr v-for="item in group.items" :key="item.id">
                <td 
                  v-for="column in columns" 
                  :key="column.key"
                  :class="[
                    'relative py-5 pr-6',
                    column.key === columns[0].key ? '' : 'hidden sm:table-cell'
                  ]"
                >
                  <slot :name="`cell-${column.key}`" :item="item" :value="getNestedValue(item, column.key)">
                    <div v-if="column.key === columns[0].key" class="flex gap-x-6">
                      <component 
                        v-if="item.icon" 
                        :is="item.icon" 
                        class="hidden h-6 w-5 flex-none text-gray-400 sm:block" 
                        aria-hidden="true" 
                      />
                      <div class="flex-auto">
                        <div class="text-sm/6 font-medium text-gray-900">
                          {{ getNestedValue(item, column.key) }}
                        </div>
                        <div v-if="item.subtitle" class="mt-1 text-xs/5 text-gray-500">
                          {{ item.subtitle }}
                        </div>
                      </div>
                    </div>
                    <div v-else>
                      <div class="text-sm/6 text-gray-900">{{ getNestedValue(item, column.key) }}</div>
                      <div v-if="item.description && column.key === columns[1]?.key" class="mt-1 text-xs/5 text-gray-500">
                        {{ item.description }}
                      </div>
                    </div>
                  </slot>
                  <div class="absolute bottom-0 right-full h-px w-screen bg-gray-100" />
                  <div class="absolute bottom-0 left-0 h-px w-screen bg-gray-100" />
                </td>
                <td v-if="actions" class="py-5 text-right">
                  <slot name="actions" :item="item">
                    <div class="flex justify-end">
                      <button 
                        v-for="action in actions" 
                        :key="action.label"
                        @click="$emit('action', action.key, item)"
                        class="text-sm/6 font-medium text-indigo-600 hover:text-indigo-500 ml-4"
                      >
                        {{ action.label }}
                      </button>
                    </div>
                  </slot>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface Column {
  key: string
  label: string
}

export interface Action {
  key: string
  label: string
}

export interface TableItem {
  id: string | number
  icon?: any
  subtitle?: string
  description?: string
  [key: string]: any
}

export interface GroupedItem {
  date?: string
  dateTime?: string
  items: TableItem[]
}

interface Props {
  data: TableItem[] | GroupedItem[]
  columns: Column[]
  actions?: Action[]
  groupByDate?: boolean
}

const props = defineProps<Props>()

defineEmits<{
  action: [actionKey: string, item: TableItem]
}>()

const groupedData = computed(() => {
  if (props.groupByDate && Array.isArray(props.data) && props.data.length > 0) {
    // Check if data is already grouped
    if ('items' in props.data[0]) {
      return props.data as GroupedItem[]
    }
    // Group by date if needed - for now just return as single group
    return [{ items: props.data as TableItem[] }]
  }
  
  return [{ items: props.data as TableItem[] }]
})

const getNestedValue = (obj: any, path: string) => {
  return path.split('.').reduce((current, key) => current?.[key], obj)
}
</script>
