<template>
  <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-2xl lg:mx-0 lg:max-w-none">
      <div class="rounded-lg border border-dashed border-gray-300 p-6">
        <div class="text-center">
          <DocumentArrowUpIcon class="mx-auto h-12 w-12 text-gray-400" />
          <div class="mt-4">
            <label for="file-upload" class="cursor-pointer">
              <span class="mt-2 block text-sm font-semibold text-gray-900">
                {{ title || 'Upload a file' }}
              </span>
              <span class="mt-1 block text-sm text-gray-600">
                {{ description || 'Drag and drop or click to select' }}
              </span>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                :accept="accept"
                :multiple="multiple"
                class="sr-only"
                @change="handleFileSelect"
                @drop="handleDrop"
                @dragover="handleDragOver"
                @dragenter="handleDragEnter"
                @dragleave="handleDragLeave"
              />
            </label>
          </div>
          <div v-if="acceptedTypes" class="mt-2">
            <p class="text-xs text-gray-500">{{ acceptedTypes }}</p>
          </div>
        </div>
      </div>
      
      <!-- File list -->
      <div v-if="files.length > 0" class="mt-4">
        <h3 class="text-sm font-medium text-gray-900">Selected files</h3>
        <ul class="mt-2 divide-y divide-gray-200 rounded-md border border-gray-200">
          <li v-for="(file, index) in files" :key="index" class="flex items-center justify-between py-3 pl-3 pr-4 text-sm">
            <div class="flex w-0 flex-1 items-center">
              <DocumentIcon class="h-5 w-5 flex-shrink-0 text-gray-400" />
              <span class="ml-2 w-0 flex-1 truncate">{{ file.name }}</span>
              <span class="ml-2 flex-shrink-0 text-gray-400">{{ formatFileSize(file.size) }}</span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <button
                type="button"
                @click="removeFile(index)"
                class="font-medium text-red-600 hover:text-red-500"
              >
                Remove
              </button>
            </div>
          </li>
        </ul>
        
        <div class="mt-4 flex justify-end gap-x-3">
          <button
            type="button"
            @click="clearFiles"
            class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
          >
            Clear all
          </button>
          <button
            type="button"
            @click="uploadFiles"
            :disabled="uploading"
            class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
          >
            {{ uploading ? 'Uploading...' : 'Upload' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DocumentArrowUpIcon, DocumentIcon } from '@heroicons/vue/24/outline'

interface Props {
  title?: string
  description?: string
  accept?: string
  acceptedTypes?: string
  multiple?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false
})

const emit = defineEmits<{
  'files-selected': [files: File[]]
  'upload': [files: File[]]
}>()

const files = ref<File[]>([])
const uploading = ref(false)

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files) {
    const newFiles = Array.from(target.files)
    if (props.multiple) {
      files.value.push(...newFiles)
    } else {
      files.value = newFiles
    }
    emit('files-selected', files.value)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer?.files) {
    const newFiles = Array.from(event.dataTransfer.files)
    if (props.multiple) {
      files.value.push(...newFiles)
    } else {
      files.value = newFiles
    }
    emit('files-selected', files.value)
  }
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
}

const removeFile = (index: number) => {
  files.value.splice(index, 1)
  emit('files-selected', files.value)
}

const clearFiles = () => {
  files.value = []
  emit('files-selected', files.value)
}

const uploadFiles = () => {
  if (files.value.length > 0) {
    uploading.value = true
    emit('upload', files.value)
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Expose method to reset uploading state
defineExpose({
  resetUploading: () => {
    uploading.value = false
  }
})
</script>
