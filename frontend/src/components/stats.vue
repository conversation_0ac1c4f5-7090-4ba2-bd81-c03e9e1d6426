<template>
  <dl :class="[
    'mx-auto grid gap-px bg-gray-900/5',
    gridCols
  ]">
    <div
      v-for="stat in stats"
      :key="stat.name"
      class="flex flex-wrap items-baseline justify-between gap-x-4 gap-y-2 bg-white px-4 py-10 sm:px-6 xl:px-8"
    >
      <dt class="text-sm/6 font-medium text-gray-500">{{ stat.name }}</dt>
      <dd
        v-if="stat.change"
        :class="[
          stat.changeType === 'negative' ? 'text-rose-600' : 'text-gray-700',
          'text-xs font-medium'
        ]"
      >
        {{ stat.change }}
      </dd>
      <dd class="w-full flex-none text-3xl/10 font-medium tracking-tight text-gray-900">
        {{ stat.value }}
      </dd>
    </div>
  </dl>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface Stat {
  name: string
  value: string | number
  change?: string
  changeType?: 'positive' | 'negative'
}

interface Props {
  stats: Stat[]
  columns?: 1 | 2 | 3 | 4
}

const props = withDefaults(defineProps<Props>(), {
  columns: 4
})

const gridCols = computed(() => {
  const colsMap: Record<number, string> = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 sm:grid-cols-2',
    3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4'
  }
  return colsMap[props.columns]
})
</script>