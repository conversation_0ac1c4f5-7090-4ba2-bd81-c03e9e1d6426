<template>
  <header class="pb-4 pt-6 sm:pb-6">
    <div class="mx-auto flex max-w-7xl flex-wrap items-center gap-6 px-4 sm:flex-nowrap sm:px-6 lg:px-8">
      <h1 class="text-base/7 font-semibold text-gray-900">{{ title }}</h1>
      <div 
        v-if="tabs && tabs.length > 0"
        class="order-last flex w-full gap-x-8 text-sm/6 font-semibold sm:order-none sm:w-auto sm:border-l sm:border-gray-200 sm:pl-6 sm:text-sm/7"
      >
        <button
          v-for="tab in tabs"
          :key="tab.name"
          @click="$emit('tab-change', tab.value)"
          :class="[
            'hover:text-gray-900 transition-colors',
            tab.current ? 'text-indigo-600' : 'text-gray-700'
          ]"
        >
          {{ tab.name }}
        </button>
      </div>
      <div v-if="$slots.actions" class="ml-auto">
        <slot name="actions" />
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
export interface Tab {
  name: string
  value: string
  current: boolean
}

interface Props {
  title: string
  tabs?: Tab[]
}

defineProps<Props>()
defineEmits<{
  'tab-change': [value: string]
}>()
</script>
