import axios from 'axios'

// API base configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001'

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error)
    return Promise.reject(error)
  }
)

// Health and Status API
export const healthApi = {
  getHealth: () => api.get('/health'),
  getWooCommerceStatus: () => api.get('/woocommerce')
}

// Dashboard API
export const dashboardApi = {
  getStats: () => api.get('/dashboard/stats'),
  getRecentImports: () => api.get('/dashboard/recent-imports'),
  getRecentExports: () => api.get('/dashboard/recent-exports')
}

// Import API
export const importApi = {
  getImports: () => api.get('/imports'),
  getImport: (id: string) => api.get(`/imports/${id}`),
  uploadFile: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/imports/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  triggerImport: (fileId: string) => api.post(`/imports/${fileId}/trigger`)
}

// Export API
export const exportApi = {
  getExports: () => api.get('/exports'),
  getExport: (id: string) => api.get(`/exports/${id}`),
  createExport: (request: any) => api.post('/exports', request),
  triggerExport: (exportId: string) => api.post(`/exports/${exportId}/trigger`),
  downloadXml: (id: string) => api.get(`/exports/${id}/xml`),
  getExportXml: (id: string) => api.get(`/exports/${id}/xml`)
}

export default api