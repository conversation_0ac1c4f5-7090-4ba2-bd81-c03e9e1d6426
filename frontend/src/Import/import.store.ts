import { defineStore } from 'pinia'
import { ref } from 'vue'
import { importApi } from '../common/api'

export interface ImportItem {
  id: string
  filename: string
  originalFilename: string
  uploadDate: string
  uploadDateTime: string
  status: 'pending' | 'processing' | 'success' | 'error'
  fileSize: number
  productsCount?: number
  customersCount?: number
  categoriesCount?: number
  errorMessage?: string
  processedAt?: string
  processedDateTime?: string
}

export interface ImportDetail extends ImportItem {
  products: ImportedProduct[]
  customers: ImportedCustomer[]
  categories: ImportedCategory[]
  summary: ImportSummary
}

export interface ImportedProduct {
  id: string
  name: string
  sku: string
  price: number
  status: 'imported' | 'updated' | 'skipped' | 'error'
  woocommerceId?: number
  errorMessage?: string
}

export interface ImportedCustomer {
  id: string
  name: string
  email: string
  status: 'imported' | 'updated' | 'skipped' | 'error'
  woocommerceId?: number
  errorMessage?: string
}

export interface ImportedCategory {
  id: string
  name: string
  status: 'imported' | 'updated' | 'skipped' | 'error'
  woocommerceId?: number
  errorMessage?: string
}

export interface ImportSummary {
  totalProducts: number
  importedProducts: number
  updatedProducts: number
  skippedProducts: number
  errorProducts: number
  totalCustomers: number
  importedCustomers: number
  updatedCustomers: number
  skippedCustomers: number
  errorCustomers: number
  totalCategories: number
  importedCategories: number
  updatedCategories: number
  skippedCategories: number
  errorCategories: number
}

export const useImportStore = defineStore('import', () => {
  // State
  const loading = ref(false)
  const error = ref<string | null>(null)
  const imports = ref<ImportItem[]>([])
  const currentImport = ref<ImportDetail | null>(null)
  const uploading = ref(false)

  // Actions
  const fetchImports = async () => {
    loading.value = true
    error.value = null

    try {
      const response = await importApi.getImports()
      imports.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch imports'
      console.error('Import fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const fetchImport = async (id: string) => {
    loading.value = true
    error.value = null

    try {
      const response = await importApi.getImport(id)
      currentImport.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch import details'
      console.error('Import detail fetch error:', err)
    } finally {
      loading.value = false
    }
  }

  const uploadFile = async (file: File) => {
    uploading.value = true
    error.value = null

    try {
      const response = await importApi.uploadFile(file)
      const newImport = response.data
      imports.value.unshift(newImport)
      return newImport
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to upload file'
      console.error('File upload error:', err)
      throw err
    } finally {
      uploading.value = false
    }
  }

  const triggerImport = async (fileId: string) => {
    loading.value = true
    error.value = null

    try {
      const response = await importApi.triggerImport(fileId)
      // Update the import status
      const importIndex = imports.value.findIndex(imp => imp.id === fileId)
      if (importIndex !== -1) {
        imports.value[importIndex] = { ...imports.value[importIndex], ...response.data }
      }
      return response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to trigger import'
      console.error('Import trigger error:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    loading,
    error,
    imports,
    currentImport,
    uploading,

    // Actions
    fetchImports,
    fetchImport,
    uploadFile,
    triggerImport,
    clearError
  }
})