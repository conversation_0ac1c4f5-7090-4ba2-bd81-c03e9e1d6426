<template>
  <div class="relative isolate overflow-hidden">
    <!-- <PERSON>er -->
    <PageHeader title="RZA Imports">
      <template #actions>
        <button
          @click="refreshImports"
          :disabled="loading"
          class="flex items-center gap-x-1 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 disabled:opacity-50"
        >
          <ArrowPathIcon class="-ml-1.5 size-5" :class="{ 'animate-spin': loading }" aria-hidden="true" />
          Refresh
        </button>
      </template>
    </PageHeader>

    <!-- Background decoration -->
    <div class="absolute left-16 top-full -mt-16 transform-gpu opacity-50 blur-3xl xl:left-1/2 xl:-ml-80">
      <div class="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]" style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)" />
    </div>
    <div class="absolute inset-x-0 bottom-0 h-px bg-gray-900/5" />
  </div>

  <div class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
    <!-- Upload Section -->
    <div class="mb-8">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Upload RZA Export File</h2>
      <FileUpload
        title="Upload RZA XML Export"
        description="Select an RZA full export XML file to import products and customers into WooCommerce"
        accept=".xml"
        accepted-types="XML files only"
        @upload="handleFileUpload"
      />
    </div>

    <!-- Error Display -->
    <div v-if="importStore.error" class="mb-6">
      <div class="rounded-md bg-red-50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <XCircleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="mt-2 text-sm text-red-700">
              <p>{{ importStore.error }}</p>
            </div>
            <div class="mt-4">
              <button
                type="button"
                @click="importStore.clearError"
                class="rounded-md bg-red-50 px-2 py-1.5 text-sm font-medium text-red-800 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 focus:ring-offset-red-50"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Imports List -->
    <div>
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900">Import History</h2>
        <div class="text-sm text-gray-500">
          {{ importStore.imports.length }} total imports
        </div>
      </div>

      <div v-if="loading && importStore.imports.length === 0" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading imports...</p>
      </div>

      <div v-else-if="importStore.imports.length === 0" class="text-center py-12">
        <DocumentArrowUpIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-semibold text-gray-900">No imports yet</h3>
        <p class="mt-1 text-sm text-gray-500">Upload your first RZA export file to get started.</p>
      </div>

      <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
          <li v-for="importItem in importStore.imports" :key="importItem.id">
            <div class="px-4 py-4 flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <DocumentIcon class="h-10 w-10 text-gray-400" />
                </div>
                <div class="ml-4">
                  <div class="flex items-center">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      {{ importItem.originalFilename }}
                    </p>
                    <div class="ml-2 flex-shrink-0">
                      <span :class="getStatusBadgeClass(importItem.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ getStatusText(importItem.status) }}
                      </span>
                    </div>
                  </div>
                  <div class="mt-1 flex items-center text-sm text-gray-500">
                    <time :datetime="importItem.uploadDateTime">{{ formatDate(importItem.uploadDate) }}</time>
                    <span class="mx-2">•</span>
                    <span>{{ formatFileSize(importItem.fileSize) }}</span>
                    <span v-if="importItem.productsCount !== undefined" class="mx-2">•</span>
                    <span v-if="importItem.productsCount !== undefined">
                      {{ importItem.productsCount }} products, {{ importItem.customersCount }} customers
                    </span>
                  </div>
                  <div v-if="importItem.errorMessage" class="mt-1 text-sm text-red-600">
                    {{ importItem.errorMessage }}
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  v-if="importItem.status === 'pending'"
                  @click="triggerImport(importItem.id)"
                  :disabled="loading"
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  <PlayIcon class="-ml-0.5 mr-2 h-4 w-4" aria-hidden="true" />
                  Import
                </button>
                <router-link
                  :to="`/import/${importItem.id}`"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <EyeIcon class="-ml-0.5 mr-2 h-4 w-4" aria-hidden="true" />
                  View
                </router-link>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import {
  ArrowPathIcon,
  DocumentArrowUpIcon,
  DocumentIcon,
  XCircleIcon,
  PlayIcon,
  EyeIcon
} from '@heroicons/vue/24/outline'

import PageHeader from '../components/PageHeader.vue'
import FileUpload from '../components/FileUpload.vue'
import { useImportStore } from './import.store'

const importStore = useImportStore()

const loading = computed(() => importStore.loading)

// Actions
const refreshImports = async () => {
  await importStore.fetchImports()
}

const handleFileUpload = async (files: File[]) => {
  if (files.length > 0) {
    try {
      await importStore.uploadFile(files[0])
      // Refresh the imports list
      await importStore.fetchImports()
    } catch (error) {
      // Error is handled in the store
    }
  }
}

const triggerImport = async (importId: string) => {
  try {
    await importStore.triggerImport(importId)
    // Refresh the imports list
    await importStore.fetchImports()
  } catch (error) {
    // Error is handled in the store
  }
}

// Utility functions
const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    success: 'bg-green-100 text-green-800',
    error: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: 'Pending',
    processing: 'Processing',
    success: 'Success',
    error: 'Error'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Initialize data
onMounted(() => {
  importStore.fetchImports()
})
</script>