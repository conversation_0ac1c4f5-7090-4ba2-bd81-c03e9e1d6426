<template>
  <div class="relative isolate overflow-hidden">
    <!-- <PERSON>er -->
    <header class="relative isolate pt-16">
      <div class="absolute inset-0 -z-10 overflow-hidden" aria-hidden="true">
        <div class="absolute left-16 top-full -mt-16 transform-gpu opacity-50 blur-3xl xl:left-1/2 xl:-ml-80">
          <div class="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]" style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)" />
        </div>
        <div class="absolute inset-x-0 bottom-0 h-px bg-gray-900/5" />
      </div>

      <div class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
        <div class="mx-auto flex max-w-2xl items-center justify-between gap-x-8 lg:mx-0 lg:max-w-none">
          <div class="flex items-center gap-x-6">
            <DocumentIcon class="size-16 flex-none text-gray-400" />
            <h1>
              <div class="text-sm/6 text-gray-500">Import <span class="text-gray-700">#{{ $route.params.id }}</span></div>
              <div class="mt-1 text-base font-semibold text-gray-900">{{ importStore.currentImport?.originalFilename }}</div>
            </h1>
          </div>
          <div class="flex items-center gap-x-4 sm:gap-x-6">
            <router-link to="/import" class="hidden text-sm/6 font-semibold text-gray-900 sm:block">
              ← Back to Imports
            </router-link>
            <button
              v-if="importStore.currentImport?.status === 'pending'"
              @click="triggerImport"
              :disabled="loading"
              class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
            >
              {{ loading ? 'Processing...' : 'Start Import' }}
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="mx-auto max-w-7xl px-4 py-16 sm:px-6 lg:px-8">
      <div v-if="loading && !importStore.currentImport" class="text-center py-12">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <p class="mt-2 text-sm text-gray-500">Loading import details...</p>
      </div>

      <div v-else-if="importStore.error" class="text-center py-12">
        <XCircleIcon class="mx-auto h-12 w-12 text-red-400" />
        <h3 class="mt-2 text-sm font-semibold text-gray-900">Error loading import</h3>
        <p class="mt-1 text-sm text-gray-500">{{ importStore.error }}</p>
        <div class="mt-6">
          <button
            @click="refreshImport"
            class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500"
          >
            Try Again
          </button>
        </div>
      </div>

      <div v-else-if="importStore.currentImport" class="mx-auto grid max-w-2xl grid-cols-1 grid-rows-1 items-start gap-x-8 gap-y-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
        <!-- Import Summary -->
        <div class="lg:col-start-3 lg:row-end-1">
          <h2 class="sr-only">Summary</h2>
          <div class="rounded-lg bg-gray-50 shadow-sm ring-1 ring-gray-900/5">
            <dl class="flex flex-wrap">
              <div class="flex-auto pl-6 pt-6">
                <dt class="text-sm/6 font-semibold text-gray-900">Status</dt>
                <dd class="mt-1">
                  <span :class="getStatusBadgeClass(importStore.currentImport.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                    {{ getStatusText(importStore.currentImport.status) }}
                  </span>
                </dd>
              </div>
              <div class="flex-none self-end px-6 pt-4">
                <dt class="sr-only">File Size</dt>
                <dd class="text-sm text-gray-500">{{ formatFileSize(importStore.currentImport.fileSize) }}</dd>
              </div>
              <div class="mt-6 flex w-full flex-none gap-x-4 border-t border-gray-900/5 px-6 pt-6">
                <dt class="flex-none">
                  <span class="sr-only">Upload Date</span>
                  <CalendarDaysIcon class="h-6 w-5 text-gray-400" aria-hidden="true" />
                </dt>
                <dd class="text-sm/6 text-gray-500">
                  <time :datetime="importStore.currentImport.uploadDateTime">
                    {{ formatDate(importStore.currentImport.uploadDate) }}
                  </time>
                </dd>
              </div>
              <div v-if="importStore.currentImport.processedAt" class="mt-4 flex w-full flex-none gap-x-4 px-6">
                <dt class="flex-none">
                  <span class="sr-only">Processed Date</span>
                  <CheckCircleIcon class="h-6 w-5 text-gray-400" aria-hidden="true" />
                </dt>
                <dd class="text-sm/6 text-gray-500">
                  <time :datetime="importStore.currentImport.processedDateTime">
                    Processed {{ formatDate(importStore.currentImport.processedAt) }}
                  </time>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Import Details -->
        <div class="-mx-4 px-4 py-8 shadow-sm ring-1 ring-gray-900/5 sm:mx-0 sm:rounded-lg sm:px-8 sm:pb-14 lg:col-span-2 lg:row-span-2 lg:row-end-2 xl:px-16 xl:pb-20 xl:pt-16">
          <h2 class="text-base font-semibold text-gray-900">Import Details</h2>
          
          <!-- Summary Stats -->
          <div v-if="importStore.currentImport.summary" class="mt-6 grid grid-cols-1 gap-6 sm:grid-cols-3">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <CubeIcon class="h-6 w-6 text-blue-400" aria-hidden="true" />
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">Products</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ importStore.currentImport.summary.totalProducts }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <UserGroupIcon class="h-6 w-6 text-green-400" aria-hidden="true" />
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">Customers</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ importStore.currentImport.summary.totalCustomers }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <TagIcon class="h-6 w-6 text-purple-400" aria-hidden="true" />
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">Categories</dt>
                      <dd class="text-lg font-medium text-gray-900">{{ importStore.currentImport.summary.totalCategories }}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="importStore.currentImport.errorMessage" class="mt-6 rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <XCircleIcon class="h-5 w-5 text-red-400" aria-hidden="true" />
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Import Error</h3>
                <div class="mt-2 text-sm text-red-700">
                  <p>{{ importStore.currentImport.errorMessage }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- File Info -->
          <div class="mt-8">
            <h3 class="text-sm font-medium text-gray-900">File Information</h3>
            <dl class="mt-2 grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
              <div>
                <dt class="text-sm font-medium text-gray-500">Original Filename</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ importStore.currentImport.originalFilename }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">File Size</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatFileSize(importStore.currentImport.fileSize) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Upload Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(importStore.currentImport.uploadDate) }}</dd>
              </div>
              <div v-if="importStore.currentImport.processedAt">
                <dt class="text-sm font-medium text-gray-500">Processed Date</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(importStore.currentImport.processedAt) }}</dd>
              </div>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  DocumentIcon,
  XCircleIcon,
  CalendarDaysIcon,
  CheckCircleIcon,
  CubeIcon,
  UserGroupIcon,
  TagIcon
} from '@heroicons/vue/24/outline'

import { useImportStore } from './import.store'

const route = useRoute()
const importStore = useImportStore()

const loading = computed(() => importStore.loading)

// Actions
const refreshImport = async () => {
  const importId = route.params.id as string
  await importStore.fetchImport(importId)
}

const triggerImport = async () => {
  const importId = route.params.id as string
  try {
    await importStore.triggerImport(importId)
    await refreshImport()
  } catch (error) {
    // Error is handled in the store
  }
}

// Utility functions
const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    success: 'bg-green-100 text-green-800',
    error: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    pending: 'Pending',
    processing: 'Processing',
    success: 'Success',
    error: 'Error'
  }
  return texts[status as keyof typeof texts] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Initialize data
onMounted(() => {
  const importId = route.params.id as string
  importStore.fetchImport(importId)
})
</script>
