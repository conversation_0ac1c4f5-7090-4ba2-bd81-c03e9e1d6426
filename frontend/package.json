{"name": "frontend", "version": "1.0.0", "description": "Vue.js admin interface for WooCommerce sync", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@vueuse/core": "^10.7.0", "axios": "^1.6.2", "pinia": "^2.1.7", "vue": "^3.3.11", "vue-router": "^4.2.5"}, "devDependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "^5.3.3", "vite": "^5.0.10", "vue-tsc": "^1.8.25"}}