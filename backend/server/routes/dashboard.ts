import { Router } from 'express';
import { createModuleLogger } from '../../infrastructure/logger';
import { databaseService } from '../../infrastructure/database';
import wooCommerceService from '../../services/woocommerce';

const router = Router();
const logger = createModuleLogger('dashboard-routes');

// Get dashboard stats
router.get('/stats', async (req, res) => {
  try {
    // Mock data for now - replace with actual database queries
    const stats = {
      totalImports: 0,
      totalExports: 0,
      successfulImports: 0,
      successfulExports: 0,
      lastImportDate: null,
      lastExportDate: null
    };

    res.json(stats);
  } catch (error) {
    logger.error('Failed to get dashboard stats:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get dashboard stats'
    });
  }
});

// Get recent imports
router.get('/recent-imports', async (req, res) => {
  try {
    // Mock data for now - replace with actual database queries
    const recentImports = [
      {
        id: '1',
        filename: 'rza-export-2025-01-01.xml',
        date: '2025-01-01',
        dateTime: '2025-01-01T10:00:00Z',
        status: 'success',
        productsCount: 150,
        customersCount: 45
      },
      {
        id: '2',
        filename: 'rza-export-2024-12-31.xml',
        date: '2024-12-31',
        dateTime: '2024-12-31T15:30:00Z',
        status: 'success',
        productsCount: 120,
        customersCount: 38
      }
    ];

    res.json(recentImports);
  } catch (error) {
    logger.error('Failed to get recent imports:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get recent imports'
    });
  }
});

// Get recent exports
router.get('/recent-exports', async (req, res) => {
  try {
    // Mock data for now - replace with actual database queries
    const recentExports = [
      {
        id: '1',
        date: '2025-01-01',
        dateTime: '2025-01-01T12:00:00Z',
        status: 'success',
        ordersCount: 25,
        totalAmount: '2,450.00'
      },
      {
        id: '2',
        date: '2024-12-30',
        dateTime: '2024-12-30T14:15:00Z',
        status: 'success',
        ordersCount: 18,
        totalAmount: '1,890.50'
      }
    ];

    res.json(recentExports);
  } catch (error) {
    logger.error('Failed to get recent exports:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get recent exports'
    });
  }
});

// Get system health
router.get('/health', async (req, res) => {
  try {
    const dbHealth = await databaseService.healthCheck();
    const wooCommerceHealth = wooCommerceService ? await wooCommerceService.healthCheck() : false;

    const health = {
      database: {
        status: dbHealth ? 'connected' : 'disconnected',
        connected: dbHealth
      },
      woocommerce: {
        status: wooCommerceHealth ? 'connected' : 'disconnected',
        connected: wooCommerceHealth
      },
      server: {
        status: 'running',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.env.npm_package_version || '1.0.0'
      }
    };

    res.json(health);
  } catch (error) {
    logger.error('Failed to get system health:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get system health'
    });
  }
});

export default router;
