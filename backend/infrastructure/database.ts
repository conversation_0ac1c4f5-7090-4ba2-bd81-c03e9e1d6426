/**
 * Database infrastructure service using better-sqlite3
 * Provides the core database connection and basic operations
 */

import Database from "better-sqlite3";
import { existsSync, mkdirSync } from "fs";
import { dirname } from "path";
import { createModuleLogger } from "./logger";

const logger = createModuleLogger("database");

export class DatabaseInfrastructure {
  private static instance: DatabaseInfrastructure;
  private db: Database.Database | null = null;
  private dbPath: string;

  private constructor() {
    // Use DATABASE_PATH env variable with fallback
    this.dbPath = process.env.DATABASE_PATH || "./data/database.sqlite";
    this.initialize();
  }

  public static getInstance(): DatabaseInfrastructure {
    if (!DatabaseInfrastructure.instance) {
      DatabaseInfrastructure.instance = new DatabaseInfrastructure();
    }
    return DatabaseInfrastructure.instance;
  }

  private initialize(): void {
    try {
      // Ensure the directory exists
      const dbDir = dirname(this.dbPath);
      if (!existsSync(dbDir)) {
        mkdirSync(dbDir, { recursive: true });
        logger.info(`Created database directory: ${dbDir}`);
      }

      // Create database connection
      this.db = new Database(this.dbPath);
      this.db.pragma("journal_mode = WAL");
      this.db.pragma("foreign_keys = ON");

      logger.info(`Database connected: ${this.dbPath}`);

      // Initialize tables
      this.initializeTables();
    } catch (error) {
      logger.error("Failed to initialize database:", error);
      throw error;
    }
  }

  private initializeTables(): void {
    if (!this.db) {
      throw new Error("Database not initialized");
    }

    // Create export_jobs table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS export_jobs (
        id TEXT PRIMARY KEY,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        error_message TEXT,
        product_ids_with_price_error TEXT, -- JSON array
        product_ids_with_category_error TEXT, -- JSON array
        new_unknown_categories TEXT, -- JSON array
        new_unknown_attributes TEXT, -- JSON array
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create import_jobs table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS import_jobs (
        id TEXT PRIMARY KEY,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    logger.info("Database tables initialized");
  }

  public getDatabase(): Database.Database {
    if (!this.db) {
      throw new Error("Database not initialized");
    }
    return this.db;
  }

  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.db) {
        return false;
      }

      // Simple query to check if database is responsive
      const result = this.db.prepare("SELECT 1 as test").get();
      return result && (result as any).test === 1;
    } catch (error) {
      logger.error("Database health check failed:", error);
      return false;
    }
  }

  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      logger.info("Database connection closed");
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseInfrastructure.getInstance();
export default databaseService;
