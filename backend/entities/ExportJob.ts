/**
 * TypeORM Entity for Export Jobs
 * Represents export job records in the database
 */

import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('export_jobs')
export class ExportJob {
  @PrimaryColumn('text')
  id!: string;

  @Column('text', { name: 'started_at' })
  startedAt!: string;

  @Column('text', { name: 'completed_at', nullable: true })
  completedAt?: string;

  @Column('text', { name: 'error_message', nullable: true })
  errorMessage?: string;

  @Column('simple-json', { name: 'product_ids_with_price_error', nullable: true })
  productIdsWithPriceError?: string[];

  @Column('simple-json', { name: 'product_ids_with_category_error', nullable: true })
  productIdsWithCategoryError?: string[];

  @Column('simple-json', { name: 'new_unknown_categories', nullable: true })
  newUnknownCategories?: string[];

  @Column('simple-json', { name: 'new_unknown_attributes', nullable: true })
  newUnknownAttributes?: string[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;
}
