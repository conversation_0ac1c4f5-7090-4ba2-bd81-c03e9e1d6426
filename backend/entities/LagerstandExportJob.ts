/**
 * TypeORM Entity for Lagerstand Export Jobs
 * Represents stock export job records in the database
 */

import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('lagerstand_export_jobs')
export class LagerstandExportJob {
  @PrimaryColumn('text')
  id!: string;

  @Column('text', { name: 'started_at' })
  startedAt!: string;

  @Column('text', { name: 'completed_at', nullable: true })
  completedAt?: string;

  @Column('text', { name: 'error_message', nullable: true })
  errorMessage?: string;

  @Column('simple-json', { name: 'product_ids_processed', nullable: true })
  productIdsProcessed?: string[];

  @Column('simple-json', { name: 'product_ids_with_error', nullable: true })
  productIdsWithError?: string[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;
}
