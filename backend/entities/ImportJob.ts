/**
 * TypeORM Entity for Import Jobs
 * Represents import job records in the database
 */

import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('import_jobs')
export class ImportJob {
  @PrimaryColumn('text')
  id!: string;

  @Column('text', { name: 'started_at' })
  startedAt!: string;

  @Column('text', { name: 'completed_at', nullable: true })
  completedAt?: string;

  @Column('text', { name: 'error_message', nullable: true })
  errorMessage?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt!: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt!: Date;
}
