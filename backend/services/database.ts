/**
 * The database service the access and write to the database.
 * The schema is created with typeorm.
 */

export type ExportJob = {
  id: string;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
  productIdsWithPriceError?: string[];
  productIdsWithCategoryError?: string[];
  newUnknownCategories?: string[];
  newUnknownAttributes?: string[];
};

export type ImportJob = {
  id: string;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
};

export class DatabaseService {
  public async getExportJobs(): Promise<ExportJob[]> {
    throw new Error("Not implemented");
  }

  public async getExportJob(id: string): Promise<ExportJob | null> {
    throw new Error("Not implemented");
  }

  public async createExportJob(): Promise<ExportJob> {
    throw new Error("Not implemented");
  }

  public async updateExportJob(
    id: string,
    update: Partial<ExportJob>
  ): Promise<ExportJob> {
    throw new Error("Not implemented");
  }
}
