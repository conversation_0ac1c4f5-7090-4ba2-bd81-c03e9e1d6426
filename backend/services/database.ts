/**
 * The database service to access and write to the database.
 * Uses better-sqlite3 for SQLite operations with a simple, maintainable approach.
 */

import { databaseService as dbInfrastructure } from "../infrastructure/database";
import { createModuleLogger } from "../infrastructure/logger";
const logger = createModuleLogger("database-service");

/**
 * Generate a unique ID for jobs
 */
function generateJobId(): string {
  return (
    Date.now().toString() + "-" + Math.random().toString(36).substring(2, 11)
  );
}

export type ExportJob = {
  id: string;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
  productIdsWithPriceError?: string[];
  productIdsWithCategoryError?: string[];
  newUnknownCategories?: string[];
  newUnknownAttributes?: string[];
};

export type ImportJob = {
  id: string;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
};

/**
 * Database service for managing export and import jobs
 * Provides CRUD operations for both job types with proper error handling
 */
export class DatabaseService {
  // ===== EXPORT JOB METHODS =====

  /**
   * Retrieve all export jobs from the database
   * @returns Promise<ExportJob[]> Array of all export jobs
   */
  public async getExportJobs(): Promise<ExportJob[]> {
    try {
      const db = dbInfrastructure.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage,
          product_ids_with_price_error as productIdsWithPriceError,
          product_ids_with_category_error as productIdsWithCategoryError,
          new_unknown_categories as newUnknownCategories,
          new_unknown_attributes as newUnknownAttributes
        FROM export_jobs
        ORDER BY created_at DESC
      `);

      const rows = stmt.all() as any[];

      // Parse JSON fields and convert to ExportJob objects
      return rows.map((row) => this.parseExportJobRow(row));
    } catch (error) {
      logger.error("Failed to get export jobs:", error);
      throw new Error("Failed to retrieve export jobs");
    }
  }

  /**
   * Retrieve a specific export job by ID
   * @param id - The export job ID
   * @returns Promise<ExportJob | null> The export job or null if not found
   */
  public async getExportJob(id: string): Promise<ExportJob | null> {
    try {
      const db = dbInfrastructure.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage,
          product_ids_with_price_error as productIdsWithPriceError,
          product_ids_with_category_error as productIdsWithCategoryError,
          new_unknown_categories as newUnknownCategories,
          new_unknown_attributes as newUnknownAttributes
        FROM export_jobs
        WHERE id = ?
      `);

      const row = stmt.get(id) as any;

      if (!row) {
        return null;
      }

      return this.parseExportJobRow(row);
    } catch (error) {
      logger.error("Failed to get export job:", { id, error });
      throw new Error(`Failed to retrieve export job with ID: ${id}`);
    }
  }

  /**
   * Create a new export job
   * @returns Promise<ExportJob> The created export job
   */
  public async createExportJob(): Promise<ExportJob> {
    try {
      const db = dbInfrastructure.getDatabase();
      const id = generateJobId();
      const startedAt = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO export_jobs (id, started_at)
        VALUES (?, ?)
      `);

      stmt.run(id, startedAt);

      const exportJob: ExportJob = {
        id,
        startedAt,
      };

      logger.info("Created export job:", { id });
      return exportJob;
    } catch (error) {
      logger.error("Failed to create export job:", error);
      throw new Error("Failed to create export job");
    }
  }

  /**
   * Update an existing export job
   * @param id - The export job ID
   * @param update - Partial export job data to update
   * @returns Promise<ExportJob> The updated export job
   */
  public async updateExportJob(
    id: string,
    update: Partial<ExportJob>
  ): Promise<ExportJob> {
    try {
      const db = dbInfrastructure.getDatabase();

      // Build dynamic update query based on provided fields
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (update.completedAt !== undefined) {
        updateFields.push("completed_at = ?");
        updateValues.push(update.completedAt);
      }

      if (update.errorMessage !== undefined) {
        updateFields.push("error_message = ?");
        updateValues.push(update.errorMessage);
      }

      if (update.productIdsWithPriceError !== undefined) {
        updateFields.push("product_ids_with_price_error = ?");
        updateValues.push(JSON.stringify(update.productIdsWithPriceError));
      }

      if (update.productIdsWithCategoryError !== undefined) {
        updateFields.push("product_ids_with_category_error = ?");
        updateValues.push(JSON.stringify(update.productIdsWithCategoryError));
      }

      if (update.newUnknownCategories !== undefined) {
        updateFields.push("new_unknown_categories = ?");
        updateValues.push(JSON.stringify(update.newUnknownCategories));
      }

      if (update.newUnknownAttributes !== undefined) {
        updateFields.push("new_unknown_attributes = ?");
        updateValues.push(JSON.stringify(update.newUnknownAttributes));
      }

      if (updateFields.length === 0) {
        throw new Error("No fields to update");
      }

      // Add updated_at timestamp
      updateFields.push("updated_at = CURRENT_TIMESTAMP");
      updateValues.push(id); // Add ID for WHERE clause

      const stmt = db.prepare(`
        UPDATE export_jobs
        SET ${updateFields.join(", ")}
        WHERE id = ?
      `);

      const result = stmt.run(...updateValues);

      if (result.changes === 0) {
        throw new Error(`Export job with ID ${id} not found`);
      }

      // Retrieve and return the updated job
      const updatedJob = await this.getExportJob(id);
      if (!updatedJob) {
        throw new Error(`Failed to retrieve updated export job with ID ${id}`);
      }

      logger.info("Updated export job:", {
        id,
        updateFields: Object.keys(update),
      });
      return updatedJob;
    } catch (error) {
      logger.error("Failed to update export job:", { id, error });
      throw new Error(`Failed to update export job with ID: ${id}`);
    }
  }

  // ===== IMPORT JOB METHODS =====

  /**
   * Retrieve all import jobs from the database
   * @returns Promise<ImportJob[]> Array of all import jobs
   */
  public async getImportJobs(): Promise<ImportJob[]> {
    try {
      const db = dbInfrastructure.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage
        FROM import_jobs
        ORDER BY created_at DESC
      `);

      const rows = stmt.all() as any[];

      // Convert to ImportJob objects
      return rows.map((row) => this.parseImportJobRow(row));
    } catch (error) {
      logger.error("Failed to get import jobs:", error);
      throw new Error("Failed to retrieve import jobs");
    }
  }

  /**
   * Retrieve a specific import job by ID
   * @param id - The import job ID
   * @returns Promise<ImportJob | null> The import job or null if not found
   */
  public async getImportJob(id: string): Promise<ImportJob | null> {
    try {
      const db = dbInfrastructure.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage
        FROM import_jobs
        WHERE id = ?
      `);

      const row = stmt.get(id) as any;

      if (!row) {
        return null;
      }

      return this.parseImportJobRow(row);
    } catch (error) {
      logger.error("Failed to get import job:", { id, error });
      throw new Error(`Failed to retrieve import job with ID: ${id}`);
    }
  }

  /**
   * Create a new import job
   * @returns Promise<ImportJob> The created import job
   */
  public async createImportJob(): Promise<ImportJob> {
    try {
      const db = dbInfrastructure.getDatabase();
      const id = generateJobId();
      const startedAt = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO import_jobs (id, started_at)
        VALUES (?, ?)
      `);

      stmt.run(id, startedAt);

      const importJob: ImportJob = {
        id,
        startedAt,
      };

      logger.info("Created import job:", { id });
      return importJob;
    } catch (error) {
      logger.error("Failed to create import job:", error);
      throw new Error("Failed to create import job");
    }
  }

  /**
   * Update an existing import job
   * @param id - The import job ID
   * @param update - Partial import job data to update
   * @returns Promise<ImportJob> The updated import job
   */
  public async updateImportJob(
    id: string,
    update: Partial<ImportJob>
  ): Promise<ImportJob> {
    try {
      const db = dbInfrastructure.getDatabase();

      // Build dynamic update query based on provided fields
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (update.completedAt !== undefined) {
        updateFields.push("completed_at = ?");
        updateValues.push(update.completedAt);
      }

      if (update.errorMessage !== undefined) {
        updateFields.push("error_message = ?");
        updateValues.push(update.errorMessage);
      }

      if (updateFields.length === 0) {
        throw new Error("No fields to update");
      }

      // Add updated_at timestamp
      updateFields.push("updated_at = CURRENT_TIMESTAMP");
      updateValues.push(id); // Add ID for WHERE clause

      const stmt = db.prepare(`
        UPDATE import_jobs
        SET ${updateFields.join(", ")}
        WHERE id = ?
      `);

      const result = stmt.run(...updateValues);

      if (result.changes === 0) {
        throw new Error(`Import job with ID ${id} not found`);
      }

      // Retrieve and return the updated job
      const updatedJob = await this.getImportJob(id);
      if (!updatedJob) {
        throw new Error(`Failed to retrieve updated import job with ID ${id}`);
      }

      logger.info("Updated import job:", {
        id,
        updateFields: Object.keys(update),
      });
      return updatedJob;
    } catch (error) {
      logger.error("Failed to update import job:", { id, error });
      throw new Error(`Failed to update import job with ID: ${id}`);
    }
  }

  // ===== HELPER METHODS =====

  /**
   * Parse a database row into an ExportJob object
   * Handles JSON parsing for array fields
   * @param row - Raw database row
   * @returns ExportJob object
   */
  private parseExportJobRow(row: any): ExportJob {
    const exportJob: ExportJob = {
      id: row.id,
      startedAt: row.startedAt,
    };

    // Add optional fields if they exist
    if (row.completedAt) {
      exportJob.completedAt = row.completedAt;
    }

    if (row.errorMessage) {
      exportJob.errorMessage = row.errorMessage;
    }

    // Parse JSON array fields safely
    if (row.productIdsWithPriceError) {
      try {
        exportJob.productIdsWithPriceError = JSON.parse(
          row.productIdsWithPriceError
        );
      } catch (error) {
        logger.warn("Failed to parse productIdsWithPriceError JSON:", {
          id: row.id,
          error,
        });
        exportJob.productIdsWithPriceError = [];
      }
    }

    if (row.productIdsWithCategoryError) {
      try {
        exportJob.productIdsWithCategoryError = JSON.parse(
          row.productIdsWithCategoryError
        );
      } catch (error) {
        logger.warn("Failed to parse productIdsWithCategoryError JSON:", {
          id: row.id,
          error,
        });
        exportJob.productIdsWithCategoryError = [];
      }
    }

    if (row.newUnknownCategories) {
      try {
        exportJob.newUnknownCategories = JSON.parse(row.newUnknownCategories);
      } catch (error) {
        logger.warn("Failed to parse newUnknownCategories JSON:", {
          id: row.id,
          error,
        });
        exportJob.newUnknownCategories = [];
      }
    }

    if (row.newUnknownAttributes) {
      try {
        exportJob.newUnknownAttributes = JSON.parse(row.newUnknownAttributes);
      } catch (error) {
        logger.warn("Failed to parse newUnknownAttributes JSON:", {
          id: row.id,
          error,
        });
        exportJob.newUnknownAttributes = [];
      }
    }

    return exportJob;
  }

  /**
   * Parse a database row into an ImportJob object
   * @param row - Raw database row
   * @returns ImportJob object
   */
  private parseImportJobRow(row: any): ImportJob {
    const importJob: ImportJob = {
      id: row.id,
      startedAt: row.startedAt,
    };

    // Add optional fields if they exist
    if (row.completedAt) {
      importJob.completedAt = row.completedAt;
    }

    if (row.errorMessage) {
      importJob.errorMessage = row.errorMessage;
    }

    return importJob;
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
export default databaseService;
