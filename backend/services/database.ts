/**
 * The database service to access and write to the database.
 * Uses better-sqlite3 for SQLite operations with a simple, maintainable approach.
 * Handles all database operations including connection management and table creation.
 */

// Type declarations for Node.js globals
declare const process: any;
declare const require: any;

import { createModuleLogger } from "../infrastructure/logger";

const logger = createModuleLogger("database-service");

/**
 * Generate a unique ID for jobs
 */
function generateJobId(): string {
  return (
    Date.now().toString() + "-" + Math.random().toString(36).substring(2, 11)
  );
}

export type ExportJob = {
  id: string;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
  productIdsWithPriceError?: string[];
  productIdsWithCategoryError?: string[];
  newUnknownCategories?: string[];
  newUnknownAttributes?: string[];
};

export type ImportJob = {
  id: string;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
};

/**
 * Database service for managing export and import jobs
 * Provides CRUD operations for both job types with proper error handling
 * Manages SQLite database connection and table creation internally
 */
export class DatabaseService {
  private static instance: DatabaseService;
  private db: any | null = null; // Using any to avoid import issues
  private dbPath: string;

  private constructor() {
    // Use DATABASE_PATH env variable with fallback
    this.dbPath = process.env.DATABASE_PATH || "./data/database.sqlite";
    this.initialize();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Initialize the database connection and create tables
   */
  private initialize(): void {
    try {
      // Import better-sqlite3 dynamically to avoid TypeScript issues
      const Database = require("better-sqlite3");
      const fs = require("fs");
      const path = require("path");

      // Ensure the directory exists
      const dbDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
        logger.info(`Created database directory: ${dbDir}`);
      }

      // Create database connection
      this.db = new Database(this.dbPath);
      this.db.pragma("journal_mode = WAL");
      this.db.pragma("foreign_keys = ON");

      logger.info(`Database connected: ${this.dbPath}`);

      // Initialize tables
      this.initializeTables();
    } catch (error) {
      logger.error("Failed to initialize database:", error);
      throw error;
    }
  }

  /**
   * Create database tables if they don't exist
   */
  private initializeTables(): void {
    if (!this.db) {
      throw new Error("Database not initialized");
    }

    // Create export_jobs table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS export_jobs (
        id TEXT PRIMARY KEY,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        error_message TEXT,
        product_ids_with_price_error TEXT, -- JSON array
        product_ids_with_category_error TEXT, -- JSON array
        new_unknown_categories TEXT, -- JSON array
        new_unknown_attributes TEXT, -- JSON array
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create import_jobs table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS import_jobs (
        id TEXT PRIMARY KEY,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    logger.info("Database tables initialized");
  }

  /**
   * Get the database connection
   */
  private getDatabase(): any {
    if (!this.db) {
      throw new Error("Database not initialized");
    }
    return this.db;
  }

  /**
   * Check if database is healthy
   */
  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.db) {
        return false;
      }

      // Simple query to check if database is responsive
      const result = this.db.prepare("SELECT 1 as test").get();
      return result && result.test === 1;
    } catch (error) {
      logger.error("Database health check failed:", error);
      return false;
    }
  }

  /**
   * Close the database connection
   */
  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      logger.info("Database connection closed");
    }
  }
  // ===== EXPORT JOB METHODS =====

  /**
   * Retrieve all export jobs from the database
   * @returns Promise<ExportJob[]> Array of all export jobs
   */
  public async getExportJobs(): Promise<ExportJob[]> {
    try {
      const db = this.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage,
          product_ids_with_price_error as productIdsWithPriceError,
          product_ids_with_category_error as productIdsWithCategoryError,
          new_unknown_categories as newUnknownCategories,
          new_unknown_attributes as newUnknownAttributes
        FROM export_jobs
        ORDER BY created_at DESC
      `);

      const rows = stmt.all() as any[];

      // Parse JSON fields and convert to ExportJob objects
      return rows.map((row) => this.parseExportJobRow(row));
    } catch (error) {
      logger.error("Failed to get export jobs:", error);
      throw new Error("Failed to retrieve export jobs");
    }
  }

  /**
   * Retrieve a specific export job by ID
   * @param id - The export job ID
   * @returns Promise<ExportJob | null> The export job or null if not found
   */
  public async getExportJob(id: string): Promise<ExportJob | null> {
    try {
      const db = this.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage,
          product_ids_with_price_error as productIdsWithPriceError,
          product_ids_with_category_error as productIdsWithCategoryError,
          new_unknown_categories as newUnknownCategories,
          new_unknown_attributes as newUnknownAttributes
        FROM export_jobs
        WHERE id = ?
      `);

      const row = stmt.get(id) as any;

      if (!row) {
        return null;
      }

      return this.parseExportJobRow(row);
    } catch (error) {
      logger.error("Failed to get export job:", { id, error });
      throw new Error(`Failed to retrieve export job with ID: ${id}`);
    }
  }

  /**
   * Create a new export job
   * @returns Promise<ExportJob> The created export job
   */
  public async createExportJob(): Promise<ExportJob> {
    try {
      const db = this.getDatabase();
      const id = generateJobId();
      const startedAt = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO export_jobs (id, started_at)
        VALUES (?, ?)
      `);

      stmt.run(id, startedAt);

      const exportJob: ExportJob = {
        id,
        startedAt,
      };

      logger.info("Created export job:", { id });
      return exportJob;
    } catch (error) {
      logger.error("Failed to create export job:", error);
      throw new Error("Failed to create export job");
    }
  }

  /**
   * Update an existing export job
   * @param id - The export job ID
   * @param update - Partial export job data to update
   * @returns Promise<ExportJob> The updated export job
   */
  public async updateExportJob(
    id: string,
    update: Partial<ExportJob>
  ): Promise<ExportJob> {
    try {
      const db = this.getDatabase();

      // Build dynamic update query based on provided fields
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (update.completedAt !== undefined) {
        updateFields.push("completed_at = ?");
        updateValues.push(update.completedAt);
      }

      if (update.errorMessage !== undefined) {
        updateFields.push("error_message = ?");
        updateValues.push(update.errorMessage);
      }

      if (update.productIdsWithPriceError !== undefined) {
        updateFields.push("product_ids_with_price_error = ?");
        updateValues.push(JSON.stringify(update.productIdsWithPriceError));
      }

      if (update.productIdsWithCategoryError !== undefined) {
        updateFields.push("product_ids_with_category_error = ?");
        updateValues.push(JSON.stringify(update.productIdsWithCategoryError));
      }

      if (update.newUnknownCategories !== undefined) {
        updateFields.push("new_unknown_categories = ?");
        updateValues.push(JSON.stringify(update.newUnknownCategories));
      }

      if (update.newUnknownAttributes !== undefined) {
        updateFields.push("new_unknown_attributes = ?");
        updateValues.push(JSON.stringify(update.newUnknownAttributes));
      }

      if (updateFields.length === 0) {
        throw new Error("No fields to update");
      }

      // Add updated_at timestamp
      updateFields.push("updated_at = CURRENT_TIMESTAMP");
      updateValues.push(id); // Add ID for WHERE clause

      const stmt = db.prepare(`
        UPDATE export_jobs
        SET ${updateFields.join(", ")}
        WHERE id = ?
      `);

      const result = stmt.run(...updateValues);

      if (result.changes === 0) {
        throw new Error(`Export job with ID ${id} not found`);
      }

      // Retrieve and return the updated job
      const updatedJob = await this.getExportJob(id);
      if (!updatedJob) {
        throw new Error(`Failed to retrieve updated export job with ID ${id}`);
      }

      logger.info("Updated export job:", {
        id,
        updateFields: Object.keys(update),
      });
      return updatedJob;
    } catch (error) {
      logger.error("Failed to update export job:", { id, error });
      throw new Error(`Failed to update export job with ID: ${id}`);
    }
  }

  // ===== IMPORT JOB METHODS =====

  /**
   * Retrieve all import jobs from the database
   * @returns Promise<ImportJob[]> Array of all import jobs
   */
  public async getImportJobs(): Promise<ImportJob[]> {
    try {
      const db = this.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage
        FROM import_jobs
        ORDER BY created_at DESC
      `);

      const rows = stmt.all() as any[];

      // Convert to ImportJob objects
      return rows.map((row) => this.parseImportJobRow(row));
    } catch (error) {
      logger.error("Failed to get import jobs:", error);
      throw new Error("Failed to retrieve import jobs");
    }
  }

  /**
   * Retrieve a specific import job by ID
   * @param id - The import job ID
   * @returns Promise<ImportJob | null> The import job or null if not found
   */
  public async getImportJob(id: string): Promise<ImportJob | null> {
    try {
      const db = this.getDatabase();
      const stmt = db.prepare(`
        SELECT
          id,
          started_at as startedAt,
          completed_at as completedAt,
          error_message as errorMessage
        FROM import_jobs
        WHERE id = ?
      `);

      const row = stmt.get(id) as any;

      if (!row) {
        return null;
      }

      return this.parseImportJobRow(row);
    } catch (error) {
      logger.error("Failed to get import job:", { id, error });
      throw new Error(`Failed to retrieve import job with ID: ${id}`);
    }
  }

  /**
   * Create a new import job
   * @returns Promise<ImportJob> The created import job
   */
  public async createImportJob(): Promise<ImportJob> {
    try {
      const db = this.getDatabase();
      const id = generateJobId();
      const startedAt = new Date().toISOString();

      const stmt = db.prepare(`
        INSERT INTO import_jobs (id, started_at)
        VALUES (?, ?)
      `);

      stmt.run(id, startedAt);

      const importJob: ImportJob = {
        id,
        startedAt,
      };

      logger.info("Created import job:", { id });
      return importJob;
    } catch (error) {
      logger.error("Failed to create import job:", error);
      throw new Error("Failed to create import job");
    }
  }

  /**
   * Update an existing import job
   * @param id - The import job ID
   * @param update - Partial import job data to update
   * @returns Promise<ImportJob> The updated import job
   */
  public async updateImportJob(
    id: string,
    update: Partial<ImportJob>
  ): Promise<ImportJob> {
    try {
      const db = this.getDatabase();

      // Build dynamic update query based on provided fields
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (update.completedAt !== undefined) {
        updateFields.push("completed_at = ?");
        updateValues.push(update.completedAt);
      }

      if (update.errorMessage !== undefined) {
        updateFields.push("error_message = ?");
        updateValues.push(update.errorMessage);
      }

      if (updateFields.length === 0) {
        throw new Error("No fields to update");
      }

      // Add updated_at timestamp
      updateFields.push("updated_at = CURRENT_TIMESTAMP");
      updateValues.push(id); // Add ID for WHERE clause

      const stmt = db.prepare(`
        UPDATE import_jobs
        SET ${updateFields.join(", ")}
        WHERE id = ?
      `);

      const result = stmt.run(...updateValues);

      if (result.changes === 0) {
        throw new Error(`Import job with ID ${id} not found`);
      }

      // Retrieve and return the updated job
      const updatedJob = await this.getImportJob(id);
      if (!updatedJob) {
        throw new Error(`Failed to retrieve updated import job with ID ${id}`);
      }

      logger.info("Updated import job:", {
        id,
        updateFields: Object.keys(update),
      });
      return updatedJob;
    } catch (error) {
      logger.error("Failed to update import job:", { id, error });
      throw new Error(`Failed to update import job with ID: ${id}`);
    }
  }

  // ===== HELPER METHODS =====

  /**
   * Parse a database row into an ExportJob object
   * Handles JSON parsing for array fields
   * @param row - Raw database row
   * @returns ExportJob object
   */
  private parseExportJobRow(row: any): ExportJob {
    const exportJob: ExportJob = {
      id: row.id,
      startedAt: row.startedAt,
    };

    // Add optional fields if they exist
    if (row.completedAt) {
      exportJob.completedAt = row.completedAt;
    }

    if (row.errorMessage) {
      exportJob.errorMessage = row.errorMessage;
    }

    // Parse JSON array fields safely
    if (row.productIdsWithPriceError) {
      try {
        exportJob.productIdsWithPriceError = JSON.parse(
          row.productIdsWithPriceError
        );
      } catch (error) {
        logger.warn("Failed to parse productIdsWithPriceError JSON:", {
          id: row.id,
          error,
        });
        exportJob.productIdsWithPriceError = [];
      }
    }

    if (row.productIdsWithCategoryError) {
      try {
        exportJob.productIdsWithCategoryError = JSON.parse(
          row.productIdsWithCategoryError
        );
      } catch (error) {
        logger.warn("Failed to parse productIdsWithCategoryError JSON:", {
          id: row.id,
          error,
        });
        exportJob.productIdsWithCategoryError = [];
      }
    }

    if (row.newUnknownCategories) {
      try {
        exportJob.newUnknownCategories = JSON.parse(row.newUnknownCategories);
      } catch (error) {
        logger.warn("Failed to parse newUnknownCategories JSON:", {
          id: row.id,
          error,
        });
        exportJob.newUnknownCategories = [];
      }
    }

    if (row.newUnknownAttributes) {
      try {
        exportJob.newUnknownAttributes = JSON.parse(row.newUnknownAttributes);
      } catch (error) {
        logger.warn("Failed to parse newUnknownAttributes JSON:", {
          id: row.id,
          error,
        });
        exportJob.newUnknownAttributes = [];
      }
    }

    return exportJob;
  }

  /**
   * Parse a database row into an ImportJob object
   * @param row - Raw database row
   * @returns ImportJob object
   */
  private parseImportJobRow(row: any): ImportJob {
    const importJob: ImportJob = {
      id: row.id,
      startedAt: row.startedAt,
    };

    // Add optional fields if they exist
    if (row.completedAt) {
      importJob.completedAt = row.completedAt;
    }

    if (row.errorMessage) {
      importJob.errorMessage = row.errorMessage;
    }

    return importJob;
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
export default databaseService;
