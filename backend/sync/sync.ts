/**
 * This is the main entry point for the syncing logic
 * between the server and woocommerce.
 * Uses DatabaseService for job tracking and operations for actual sync logic.
 */

import { CronJob } from "cron";
import { createModuleLogger } from "../infrastructure/logger";
import { databaseService } from "../services/database";

const logger = createModuleLogger("sync");

// Import sync operations (these will throw "not implemented" errors for now)
const exportOrders = async (jobId: string): Promise<void> => {
  throw new Error("Order export not implemented yet");
};

const exportProducts = async (jobId: string): Promise<void> => {
  throw new Error("Product export not implemented yet");
};

const exportCustomers = async (jobId: string): Promise<void> => {
  throw new Error("Customer export not implemented yet");
};

const importProducts = async (jobId: string): Promise<void> => {
  throw new Error("Product import not implemented yet");
};

const importCustomers = async (jobId: string): Promise<void> => {
  throw new Error("Customer import not implemented yet");
};

const importOrders = async (jobId: string): Promise<void> => {
  throw new Error("Order import not implemented yet");
};

const exportStock = async (jobId: string): Promise<void> => {
  throw new Error("Stock export not implemented yet");
};

const importStock = async (jobId: string): Promise<void> => {
  throw new Error("Stock import not implemented yet");
};

const syncStock = async (jobId: string): Promise<void> => {
  throw new Error("Bidirectional stock sync not implemented yet");
};

/**
 * SyncService manages all synchronization operations between WooCommerce and external systems
 * Uses DatabaseService for job tracking and delegates actual sync work to operation modules
 */
export class SyncService {
  private static instance: SyncService;
  private productSyncJob?: CronJob;
  private orderSyncJob?: CronJob;
  private stockSyncJob?: CronJob;
  private isRunning = false;

  private constructor() {
    this.setupCronJobs();
  }

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  // ===== EXPORT JOB METHODS =====

  /**
   * Start an export job for orders
   */
  public async startOrderExport(): Promise<ExportJob> {
    const job = await databaseService.createExportJob();

    try {
      logger.info("Starting order export", { jobId: job.id });

      // Delegate to export operation
      await exportOrders(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateExportJob(job.id, {
        completedAt: new Date().toISOString(),
      });

      logger.info("Order export completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateExportJob(job.id, {
        completedAt: new Date().toISOString(),
        errorMessage,
      });

      logger.error("Order export failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Start an export job for products
   */
  public async startProductExport(): Promise<ExportJob> {
    const job = await databaseService.createExportJob();

    try {
      logger.info("Starting product export", { jobId: job.id });

      // Delegate to export operation
      await exportProducts(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateExportJob(job.id, {
        completedAt: new Date().toISOString(),
      });

      logger.info("Product export completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateExportJob(job.id, {
        completedAt: new Date().toISOString(),
        errorMessage,
      });

      logger.error("Product export failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Start an export job for customers
   */
  public async startCustomerExport(): Promise<ExportJob> {
    const job = await databaseService.createExportJob();

    try {
      logger.info("Starting customer export", { jobId: job.id });

      // Delegate to export operation
      await exportCustomers(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateExportJob(job.id, {
        completedAt: new Date().toISOString(),
      });

      logger.info("Customer export completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateExportJob(job.id, {
        completedAt: new Date().toISOString(),
        errorMessage,
      });

      logger.error("Customer export failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  // ===== IMPORT JOB METHODS =====

  /**
   * Start an import job for products
   */
  public async startProductImport(): Promise<ImportJob> {
    const job = await databaseService.createImportJob();

    try {
      logger.info("Starting product import", { jobId: job.id });

      // Delegate to import operation
      await importProducts(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateImportJob(job.id, {
        completedAt: new Date().toISOString(),
      });

      logger.info("Product import completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateImportJob(job.id, {
        completedAt: new Date().toISOString(),
        errorMessage,
      });

      logger.error("Product import failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Start an import job for customers
   */
  public async startCustomerImport(): Promise<ImportJob> {
    const job = await databaseService.createImportJob();

    try {
      logger.info("Starting customer import", { jobId: job.id });

      // Delegate to import operation
      await importCustomers(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateImportJob(job.id, {
        completedAt: new Date().toISOString(),
      });

      logger.info("Customer import completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateImportJob(job.id, {
        completedAt: new Date().toISOString(),
        errorMessage,
      });

      logger.error("Customer import failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Start an import job for orders
   */
  public async startOrderImport(): Promise<ImportJob> {
    const job = await databaseService.createImportJob();

    try {
      logger.info("Starting order import", { jobId: job.id });

      // Delegate to import operation
      await importOrders(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateImportJob(job.id, {
        completedAt: new Date().toISOString(),
      });

      logger.info("Order import completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateImportJob(job.id, {
        completedAt: new Date().toISOString(),
        errorMessage,
      });

      logger.error("Order import failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  // ===== LAGERSTAND (STOCK) JOB METHODS =====

  /**
   * Start a lagerstand export job for stock synchronization
   */
  public async startStockExport(): Promise<LagerstandExportJob> {
    const job = await databaseService.createLagerstandExportJob();

    try {
      logger.info("Starting stock export", { jobId: job.id });

      // Delegate to stock export operation
      await exportStock(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateLagerstandExportJob(
        job.id,
        {
          completedAt: new Date().toISOString(),
        }
      );

      logger.info("Stock export completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateLagerstandExportJob(
        job.id,
        {
          completedAt: new Date().toISOString(),
          errorMessage,
        }
      );

      logger.error("Stock export failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Start a stock import operation (using lagerstand export job for tracking)
   */
  public async startStockImport(): Promise<LagerstandExportJob> {
    const job = await databaseService.createLagerstandExportJob();

    try {
      logger.info("Starting stock import", { jobId: job.id });

      // Delegate to stock import operation
      await importStock(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateLagerstandExportJob(
        job.id,
        {
          completedAt: new Date().toISOString(),
        }
      );

      logger.info("Stock import completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateLagerstandExportJob(
        job.id,
        {
          completedAt: new Date().toISOString(),
          errorMessage,
        }
      );

      logger.error("Stock import failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  /**
   * Start a bidirectional stock synchronization
   */
  public async startStockSync(): Promise<LagerstandExportJob> {
    const job = await databaseService.createLagerstandExportJob();

    try {
      logger.info("Starting bidirectional stock sync", { jobId: job.id });

      // Delegate to bidirectional stock sync operation
      await syncStock(job.id);

      // Mark job as completed
      const completedJob = await databaseService.updateLagerstandExportJob(
        job.id,
        {
          completedAt: new Date().toISOString(),
        }
      );

      logger.info("Bidirectional stock sync completed", { jobId: job.id });
      return completedJob;
    } catch (error) {
      // Mark job as failed
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";
      const failedJob = await databaseService.updateLagerstandExportJob(
        job.id,
        {
          completedAt: new Date().toISOString(),
          errorMessage,
        }
      );

      logger.error("Bidirectional stock sync failed", {
        jobId: job.id,
        error: errorMessage,
      });
      throw error;
    }
  }

  // ===== JOB RETRIEVAL METHODS =====

  /**
   * Get all export jobs
   */
  public async getExportJobs(): Promise<ExportJob[]> {
    return await databaseService.getExportJobs();
  }

  /**
   * Get all import jobs
   */
  public async getImportJobs(): Promise<ImportJob[]> {
    return await databaseService.getImportJobs();
  }

  /**
   * Get all lagerstand export jobs
   */
  public async getLagerstandExportJobs(): Promise<LagerstandExportJob[]> {
    return await databaseService.getLagerstandExportJobs();
  }

  /**
   * Get a specific export job by ID
   */
  public async getExportJob(id: string): Promise<ExportJob | null> {
    return await databaseService.getExportJob(id);
  }

  /**
   * Get a specific import job by ID
   */
  public async getImportJob(id: string): Promise<ImportJob | null> {
    return await databaseService.getImportJob(id);
  }

  /**
   * Get a specific lagerstand export job by ID
   */
  public async getLagerstandExportJob(
    id: string
  ): Promise<LagerstandExportJob | null> {
    return await databaseService.getLagerstandExportJob(id);
  }

  private setupCronJobs(): void {
    const syncInterval = process.env.SYNC_INTERVAL_MINUTES || "30";
    const enableAutoSync = process.env.ENABLE_AUTO_SYNC === "true";

    if (!enableAutoSync) {
      logger.info("Auto sync is disabled");
      return;
    }

    // Product sync job
    this.productSyncJob = new CronJob(
      `0 */${syncInterval} * * * *`, // Every X minutes
      () => this.startProductImport(),
      null,
      false,
      "UTC"
    );

    // Order sync job
    this.orderSyncJob = new CronJob(
      `30 */${syncInterval} * * * *`, // Every X minutes, offset by 30 seconds
      () => this.startOrderExport(),
      null,
      false,
      "UTC"
    );

    // Stock sync job
    this.stockSyncJob = new CronJob(
      `0 */15 * * * *`, // Every 15 minutes
      () => this.startStockSync(),
      null,
      false,
      "UTC"
    );

    logger.info(`Sync jobs configured with ${syncInterval} minute interval`);
  }

  public startAutoSync(): void {
    if (this.productSyncJob && this.orderSyncJob && this.stockSyncJob) {
      this.productSyncJob.start();
      this.orderSyncJob.start();
      this.stockSyncJob.start();
      logger.info("Auto sync started");
    }
  }

  public stopAutoSync(): void {
    if (this.productSyncJob && this.orderSyncJob && this.stockSyncJob) {
      this.productSyncJob.stop();
      this.orderSyncJob.stop();
      this.stockSyncJob.stop();
      logger.info("Auto sync stopped");
    }
  }
}

export const syncService = SyncService.getInstance();
export default syncService;
