/**
 * This is the main entry point for the syncing logic
 * between the server and woocommerce.
 */

import { CronJob } from 'cron';
import { createModuleLogger } from '../infrastructure/logger';
import { wooCommerceService } from '../services/woocommerce';
import { databaseService } from '../infrastructure/database';

const logger = createModuleLogger('sync');

export interface SyncStatus {
  id?: number;
  type: 'products' | 'orders';
  direction: 'inbound' | 'outbound';
  status: 'pending' | 'running' | 'completed' | 'failed';
  started_at: string;
  completed_at?: string;
  records_processed: number;
  records_total: number;
  error_message?: string;
  metadata?: string;
}

export class SyncService {
  private static instance: SyncService;
  private productSyncJob?: CronJob;
  private orderSyncJob?: CronJob;
  private isRunning = false;

  private constructor() {
    this.initializeDatabase();
    this.setupCronJobs();
  }

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  private initializeDatabase(): void {
    const db = databaseService.getDatabase();

    // Create sync_status table
    db.exec(`
      CREATE TABLE IF NOT EXISTS sync_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        direction TEXT NOT NULL,
        status TEXT NOT NULL,
        started_at TEXT NOT NULL,
        completed_at TEXT,
        records_processed INTEGER DEFAULT 0,
        records_total INTEGER DEFAULT 0,
        error_message TEXT,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create sync_log table for detailed logging
    db.exec(`
      CREATE TABLE IF NOT EXISTS sync_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sync_status_id INTEGER,
        level TEXT NOT NULL,
        message TEXT NOT NULL,
        data TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (sync_status_id) REFERENCES sync_status (id)
      )
    `);

    logger.info('Sync database tables initialized');
  }

  private setupCronJobs(): void {
    const syncInterval = process.env.SYNC_INTERVAL_MINUTES || '30';
    const enableAutoSync = process.env.ENABLE_AUTO_SYNC === 'true';

    if (!enableAutoSync) {
      logger.info('Auto sync is disabled');
      return;
    }

    // Product sync job (inbound - from external server to WooCommerce)
    this.productSyncJob = new CronJob(
      `0 */${syncInterval} * * * *`, // Every X minutes
      () => this.syncProductsInbound(),
      null,
      false,
      'UTC'
    );

    // Order sync job (outbound - from WooCommerce to external server)
    this.orderSyncJob = new CronJob(
      `30 */${syncInterval} * * * *`, // Every X minutes, offset by 30 seconds
      () => this.syncOrdersOutbound(),
      null,
      false,
      'UTC'
    );

    logger.info(`Sync jobs configured with ${syncInterval} minute interval`);
  }

  public startAutoSync(): void {
    if (this.productSyncJob && this.orderSyncJob) {
      this.productSyncJob.start();
      this.orderSyncJob.start();
      logger.info('Auto sync started');
    }
  }

  public stopAutoSync(): void {
    if (this.productSyncJob && this.orderSyncJob) {
      this.productSyncJob.stop();
      this.orderSyncJob.stop();
      logger.info('Auto sync stopped');
    }
  }

  public async syncProductsInbound(): Promise<SyncStatus> {
    if (this.isRunning) {
      logger.warn('Sync already running, skipping');
      throw new Error('Sync already in progress');
    }

    const syncStatus = this.createSyncStatus('products', 'inbound');

    try {
      this.isRunning = true;
      logger.info('Starting inbound product sync');

      // TODO: Implement XML fetching from external server
      // TODO: Parse XML and extract product data
      // TODO: Update WooCommerce products via API

      // Placeholder implementation
      await this.delay(1000); // Simulate processing time

      this.updateSyncStatus(syncStatus.id!, {
        status: 'completed',
        completed_at: new Date().toISOString(),
        records_processed: 0,
        records_total: 0
      });

      logger.info('Inbound product sync completed');
      return syncStatus;

    } catch (error) {
      logger.error('Inbound product sync failed:', error);
      this.updateSyncStatus(syncStatus.id!, {
        status: 'failed',
        completed_at: new Date().toISOString(),
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  public async syncOrdersOutbound(): Promise<SyncStatus> {
    if (this.isRunning) {
      logger.warn('Sync already running, skipping');
      throw new Error('Sync already in progress');
    }

    const syncStatus = this.createSyncStatus('orders', 'outbound');

    try {
      this.isRunning = true;
      logger.info('Starting outbound order sync');

      if (!wooCommerceService) {
        throw new Error('WooCommerce service not configured');
      }

      // TODO: Fetch new orders from WooCommerce
      // TODO: Convert orders to XML format
      // TODO: Send XML to external server

      // Placeholder implementation
      await this.delay(1000); // Simulate processing time

      this.updateSyncStatus(syncStatus.id!, {
        status: 'completed',
        completed_at: new Date().toISOString(),
        records_processed: 0,
        records_total: 0
      });

      logger.info('Outbound order sync completed');
      return syncStatus;

    } catch (error) {
      logger.error('Outbound order sync failed:', error);
      this.updateSyncStatus(syncStatus.id!, {
        status: 'failed',
        completed_at: new Date().toISOString(),
        error_message: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  private createSyncStatus(type: 'products' | 'orders', direction: 'inbound' | 'outbound'): SyncStatus {
    const db = databaseService.getDatabase();
    const stmt = db.prepare(`
      INSERT INTO sync_status (type, direction, status, started_at)
      VALUES (?, ?, ?, ?)
    `);

    const syncStatus: SyncStatus = {
      type,
      direction,
      status: 'running',
      started_at: new Date().toISOString(),
      records_processed: 0,
      records_total: 0
    };

    const result = stmt.run(type, direction, 'running', syncStatus.started_at);
    syncStatus.id = result.lastInsertRowid as number;

    return syncStatus;
  }

  private updateSyncStatus(id: number, updates: Partial<SyncStatus>): void {
    const db = databaseService.getDatabase();
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates);

    const stmt = db.prepare(`
      UPDATE sync_status
      SET ${fields}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);

    stmt.run(...values, id);
  }

  public getSyncHistory(limit = 50): SyncStatus[] {
    const db = databaseService.getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM sync_status
      ORDER BY created_at DESC
      LIMIT ?
    `);

    return stmt.all(limit) as SyncStatus[];
  }

  public getSyncStatus(): { isRunning: boolean; lastSync?: SyncStatus } {
    const db = databaseService.getDatabase();
    const stmt = db.prepare(`
      SELECT * FROM sync_status
      ORDER BY created_at DESC
      LIMIT 1
    `);

    const lastSync = stmt.get() as SyncStatus | undefined;

    return {
      isRunning: this.isRunning,
      lastSync
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export const syncService = SyncService.getInstance();
export default syncService;