/**
 * Stock mapping functions for RZA Lagerstand to WooCommerce stock synchronization
 * Handles the 15-minute stock export according to API contract
 * This is a standalone module separate from import/export
 */

import {
  RzaStockArticle,
  WooCommerceStockUpdate,
  StockMappingConfig,
  DEFAULT_STOCK_CONFIG,
  StockValidationResult
} from '../models/stock.model';

/**
 * Maps a single RZA stock article to WooCommerce stock update
 * According to API contract field mapping:
 * - RZA ordernumber -> WooCommerce sku (direct)
 * - RZA instock -> WooCommerce stock_quantity (Max(0, instock))
 * 
 * @param rzaStockArticle - RZA stock article data
 * @param config - Stock mapping configuration
 * @returns WooCommerce stock update object
 */
export function mapRzaStockToWooStock(
  rzaStockArticle: RzaStockArticle,
  config: Partial<StockMappingConfig> = {}
): WooCommerceStockUpdate {
  const mappingConfig = { ...DEFAULT_STOCK_CONFIG, ...config };

  // API Contract: Max(0, instock) - ensure non-negative stock quantity
  const stockQuantity = Math.max(0, rzaStockArticle.instock);
  
  // Determine stock status based on quantity and threshold
  const stockStatus = mappingConfig.updateStockStatus
    ? (stockQuantity > mappingConfig.stockThreshold ? 'instock' : 'outofstock')
    : 'instock'; // Default to instock if not updating status

  return {
    sku: rzaStockArticle.ordernumber, // RZA: ordernumber -> WooCommerce: sku (direct)
    stock_quantity: stockQuantity, // RZA: instock -> WooCommerce: stock_quantity (Max(0, instock))
    stock_status: stockStatus,
    manage_stock: true
  };
}

/**
 * Maps multiple RZA stock articles to WooCommerce stock updates
 * 
 * @param rzaStockArticles - Array of RZA stock articles
 * @param config - Stock mapping configuration
 * @returns Array of WooCommerce stock updates
 */
export function mapRzaStocksToWooStocks(
  rzaStockArticles: RzaStockArticle[],
  config: Partial<StockMappingConfig> = {}
): WooCommerceStockUpdate[] {
  return rzaStockArticles
    .filter(article => shouldIncludeStockArticle(article))
    .map(article => mapRzaStockToWooStock(article, config));
}

/**
 * Maps full RZA article to stock article (for compatibility with export module)
 * Extracts only the fields needed for stock synchronization
 * 
 * @param rzaArticle - Full RZA article from export module
 * @returns RZA stock article with only relevant fields
 */
export function extractStockFromRzaArticle(rzaArticle: any): RzaStockArticle {
  return {
    ordernumber: rzaArticle.ordernumber,
    rzaArtikelID: rzaArticle.rzaArtikelID,
    instock: rzaArticle.instock,
    stock: rzaArticle.stock,
    fields: rzaArticle.fields
  };
}

/**
 * Maps multiple full RZA articles to stock articles
 * 
 * @param rzaArticles - Array of full RZA articles
 * @returns Array of RZA stock articles
 */
export function extractStocksFromRzaArticles(rzaArticles: any[]): RzaStockArticle[] {
  return rzaArticles.map(article => extractStockFromRzaArticle(article));
}

/**
 * Determines if a stock article should be included in the sync
 * 
 * @param article - RZA stock article to check
 * @returns True if article should be included
 */
function shouldIncludeStockArticle(article: RzaStockArticle): boolean {
  // Must have valid order number (SKU)
  if (!article.ordernumber || article.ordernumber.trim() === '') {
    return false;
  }

  // Must have valid RZA article ID
  if (!article.rzaArtikelID || article.rzaArtikelID <= 0) {
    return false;
  }

  // Stock quantity can be 0 or negative (will be normalized to 0)
  return true;
}

/**
 * Validates stock article data according to API contract
 * 
 * @param article - RZA stock article to validate
 * @returns Array of validation errors
 */
export function validateStockArticle(article: RzaStockArticle): string[] {
  const errors: string[] = [];

  // Null/undefined check
  if (!article) {
    return ['Stock article object is null or undefined'];
  }

  // API Contract validation: ordernumber is required
  if (!article.ordernumber || article.ordernumber.trim() === '') {
    errors.push('Order number (ordernumber) is required');
  }

  // API Contract validation: rzaArtikelID is required
  if (!article.rzaArtikelID || article.rzaArtikelID <= 0) {
    errors.push('RZA Article ID (rzaArtikelID) is required and must be positive');
  }

  // API Contract validation: instock must be numeric
  if (typeof article.instock !== 'number') {
    errors.push('Stock quantity (instock) must be a number');
  }

  return errors;
}

/**
 * Validates multiple stock articles
 * 
 * @param articles - Array of RZA stock articles to validate
 * @returns Object with validation results
 */
export function validateStockArticles(articles: RzaStockArticle[]): {
  valid: RzaStockArticle[];
  invalid: Array<{ article: RzaStockArticle; errors: string[] }>;
} {
  const valid: RzaStockArticle[] = [];
  const invalid: Array<{ article: RzaStockArticle; errors: string[] }> = [];

  articles.forEach(article => {
    const errors = validateStockArticle(article);
    if (errors.length === 0) {
      valid.push(article);
    } else {
      invalid.push({ article, errors });
    }
  });

  return { valid, invalid };
}

/**
 * Validates stock article with detailed result
 * 
 * @param article - RZA stock article to validate
 * @returns Detailed validation result
 */
export function validateStockArticleDetailed(article: RzaStockArticle): StockValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Null/undefined check
  if (!article) {
    return {
      isValid: false,
      errors: ['Stock article object is null or undefined'],
      warnings: []
    };
  }

  // API Contract validation: ordernumber is required
  if (!article.ordernumber || article.ordernumber.trim() === '') {
    errors.push('Order number (ordernumber) is required');
  }

  // API Contract validation: rzaArtikelID is required
  if (!article.rzaArtikelID || article.rzaArtikelID <= 0) {
    errors.push('RZA Article ID (rzaArtikelID) is required and must be positive');
  }

  // API Contract validation: instock must be numeric
  if (typeof article.instock !== 'number') {
    errors.push('Stock quantity (instock) must be a number');
  }

  // Warnings for potential issues
  if (article.instock < 0) {
    warnings.push('Negative stock quantity will be normalized to 0');
  }

  if (article.stock !== undefined && article.stock !== article.instock) {
    warnings.push('Stock and instock values differ - using instock for sync');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Filters stock articles to only include those suitable for sync
 * 
 * @param articles - Array of RZA stock articles
 * @returns Filtered array of valid stock articles
 */
export function filterValidStockArticles(articles: RzaStockArticle[]): RzaStockArticle[] {
  return articles.filter(article => shouldIncludeStockArticle(article));
}

/**
 * Calculates stock statistics for reporting
 * 
 * @param articles - Array of RZA stock articles
 * @returns Stock statistics
 */
export function calculateStockStatistics(articles: RzaStockArticle[]): {
  totalArticles: number;
  inStockArticles: number;
  outOfStockArticles: number;
  negativeStockArticles: number;
  averageStock: number;
} {
  const totalArticles = articles.length;
  const inStockArticles = articles.filter(a => a.instock > 0).length;
  const outOfStockArticles = articles.filter(a => a.instock === 0).length;
  const negativeStockArticles = articles.filter(a => a.instock < 0).length;
  const totalStock = articles.reduce((sum, a) => sum + a.instock, 0);
  const averageStock = totalArticles > 0 ? totalStock / totalArticles : 0;

  return {
    totalArticles,
    inStockArticles,
    outOfStockArticles,
    negativeStockArticles,
    averageStock
  };
}
