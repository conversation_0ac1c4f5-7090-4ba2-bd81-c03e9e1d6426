/**
 * Tests for RZA Lagerstand (Stock) module
 * Tests the standalone stock synchronization functionality
 */

import {
  RzaStockArticle,
  WooCommerceStockUpdate,
  mapRzaStockToWooStock,
  mapRzaStocksToWooStocks,
  validateStockArticle,
  validateStockArticles,
  validateStockArticleDetailed,
  calculateStockStatistics,
  filterValidStockArticles,
  DEFAULT_STOCK_CONFIG
} from '../index';

describe('RZA Lagerstand (Stock) Module Tests', () => {
  
  describe('Stock Mapping according to API Contract', () => {
    
    test('should map RZA stock article to WooCommerce stock update', () => {
      const rzaStockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-001',
        rzaArtikelID: 1001,
        instock: 25,
        stock: 30
      };

      const stockUpdate = mapRzaStockToWooStock(rzaStockArticle);

      // API Contract field mappings verification
      expect(stockUpdate.sku).toBe('STOCK-001'); // ordernumber -> sku (direct)
      expect(stockUpdate.stock_quantity).toBe(25); // instock -> stock_quantity (Max(0, instock))
      expect(stockUpdate.manage_stock).toBe(true);
      expect(stockUpdate.stock_status).toBe('instock');
    });

    test('should handle negative stock according to API contract', () => {
      const rzaStockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-002',
        rzaArtikelID: 1002,
        instock: -10
      };

      const stockUpdate = mapRzaStockToWooStock(rzaStockArticle);

      // API Contract: Max(0, instock) - ensure non-negative stock quantity
      expect(stockUpdate.stock_quantity).toBe(0);
      expect(stockUpdate.stock_status).toBe('outofstock');
    });

    test('should map multiple stock articles', () => {
      const rzaStockArticles: RzaStockArticle[] = [
        { ordernumber: 'STOCK-001', rzaArtikelID: 1001, instock: 25 },
        { ordernumber: 'STOCK-002', rzaArtikelID: 1002, instock: 0 },
        { ordernumber: 'STOCK-003', rzaArtikelID: 1003, instock: -5 }
      ];

      const stockUpdates = mapRzaStocksToWooStocks(rzaStockArticles);

      expect(stockUpdates).toHaveLength(3);
      expect(stockUpdates[0].stock_quantity).toBe(25);
      expect(stockUpdates[1].stock_quantity).toBe(0);
      expect(stockUpdates[2].stock_quantity).toBe(0); // Negative normalized to 0
    });

    test('should apply stock threshold configuration', () => {
      const rzaStockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-001',
        rzaArtikelID: 1001,
        instock: 3
      };

      const stockUpdate = mapRzaStockToWooStock(rzaStockArticle, { stockThreshold: 5 });

      expect(stockUpdate.stock_quantity).toBe(3);
      expect(stockUpdate.stock_status).toBe('outofstock'); // Below threshold
    });
  });

  describe('Stock Validation according to API Contract', () => {
    
    test('should validate required fields', () => {
      const invalidStockArticle: Partial<RzaStockArticle> = {
        // Missing ordernumber and rzaArtikelID
        instock: 25
      };

      const errors = validateStockArticle(invalidStockArticle as RzaStockArticle);

      expect(errors).toContain('Order number (ordernumber) is required');
      expect(errors).toContain('RZA Article ID (rzaArtikelID) is required and must be positive');
    });

    test('should validate data types', () => {
      const invalidStockArticle = {
        ordernumber: 'STOCK-001',
        rzaArtikelID: 1001,
        instock: 'invalid' // Should be number
      };

      const errors = validateStockArticle(invalidStockArticle as any);

      expect(errors).toContain('Stock quantity (instock) must be a number');
    });

    test('should validate multiple stock articles', () => {
      const stockArticles: RzaStockArticle[] = [
        { ordernumber: 'STOCK-001', rzaArtikelID: 1001, instock: 25 }, // Valid
        { ordernumber: '', rzaArtikelID: 1002, instock: 10 }, // Invalid: empty ordernumber
        { ordernumber: 'STOCK-003', rzaArtikelID: 0, instock: 5 } // Invalid: rzaArtikelID <= 0
      ];

      const validation = validateStockArticles(stockArticles);

      expect(validation.valid).toHaveLength(1);
      expect(validation.invalid).toHaveLength(2);
      expect(validation.valid[0].ordernumber).toBe('STOCK-001');
    });

    test('should provide detailed validation with warnings', () => {
      const stockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-001',
        rzaArtikelID: 1001,
        instock: -5,
        stock: 10 // Different from instock
      };

      const validation = validateStockArticleDetailed(stockArticle);

      expect(validation.isValid).toBe(true);
      expect(validation.warnings).toContain('Negative stock quantity will be normalized to 0');
      expect(validation.warnings).toContain('Stock and instock values differ - using instock for sync');
    });

    test('should handle null/undefined articles', () => {
      const errors = validateStockArticle(null as any);
      expect(errors).toContain('Stock article object is null or undefined');

      const detailedValidation = validateStockArticleDetailed(undefined as any);
      expect(detailedValidation.isValid).toBe(false);
      expect(detailedValidation.errors).toContain('Stock article object is null or undefined');
    });
  });

  describe('Stock Statistics and Filtering', () => {
    
    test('should calculate stock statistics', () => {
      const stockArticles: RzaStockArticle[] = [
        { ordernumber: 'STOCK-001', rzaArtikelID: 1001, instock: 25 },
        { ordernumber: 'STOCK-002', rzaArtikelID: 1002, instock: 0 },
        { ordernumber: 'STOCK-003', rzaArtikelID: 1003, instock: -5 },
        { ordernumber: 'STOCK-004', rzaArtikelID: 1004, instock: 15 }
      ];

      const stats = calculateStockStatistics(stockArticles);

      expect(stats.totalArticles).toBe(4);
      expect(stats.inStockArticles).toBe(2); // 25 and 15
      expect(stats.outOfStockArticles).toBe(1); // 0
      expect(stats.negativeStockArticles).toBe(1); // -5
      expect(stats.averageStock).toBe(8.75); // (25 + 0 + (-5) + 15) / 4
    });

    test('should filter valid stock articles', () => {
      const stockArticles: RzaStockArticle[] = [
        { ordernumber: 'STOCK-001', rzaArtikelID: 1001, instock: 25 }, // Valid
        { ordernumber: '', rzaArtikelID: 1002, instock: 10 }, // Invalid: empty ordernumber
        { ordernumber: 'STOCK-003', rzaArtikelID: 0, instock: 5 }, // Invalid: rzaArtikelID <= 0
        { ordernumber: 'STOCK-004', rzaArtikelID: 1004, instock: 15 } // Valid
      ];

      const validArticles = filterValidStockArticles(stockArticles);

      expect(validArticles).toHaveLength(2);
      expect(validArticles[0].ordernumber).toBe('STOCK-001');
      expect(validArticles[1].ordernumber).toBe('STOCK-004');
    });
  });

  describe('Configuration and Defaults', () => {
    
    test('should use default configuration', () => {
      const rzaStockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-001',
        rzaArtikelID: 1001,
        instock: 5
      };

      const stockUpdate = mapRzaStockToWooStock(rzaStockArticle);

      expect(stockUpdate.stock_status).toBe('instock'); // Default threshold is 0
      expect(stockUpdate.manage_stock).toBe(true);
    });

    test('should apply custom configuration', () => {
      const rzaStockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-001',
        rzaArtikelID: 1001,
        instock: 3
      };

      const customConfig = {
        stockThreshold: 5,
        updateStockStatus: false
      };

      const stockUpdate = mapRzaStockToWooStock(rzaStockArticle, customConfig);

      expect(stockUpdate.stock_quantity).toBe(3);
      expect(stockUpdate.stock_status).toBe('instock'); // updateStockStatus is false
    });

    test('should verify default configuration values', () => {
      expect(DEFAULT_STOCK_CONFIG.stockThreshold).toBe(0);
      expect(DEFAULT_STOCK_CONFIG.updateStockStatus).toBe(true);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    
    test('should handle empty stock articles array', () => {
      const stockUpdates = mapRzaStocksToWooStocks([]);
      expect(stockUpdates).toHaveLength(0);

      const validation = validateStockArticles([]);
      expect(validation.valid).toHaveLength(0);
      expect(validation.invalid).toHaveLength(0);

      const stats = calculateStockStatistics([]);
      expect(stats.totalArticles).toBe(0);
      expect(stats.averageStock).toBe(0);
    });

    test('should handle very large stock quantities', () => {
      const rzaStockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-001',
        rzaArtikelID: 1001,
        instock: 999999
      };

      const stockUpdate = mapRzaStockToWooStock(rzaStockArticle);

      expect(stockUpdate.stock_quantity).toBe(999999);
      expect(stockUpdate.stock_status).toBe('instock');
    });

    test('should handle special characters in ordernumber', () => {
      const rzaStockArticle: RzaStockArticle = {
        ordernumber: 'STOCK-001-ÄÖÜ-€',
        rzaArtikelID: 1001,
        instock: 25
      };

      const stockUpdate = mapRzaStockToWooStock(rzaStockArticle);

      expect(stockUpdate.sku).toBe('STOCK-001-ÄÖÜ-€');
      expect(stockUpdate.stock_quantity).toBe(25);
    });
  });
});
