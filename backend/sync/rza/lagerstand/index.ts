/**
 * RZA Lagerstand (Stock) Module Index
 * Standalone module for 15-minute stock synchronization
 * Separate from import/export functionality
 */

// === MODELS ===
export * from './models/stock.model';

// === MAPPERS ===
export * from './mappers/stock.mapper';

// === SERVICES ===
export * from './stockService';

// === TYPE DEFINITIONS ===
// Re-export key interfaces for convenience
export type {
  RzaStockArticle,
  WooCommerceStockUpdate,
  StockMappingConfig,
  StockExportConfig,
  StockExportResult,
  RzaLagerstand,
  StockValidationResult,
  BatchStockValidationResult
} from './models/stock.model';

// === MAIN FUNCTIONS ===
// Re-export main functions for easy access
export {
  mapRzaStockToWooStock,
  mapRzaStocksToWooStocks,
  extractStockFromRzaArticle,
  extractStocksFromRzaArticles,
  validateStockArticle,
  validateStockArticles,
  validateStockArticleDetailed,
  filterValidStockArticles,
  calculateStockStatistics
} from './mappers/stock.mapper';

export {
  RzaStockService,
  exportRzaStock
} from './stockService';

// === DEFAULT CONFIGURATIONS ===
export {
  DEFAULT_STOCK_CONFIG,
  DEFAULT_STOCK_EXPORT_CONFIG
} from './models/stock.model';

// === API CONTRACT COMPLIANCE ===
/**
 * This module implements the RZA Lagerstand (Stock) synchronization according to the API contract:
 * 
 * 1. 15-minute Stock Sync (lagerstand_export):
 *    - Maps only ordernumber, rzaArtikelID, instock fields
 *    - Optimized for frequent stock updates
 *    - Standalone from full product sync
 * 
 * 2. Field mappings as specified in API contract:
 *    - ordernumber -> sku (direct)
 *    - rzaArtikelID -> reference (for tracking)
 *    - instock -> stock_quantity (Max(0, instock))
 * 
 * 3. Validation according to API contract:
 *    - ordernumber is required
 *    - rzaArtikelID is required and must be positive
 *    - instock must be numeric
 * 
 * 4. Performance optimizations:
 *    - Minimal data processing
 *    - Fast XML parsing
 *    - Efficient validation
 *    - Batch processing support
 */
