/**
 * Stock/Lagerstand model types for RZA stock synchronization
 * This is a standalone module for 15-minute stock updates
 */

/**
 * RZA stock article interface for lagerstand export
 * According to API contract, only these fields are needed for stock sync
 */
export interface RzaStockArticle {
  ordernumber: string; // RZA: ordernumber -> WooCommerce: sku (direct)
  rzaArtikelID: number; // RZA: rzaArtikelID (for reference)
  instock: number; // RZA: instock -> WooCommerce: stock_quantity (Max(0, instock))
  stock?: number; // RZA: stock (actual stock level, optional)
  fields?: Array<{ fieldnumber: number; value: string }>; // RZA: fields (optional, may be present but not used)
}

/**
 * WooCommerce stock update interface
 * Minimal interface for stock-only updates
 */
export interface WooCommerceStockUpdate {
  sku: string; // Product SKU to identify the product
  stock_quantity: number; // New stock quantity
  stock_status: 'instock' | 'outofstock' | 'onbackorder'; // Stock status
  manage_stock: boolean; // Always true for stock management
}

/**
 * Configuration for stock mapping
 */
export interface StockMappingConfig {
  stockThreshold: number; // Minimum stock to consider in stock (default: 0)
  updateStockStatus: boolean; // Whether to update stock status based on quantity (default: true)
}

/**
 * Default configuration for stock mapping
 */
export const DEFAULT_STOCK_CONFIG: StockMappingConfig = {
  stockThreshold: 0,
  updateStockStatus: true
};

/**
 * Stock export result interface
 */
export interface StockExportResult {
  success: boolean;
  message: string;
  stats: {
    stockUpdatesProcessed: number;
    errors: string[];
  };
  outputFiles: string[];
}

/**
 * Configuration for stock export service
 */
export interface StockExportConfig {
  // File paths
  xmlFilePath: string;
  outputDirectory?: string;
  
  // Mapping configuration
  stockConfig?: Partial<StockMappingConfig>;
  
  // Output options
  generateJsonFiles: boolean;
  generateCsvFiles: boolean;
  logProgress: boolean;
}

/**
 * Default stock export configuration
 */
export const DEFAULT_STOCK_EXPORT_CONFIG: StockExportConfig = {
  xmlFilePath: '',
  outputDirectory: './tmp/rza-lagerstand',
  generateJsonFiles: true,
  generateCsvFiles: false,
  logProgress: true
};

/**
 * RZA Lagerstand XML structure (simplified for stock updates)
 */
export interface RzaLagerstand {
  articles: RzaStockArticle[];
}

/**
 * Stock validation result interface
 */
export interface StockValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Batch stock validation result
 */
export interface BatchStockValidationResult {
  valid: RzaStockArticle[];
  invalid: Array<{ article: RzaStockArticle; errors: string[] }>;
  totalErrors: number;
  totalWarnings: number;
}
