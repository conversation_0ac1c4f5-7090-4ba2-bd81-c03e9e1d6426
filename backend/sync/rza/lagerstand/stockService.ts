/**
 * RZA Lagerstand (Stock) Service
 * Standalone service for 15-minute stock synchronization
 * Separate from import/export functionality
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { parseStringPromise } from 'xml2js';

import {
  RzaStockArticle,
  WooCommerceStockUpdate,
  StockExportConfig,
  StockExportResult,
  DEFAULT_STOCK_EXPORT_CONFIG,
  RzaLagerstand
} from './models/stock.model';

import {
  mapRzaStocksToWooStocks,
  validateStockArticles,
  calculateStockStatistics,
  filterValidStockArticles
} from './mappers/stock.mapper';

/**
 * RZA Stock Service for handling lagerstand (stock) synchronization
 * This is a standalone service focused only on stock updates
 */
export class RzaStockService {
  private config: StockExportConfig;

  constructor(config: Partial<StockExportConfig>) {
    this.config = { ...DEFAULT_STOCK_EXPORT_CONFIG, ...config };
  }

  /**
   * Main method to export stock data to WooCommerce format
   *
   * @returns Stock export result
   */
  async exportStockToWooCommerce(): Promise<StockExportResult> {
    const result: StockExportResult = {
      success: false,
      message: '',
      stats: {
        stockUpdatesProcessed: 0,
        errors: []
      },
      outputFiles: []
    };

    try {
      this.log('Starting RZA stock export...');

      // Parse RZA stock XML
      const rzaStockData = await this.parseRzaStockXml(this.config.xmlFilePath);
      this.log(`Parsed ${rzaStockData.articles.length} stock articles from XML`);

      // Process stock updates
      const stockResult = await this.processStockUpdates(rzaStockData.articles);
      result.stats.stockUpdatesProcessed = stockResult.count;
      result.outputFiles.push(...stockResult.files);
      result.stats.errors.push(...stockResult.errors);

      // Set result status
      result.success = result.stats.errors.length === 0;
      result.message = result.success
        ? `Successfully processed ${result.stats.stockUpdatesProcessed} stock updates`
        : `Processed ${result.stats.stockUpdatesProcessed} stock updates with ${result.stats.errors.length} errors`;

      this.log(result.message);

      if (result.stats.errors.length > 0) {
        this.log(`Encountered ${result.stats.errors.length} errors during stock export.`);
      }

    } catch (error) {
      result.success = false;
      result.message = `Stock export failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.stats.errors.push(result.message);
      this.log(result.message);
    }

    return result;
  }

  /**
   * Processes stock updates from RZA articles
   *
   * @param articles - Array of RZA stock articles
   * @returns Processing result
   */
  private async processStockUpdates(
    articles: RzaStockArticle[]
  ): Promise<{ count: number; files: string[]; errors: string[] }> {
    const result = { count: 0, files: [], errors: [] };

    try {
      // Validate stock articles
      const validation = validateStockArticles(articles);
      if (validation.invalid.length > 0) {
        validation.invalid.forEach(({ article, errors }) => {
          result.errors.push(`Stock validation failed for ${article.ordernumber}: ${errors.join(', ')}`);
        });
      }

      // Filter valid articles
      const validArticles = filterValidStockArticles(validation.valid);

      // Map to WooCommerce format
      const wooStockUpdates = mapRzaStocksToWooStocks(validArticles, this.config.stockConfig);
      result.count = wooStockUpdates.length;

      // Generate statistics
      const stats = calculateStockStatistics(validArticles);
      this.log(`Stock statistics: ${stats.totalArticles} total, ${stats.inStockArticles} in stock, ${stats.outOfStockArticles} out of stock`);

      // Create output directory
      if (this.config.outputDirectory) {
        await this.ensureDirectoryExists(this.config.outputDirectory);
      }

      // Generate JSON output
      if (this.config.generateJsonFiles && this.config.outputDirectory) {
        const jsonFilePath = path.join(this.config.outputDirectory, 'woocommerce-stock-updates.json');
        await fs.writeFile(jsonFilePath, JSON.stringify(wooStockUpdates, null, 2));
        result.files.push(jsonFilePath);

        // Also save statistics
        const statsFilePath = path.join(this.config.outputDirectory, 'stock-statistics.json');
        await fs.writeFile(statsFilePath, JSON.stringify(stats, null, 2));
        result.files.push(statsFilePath);
      }

      // Generate CSV output if requested
      if (this.config.generateCsvFiles && this.config.outputDirectory) {
        const csvFilePath = path.join(this.config.outputDirectory, 'woocommerce-stock-updates.csv');
        const csvContent = this.generateStockCsv(wooStockUpdates);
        await fs.writeFile(csvFilePath, csvContent);
        result.files.push(csvFilePath);
      }

    } catch (error) {
      result.errors.push(`Stock processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Parses RZA stock XML file
   *
   * @param xmlFilePath - Path to RZA stock XML file
   * @returns Parsed RZA stock data
   */
  private async parseRzaStockXml(xmlFilePath: string): Promise<RzaLagerstand> {
    try {
      const xmlContent = await fs.readFile(xmlFilePath, 'utf-8');
      const parsedXml = await parseStringPromise(xmlContent);

      // Extract stock articles from XML structure
      // This will need to be adapted based on the actual XML structure
      const articles: RzaStockArticle[] = [];

      // TODO: Implement actual XML parsing based on lagerstand.example.xml structure
      // For now, return empty structure

      return { articles };
    } catch (error) {
      throw new Error(`Failed to parse RZA stock XML: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generates CSV content for stock updates
   *
   * @param stockUpdates - Array of WooCommerce stock updates
   * @returns CSV content string
   */
  private generateStockCsv(stockUpdates: WooCommerceStockUpdate[]): string {
    const headers = ['sku', 'stock_quantity', 'stock_status', 'manage_stock'];
    const rows = stockUpdates.map(update => [
      update.sku,
      update.stock_quantity.toString(),
      update.stock_status,
      update.manage_stock.toString()
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * Ensures directory exists, creates if not
   *
   * @param dirPath - Directory path to ensure
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * Logs message if logging is enabled
   *
   * @param message - Message to log
   */
  private log(message: string): void {
    if (this.config.logProgress) {
      console.log(`[RZA Stock Service] ${message}`);
    }
  }
}

/**
 * Convenience function to create and run stock export
 *
 * @param config - Stock export configuration
 * @returns Stock export result
 */
export async function exportRzaStock(config: Partial<StockExportConfig>): Promise<StockExportResult> {
  const service = new RzaStockService(config);
  return await service.exportStockToWooCommerce();
}

  /**
   * Main method to export stock data to WooCommerce format
   * 
   * @returns Stock export result
   */
  async exportStockToWooCommerce(): Promise<StockExportResult> {
    const result: StockExportResult = {
      success: false,
      message: '',
      stats: {
        stockUpdatesProcessed: 0,
        errors: []
      },
      outputFiles: []
    };

    try {
      this.log('Starting RZA stock export...');

      // Parse RZA stock XML
      const rzaStockData = await this.parseRzaStockXml(this.config.xmlFilePath);
      this.log(`Parsed ${rzaStockData.articles.length} stock articles from XML`);

      // Process stock updates
      const stockResult = await this.processStockUpdates(rzaStockData.articles);
      result.stats.stockUpdatesProcessed = stockResult.count;
      result.outputFiles.push(...stockResult.files);
      result.stats.errors.push(...stockResult.errors);

      // Set result status
      result.success = result.stats.errors.length === 0;
      result.message = result.success 
        ? `Successfully processed ${result.stats.stockUpdatesProcessed} stock updates`
        : `Processed ${result.stats.stockUpdatesProcessed} stock updates with ${result.stats.errors.length} errors`;

      this.log(result.message);
      
      if (result.stats.errors.length > 0) {
        this.log(`Encountered ${result.stats.errors.length} errors during stock export.`);
      }

    } catch (error) {
      result.success = false;
      result.message = `Stock export failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.stats.errors.push(result.message);
      this.log(result.message);
    }

    return result;
  }

  /**
   * Processes stock updates from RZA articles
   * 
   * @param articles - Array of RZA stock articles
   * @returns Processing result
   */
  private async processStockUpdates(
    articles: RzaStockArticle[]
  ): Promise<{ count: number; files: string[]; errors: string[] }> {
    const result = { count: 0, files: [], errors: [] };

    try {
      // Validate stock articles
      const validation = validateStockArticles(articles);
      if (validation.invalid.length > 0) {
        validation.invalid.forEach(({ article, errors }) => {
          result.errors.push(`Stock validation failed for ${article.ordernumber}: ${errors.join(', ')}`);
        });
      }

      // Filter valid articles
      const validArticles = filterValidStockArticles(validation.valid);
      
      // Map to WooCommerce format
      const wooStockUpdates = mapRzaStocksToWooStocks(validArticles, this.config.stockConfig);
      result.count = wooStockUpdates.length;

      // Generate statistics
      const stats = calculateStockStatistics(validArticles);
      this.log(`Stock statistics: ${stats.totalArticles} total, ${stats.inStockArticles} in stock, ${stats.outOfStockArticles} out of stock`);

      // Create output directory
      if (this.config.outputDirectory) {
        await this.ensureDirectoryExists(this.config.outputDirectory);
      }

      // Generate JSON output
      if (this.config.generateJsonFiles && this.config.outputDirectory) {
        const jsonFilePath = path.join(this.config.outputDirectory, 'woocommerce-stock-updates.json');
        await fs.writeFile(jsonFilePath, JSON.stringify(wooStockUpdates, null, 2));
        result.files.push(jsonFilePath);

        // Also save statistics
        const statsFilePath = path.join(this.config.outputDirectory, 'stock-statistics.json');
        await fs.writeFile(statsFilePath, JSON.stringify(stats, null, 2));
        result.files.push(statsFilePath);
      }

      // Generate CSV output if requested
      if (this.config.generateCsvFiles && this.config.outputDirectory) {
        const csvFilePath = path.join(this.config.outputDirectory, 'woocommerce-stock-updates.csv');
        const csvContent = this.generateStockCsv(wooStockUpdates);
        await fs.writeFile(csvFilePath, csvContent);
        result.files.push(csvFilePath);
      }

    } catch (error) {
      result.errors.push(`Stock processing error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Parses RZA stock XML file
   * 
   * @param xmlFilePath - Path to RZA stock XML file
   * @returns Parsed RZA stock data
   */
  private async parseRzaStockXml(xmlFilePath: string): Promise<RzaLagerstand> {
    try {
      const xmlContent = await fs.readFile(xmlFilePath, 'utf-8');
      const parsedXml = await parseStringPromise(xmlContent);

      // Extract stock articles from XML structure
      // This will need to be adapted based on the actual XML structure
      const articles: RzaStockArticle[] = [];
      
      // TODO: Implement actual XML parsing based on lagerstand.example.xml structure
      // For now, return empty structure
      
      return { articles };
    } catch (error) {
      throw new Error(`Failed to parse RZA stock XML: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generates CSV content for stock updates
   * 
   * @param stockUpdates - Array of WooCommerce stock updates
   * @returns CSV content string
   */
  private generateStockCsv(stockUpdates: WooCommerceStockUpdate[]): string {
    const headers = ['sku', 'stock_quantity', 'stock_status', 'manage_stock'];
    const rows = stockUpdates.map(update => [
      update.sku,
      update.stock_quantity.toString(),
      update.stock_status,
      update.manage_stock.toString()
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }

  /**
   * Ensures directory exists, creates if not
   * 
   * @param dirPath - Directory path to ensure
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  /**
   * Logs message if logging is enabled
   * 
   * @param message - Message to log
   */
  private log(message: string): void {
    if (this.config.logProgress) {
      console.log(`[RZA Stock Service] ${message}`);
    }
  }
}

/**
 * Convenience function to create and run stock export
 * 
 * @param config - Stock export configuration
 * @returns Stock export result
 */
export async function exportRzaStock(config: Partial<StockExportConfig>): Promise<StockExportResult> {
  const service = new RzaStockService(config);
  return await service.exportStockToWooCommerce();
}

/**
 * Default export configuration for common use cases
 */
export const STOCK_EXPORT_CONFIGS = {
  development: {
    ...DEFAULT_STOCK_EXPORT_CONFIG,
    outputDirectory: './tmp/rza-lagerstand-dev',
    logProgress: true
  },
  production: {
    ...DEFAULT_STOCK_EXPORT_CONFIG,
    outputDirectory: './tmp/rza-lagerstand-prod',
    logProgress: false,
    generateCsvFiles: true
  }
};
