# RZA Lagerstand (Stock) Module

This is a **standalone module** for RZA stock synchronization, completely separate from the import and export functionality. It handles the 15-minute stock updates according to the API contract specifications.

## 📋 API Contract Compliance

This module implements the exact specifications defined in `API_Contract.md` for the **15-minute stock synchronization**:

### Stock Field Mappings (API Contract)
- **Order Number**: `ordernumber` → `sku` (direct mapping)
- **Stock Quantity**: `instock` → `stock_quantity` (Max(0, instock) to ensure non-negative)
- **Article Reference**: `rzaArtikelID` (for tracking and validation)

### Validation Rules (API Contract)
- **Required Fields**: ordernumber, rzaArtikelID must be present
- **Data Types**: instock must be numeric
- **Business Rules**: rzaArtikelID must be positive integer

### Performance Optimizations
- **Minimal Data**: Only processes essential stock fields
- **Fast Processing**: Optimized for frequent 15-minute updates
- **Batch Support**: Handles multiple stock updates efficiently

## 🏗️ Architecture

The module follows the same structure as import/export modules:

```
backend/sync/rza/lagerstand/
├── models/                    # TypeScript model definitions
│   └── stock.model.ts        # Stock-specific models and configurations
├── mappers/                  # Stock mapping functions
│   └── stock.mapper.ts       # RZA stock to WooCommerce mapping
├── tests/                    # Unit and integration tests
│   └── stock.test.ts         # Comprehensive stock module tests
├── stockService.ts           # Main stock synchronization service
├── index.ts                  # Module exports
├── README.md                 # This documentation
└── lagerstand.example.xml    # Example RZA stock XML structure
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { RzaStockService, DEFAULT_STOCK_EXPORT_CONFIG } from './lagerstand';

// Create stock service
const stockService = new RzaStockService({
  ...DEFAULT_STOCK_EXPORT_CONFIG,
  xmlFilePath: 'path/to/rza-lagerstand.xml',
  outputDirectory: './tmp/stock-updates'
});

// Export stock updates
const result = await stockService.exportStockToWooCommerce();
console.log(`Processed ${result.stats.stockUpdatesProcessed} stock updates`);
```

### Convenience Function

```typescript
import { exportRzaStock } from './lagerstand';

// Quick export
const result = await exportRzaStock({
  xmlFilePath: 'path/to/rza-lagerstand.xml',
  outputDirectory: './tmp/stock-updates',
  generateJsonFiles: true,
  logProgress: true
});
```

### Manual Mapping

```typescript
import { 
  mapRzaStockToWooStock, 
  mapRzaStocksToWooStocks,
  validateStockArticles 
} from './lagerstand';

// Map single stock article
const stockUpdate = mapRzaStockToWooStock({
  ordernumber: 'ARTICLE-001',
  rzaArtikelID: 1001,
  instock: 25
});

// Map multiple articles with validation
const stockArticles = [/* ... */];
const validation = validateStockArticles(stockArticles);
const stockUpdates = mapRzaStocksToWooStocks(validation.valid);
```

## ⚙️ Configuration

### Stock Export Configuration

```typescript
interface StockExportConfig {
  // File paths
  xmlFilePath: string;                    // Path to RZA lagerstand XML
  outputDirectory?: string;               // Output directory for results
  
  // Mapping configuration
  stockConfig?: Partial<StockMappingConfig>;
  
  // Output options
  generateJsonFiles: boolean;             // Generate JSON output
  generateCsvFiles: boolean;              // Generate CSV output
  logProgress: boolean;                   // Enable logging
}
```

### Stock Mapping Configuration

```typescript
interface StockMappingConfig {
  stockThreshold: number;                 // Minimum stock for 'instock' status
  updateStockStatus: boolean;             // Update stock status based on quantity
}
```

### Default Configurations

```typescript
// Default stock mapping
const DEFAULT_STOCK_CONFIG = {
  stockThreshold: 0,
  updateStockStatus: true
};

// Default export configuration
const DEFAULT_STOCK_EXPORT_CONFIG = {
  xmlFilePath: '',
  outputDirectory: './tmp/rza-lagerstand',
  generateJsonFiles: true,
  generateCsvFiles: false,
  logProgress: true
};
```

## 📊 Output Formats

### JSON Output
```json
[
  {
    "sku": "ARTICLE-001",
    "stock_quantity": 25,
    "stock_status": "instock",
    "manage_stock": true
  }
]
```

### CSV Output
```csv
sku,stock_quantity,stock_status,manage_stock
ARTICLE-001,25,instock,true
ARTICLE-002,0,outofstock,true
```

### Statistics Output
```json
{
  "totalArticles": 100,
  "inStockArticles": 75,
  "outOfStockArticles": 20,
  "negativeStockArticles": 5,
  "averageStock": 12.5
}
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
npm test -- lagerstand/tests/stock.test.ts
```

The tests cover:
- API contract compliance
- Field mapping accuracy
- Validation rules
- Edge cases and error handling
- Performance scenarios

## 🔧 API Reference

### Main Functions

#### `mapRzaStockToWooStock(article, config?)`
Maps a single RZA stock article to WooCommerce stock update.

#### `mapRzaStocksToWooStocks(articles, config?)`
Maps multiple RZA stock articles to WooCommerce stock updates.

#### `validateStockArticle(article)`
Validates a single stock article according to API contract rules.

#### `validateStockArticles(articles)`
Validates multiple stock articles and returns valid/invalid separation.

#### `calculateStockStatistics(articles)`
Calculates comprehensive statistics for stock articles.

### Service Class

#### `RzaStockService`
Main service class for stock synchronization.

**Methods:**
- `exportStockToWooCommerce()`: Main export method
- `constructor(config)`: Initialize with configuration

## 🚨 Error Handling

The module provides comprehensive error handling:

- **Validation Errors**: Detailed field-level validation messages
- **Processing Errors**: Graceful handling of XML parsing and mapping errors
- **File Errors**: Proper handling of file system operations
- **Configuration Errors**: Clear messages for configuration issues

## 📈 Performance

Optimized for frequent 15-minute updates:

- **Minimal Memory Usage**: Only loads essential stock data
- **Fast Processing**: Streamlined mapping and validation
- **Batch Operations**: Efficient handling of large stock lists
- **Error Recovery**: Continues processing even with individual item errors

## 🔗 Integration

This module is designed to integrate with:

- **WooCommerce REST API**: Direct stock updates
- **Cron Jobs**: Scheduled 15-minute synchronization
- **Monitoring Systems**: Comprehensive logging and statistics
- **Error Reporting**: Detailed error tracking and reporting

## 📝 Notes

- This module is **completely standalone** and does not depend on import/export modules
- Stock quantities are always normalized to non-negative values (Max(0, instock))
- The module follows the exact API contract specifications for field mappings
- All validation rules match the business requirements defined in the API contract
