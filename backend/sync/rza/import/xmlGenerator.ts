/**
 * XML generator for RZA import format
 * Converts RZA data structures to XML format
 */

import { create } from 'xmlbuilder2';
import {
  RzaTransaktion,
  RzaDokument,
  RzaAdresse,
  RzaPosition,
  RzaSummen,
  RzaSteuersaetze,
  RzaImportData
} from './types';

/**
 * Generates XML string from RZA import data
 * 
 * @param importData - RZA import data structure
 * @returns XML string
 */
export function generateRzaImportXML(importData: RzaImportData): string {
  const { transaktion } = importData;

  // Create XML document with proper namespace and schema
  const root = create({ version: '1.0', encoding: 'UTF-8' })
    .ele('rza:Transaktion', {
      'xmlns:rza': 'https://www.rza.at/XML/Fakt/',
      'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
      'xsi:schemaLocation': 'https://www.rza.at/XML/Fakt/ Transaktion.xsd',
      'Erstellungsdatum': transaktion.Erstellungsdatum,
      'Modus': transaktion.Modus,
      'Verarbeitung': transaktion.Verarbeitung
    });

  // Add ErstelltVon if provided
  if (transaktion.ErstelltVon) {
    root.att('ErstelltVon', transaktion.ErstelltVon);
  }

  // Add sender (Absender)
  addAbsenderToXML(root, transaktion.Absender);

  // Add documents
  transaktion.Dokument.forEach(dokument => {
    addDokumentToXML(root, dokument);
  });

  // Add addresses
  if (transaktion.Adresse && transaktion.Adresse.length > 0) {
    transaktion.Adresse.forEach(adresse => {
      addAdresseToXML(root, adresse);
    });
  }

  return root.end({ prettyPrint: true });
}

/**
 * Adds sender information to XML
 * 
 * @param parent - Parent XML element
 * @param absender - Sender data
 */
function addAbsenderToXML(parent: any, absender: any): void {
  const absenderEl = parent.ele('rza:Absender', {
    'Name': absender.Name
  });

  // Add optional attributes
  if (absender.Name2) absenderEl.att('Name2', absender.Name2);
  if (absender.Strasse) absenderEl.att('Strasse', absender.Strasse);
  if (absender.PLZ) absenderEl.att('PLZ', absender.PLZ);
  if (absender.Ort) absenderEl.att('Ort', absender.Ort);
  if (absender.Land) absenderEl.att('Land', absender.Land);
  if (absender.UIDNummer) absenderEl.att('UIDNummer', absender.UIDNummer);
  if (absender.GLN) absenderEl.att('GLN', absender.GLN);

  // Add contact information if provided
  if (absender.Kontakt) {
    const kontaktEl = absenderEl.ele('rza:Kontakt');
    if (absender.Kontakt.Telefon) kontaktEl.att('Telefon', absender.Kontakt.Telefon);
    if (absender.Kontakt.Fax) kontaktEl.att('Fax', absender.Kontakt.Fax);
    if (absender.Kontakt.Email) kontaktEl.att('Email', absender.Kontakt.Email);
    if (absender.Kontakt.Ansprechpartner) kontaktEl.att('Ansprechpartner', absender.Kontakt.Ansprechpartner);
  }

  // Add header lines if provided
  if (absender.Kopfzeilen) {
    const kopfzeilenEl = absenderEl.ele('rza:Kopfzeilen');
    if (absender.Kopfzeilen.Zeile1) kopfzeilenEl.ele('rza:Zeile1').txt(absender.Kopfzeilen.Zeile1);
    if (absender.Kopfzeilen.Zeile2) kopfzeilenEl.ele('rza:Zeile2').txt(absender.Kopfzeilen.Zeile2);
    if (absender.Kopfzeilen.Zeile3) kopfzeilenEl.ele('rza:Zeile3').txt(absender.Kopfzeilen.Zeile3);
  }

  // Add footer lines if provided
  if (absender.Fusszeilen) {
    const fusszeilenEl = absenderEl.ele('rza:Fusszeilen');
    if (absender.Fusszeilen.Zeile1) fusszeilenEl.ele('rza:Zeile1').txt(absender.Fusszeilen.Zeile1);
    if (absender.Fusszeilen.Zeile2) fusszeilenEl.ele('rza:Zeile2').txt(absender.Fusszeilen.Zeile2);
    if (absender.Fusszeilen.Zeile3) fusszeilenEl.ele('rza:Zeile3').txt(absender.Fusszeilen.Zeile3);
  }
}

/**
 * Adds document to XML
 * 
 * @param parent - Parent XML element
 * @param dokument - Document data
 */
function addDokumentToXML(parent: any, dokument: RzaDokument): void {
  const dokumentEl = parent.ele('rza:Dokument', {
    'Art': dokument.Art,
    'RefID': dokument.RefID.toString(),
    'AdresseRefID': dokument.AdresseRefID.toString(),
    'Datum': dokument.Datum,
    'Waehrung': dokument.Waehrung
  });

  // Add optional attributes
  if (dokument.LieferadresseRefID) {
    dokumentEl.att('LieferadresseRefID', dokument.LieferadresseRefID.toString());
  }
  if (dokument.RechnungsadresseRefID) {
    dokumentEl.att('RechnungsadresseRefID', dokument.RechnungsadresseRefID.toString());
  }
  if (dokument.Titel) dokumentEl.att('Titel', dokument.Titel);
  if (dokument.Belegnummer) dokumentEl.att('Belegnummer', dokument.Belegnummer);
  if (dokument.Lieferdatum) dokumentEl.att('Lieferdatum', dokument.Lieferdatum);

  // Add sums
  addSummenToXML(dokumentEl, dokument.Summen);

  // Add tax rates if provided
  if (dokument.Steuersaetze) {
    addSteuersaetzeToXML(dokumentEl, dokument.Steuersaetze);
  }

  // Add payment terms if provided
  if (dokument.Zahlungskondition) {
    const zahlungEl = dokumentEl.ele('rza:Zahlungskondition');
    if (dokument.Zahlungskondition.Zahlungsziel) {
      zahlungEl.att('Zahlungsziel', dokument.Zahlungskondition.Zahlungsziel.toString());
    }
    if (dokument.Zahlungskondition.Skontotage) {
      zahlungEl.att('Skontotage', dokument.Zahlungskondition.Skontotage.toString());
    }
    if (dokument.Zahlungskondition.Skontoprozent) {
      zahlungEl.att('Skontoprozent', dokument.Zahlungskondition.Skontoprozent.toString());
    }
    if (dokument.Zahlungskondition.Zahlungsart) {
      zahlungEl.att('Zahlungsart', dokument.Zahlungskondition.Zahlungsart);
    }
  }

  // Add delivery terms if provided
  if (dokument.Lieferkondition) {
    const lieferEl = dokumentEl.ele('rza:Lieferkondition');
    if (dokument.Lieferkondition.Lieferart) {
      lieferEl.att('Lieferart', dokument.Lieferkondition.Lieferart);
    }
    if (dokument.Lieferkondition.Lieferkosten) {
      lieferEl.att('Lieferkosten', dokument.Lieferkondition.Lieferkosten.toString());
    }
    if (dokument.Lieferkondition.Lieferdatum) {
      lieferEl.att('Lieferdatum', dokument.Lieferkondition.Lieferdatum);
    }
  }

  // Add intro text if provided
  if (dokument.Einleitungstext) {
    dokumentEl.ele('rza:Einleitungstext').txt(dokument.Einleitungstext);
  }

  // Add closing text if provided
  if (dokument.Abschliessendtext) {
    dokumentEl.ele('rza:Abschliessendtext').txt(dokument.Abschliessendtext);
  }

  // Add alternative address if provided
  if (dokument.AbweichendeAnschrift) {
    const anschriftEl = dokumentEl.ele('rza:AbweichendeAnschrift');
    if (dokument.AbweichendeAnschrift.Name) anschriftEl.att('Name', dokument.AbweichendeAnschrift.Name);
    if (dokument.AbweichendeAnschrift.Name2) anschriftEl.att('Name2', dokument.AbweichendeAnschrift.Name2);
    if (dokument.AbweichendeAnschrift.Strasse) anschriftEl.att('Strasse', dokument.AbweichendeAnschrift.Strasse);
    if (dokument.AbweichendeAnschrift.PLZ) anschriftEl.att('PLZ', dokument.AbweichendeAnschrift.PLZ);
    if (dokument.AbweichendeAnschrift.Ort) anschriftEl.att('Ort', dokument.AbweichendeAnschrift.Ort);
    if (dokument.AbweichendeAnschrift.Land) anschriftEl.att('Land', dokument.AbweichendeAnschrift.Land);
  }

  // Add additional data if provided
  if (dokument.Zusatzdaten && dokument.Zusatzdaten.length > 0) {
    dokument.Zusatzdaten.forEach(zusatz => {
      dokumentEl.ele('rza:Zusatzdaten', {
        'Bezeichnung': zusatz.Bezeichnung,
        'Wert': zusatz.Wert
      });
    });
  }

  // Add document internet information if provided
  if (dokument.DokumentInternetInformationen) {
    const internetEl = dokumentEl.ele('rza:DokumentInternetInformationen', {
      'BelegkreisID': dokument.DokumentInternetInformationen.BelegkreisID.toString(),
      'FiliallagerID': dokument.DokumentInternetInformationen.FiliallagerID.toString(),
      'WaehrungID': dokument.DokumentInternetInformationen.WaehrungID.toString()
    });

    if (dokument.DokumentInternetInformationen.ID) {
      internetEl.att('ID', dokument.DokumentInternetInformationen.ID.toString());
    }
    if (dokument.DokumentInternetInformationen.Barfaktura) {
      internetEl.att('Barfaktura', dokument.DokumentInternetInformationen.Barfaktura);
    }
    if (dokument.DokumentInternetInformationen.ReverseChargeRechnung) {
      internetEl.att('ReverseChargeRechnung', dokument.DokumentInternetInformationen.ReverseChargeRechnung);
    }

    // Add account distribution if provided
    if (dokument.DokumentInternetInformationen.AufteilungKonten) {
      const aufteilungEl = internetEl.ele('rza:AufteilungKonten');
      dokument.DokumentInternetInformationen.AufteilungKonten.Konto.forEach(konto => {
        const kontoEl = aufteilungEl.ele('rza:Konto', {
          'Kontonummer': konto.Kontonummer,
          'Betrag': konto.Betrag.toString()
        });
        if (konto.Prozent) {
          kontoEl.att('Prozent', konto.Prozent.toString());
        }
      });
    }

    // Add commissions if provided
    if (dokument.DokumentInternetInformationen.Provisionen) {
      const provisionenEl = internetEl.ele('rza:Provisionen');
      dokument.DokumentInternetInformationen.Provisionen.Provision.forEach(provision => {
        provisionenEl.ele('rza:Provision', {
          'Vertreter': provision.Vertreter,
          'Prozent': provision.Prozent.toString(),
          'Betrag': provision.Betrag.toString()
        });
      });
    }
  }

  // Add positions
  addPositionenToXML(dokumentEl, dokument.Positionen);
}

/**
 * Adds sums to XML
 *
 * @param parent - Parent XML element
 * @param summen - Sums data
 */
function addSummenToXML(parent: any, summen: RzaSummen): void {
  const summenEl = parent.ele('rza:Summen');

  if (summen.Nettosumme !== undefined) {
    summenEl.att('Nettosumme', summen.Nettosumme.toString());
  }
  if (summen.Steuersumme !== undefined) {
    summenEl.att('Steuersumme', summen.Steuersumme.toString());
  }
  if (summen.Bruttosumme !== undefined) {
    summenEl.att('Bruttosumme', summen.Bruttosumme.toString());
  }
  if (summen.Skontosumme !== undefined) {
    summenEl.att('Skontosumme', summen.Skontosumme.toString());
  }
  if (summen.Rabattsumme !== undefined) {
    summenEl.att('Rabattsumme', summen.Rabattsumme.toString());
  }
}

/**
 * Adds tax rates to XML
 *
 * @param parent - Parent XML element
 * @param steuersaetze - Tax rates data
 */
function addSteuersaetzeToXML(parent: any, steuersaetze: RzaSteuersaetze): void {
  const steuersaetzeEl = parent.ele('rza:Steuersaetze');

  steuersaetze.Steuersatz.forEach(steuersatz => {
    steuersaetzeEl.ele('rza:Steuersatz', {
      'Prozent': steuersatz.Prozent.toString(),
      'Nettobetrag': steuersatz.Nettobetrag.toString(),
      'Steuerbetrag': steuersatz.Steuerbetrag.toString()
    });
  });
}

/**
 * Adds positions to XML
 *
 * @param parent - Parent XML element
 * @param positionen - Positions data
 */
function addPositionenToXML(parent: any, positionen: any): void {
  const positionenEl = parent.ele('rza:Positionen');

  positionen.Position.forEach((position: RzaPosition) => {
    const positionEl = positionenEl.ele('rza:Position', {
      'LfdNummer': position.LfdNummer.toString(),
      'Menge': position.Menge.toString()
    });

    // Add optional attributes
    if (position.Preis !== undefined) {
      positionEl.att('Preis', position.Preis.toString());
    }
    if (position.Einheit) {
      positionEl.att('Einheit', position.Einheit);
    }
    if (position.MWSt !== undefined) {
      positionEl.att('MWSt', position.MWSt.toString());
    }

    // Add position text if provided
    if (position.Positionstext) {
      positionEl.ele('rza:Positionstext').txt(position.Positionstext.Text);
    }

    // Add article info
    const artikelEl = positionEl.ele('rza:PositionArtikelinfo');
    if (position.PositionArtikelinfo.ArtikelID) {
      artikelEl.att('ArtikelID', position.PositionArtikelinfo.ArtikelID.toString());
    }
    if (position.PositionArtikelinfo.EAN) {
      artikelEl.att('EAN', position.PositionArtikelinfo.EAN);
    }
    if (position.PositionArtikelinfo.Artikelnummer) {
      artikelEl.att('Artikelnummer', position.PositionArtikelinfo.Artikelnummer);
    }
    if (position.PositionArtikelinfo.Bezeichnung) {
      artikelEl.att('Bezeichnung', position.PositionArtikelinfo.Bezeichnung);
    }

    // Add position internet information if provided
    if (position.PositionInternetInformationen) {
      const internetEl = positionEl.ele('rza:PositionInternetInformationen');
      if (position.PositionInternetInformationen.LagerID) {
        internetEl.att('LagerID', position.PositionInternetInformationen.LagerID.toString());
      }
      if (position.PositionInternetInformationen.KostenstelleID) {
        internetEl.att('KostenstelleID', position.PositionInternetInformationen.KostenstelleID.toString());
      }
      if (position.PositionInternetInformationen.ProjektID) {
        internetEl.att('ProjektID', position.PositionInternetInformationen.ProjektID.toString());
      }
    }
  });
}

/**
 * Adds address to XML
 *
 * @param parent - Parent XML element
 * @param adresse - Address data
 */
function addAdresseToXML(parent: any, adresse: RzaAdresse): void {
  const adresseEl = parent.ele('rza:Adresse', {
    'RefID': adresse.RefID.toString()
  });

  // Add optional attributes
  if (adresse.ID) adresseEl.att('ID', adresse.ID);
  if (adresse.Zuname) adresseEl.att('Zuname', adresse.Zuname);
  if (adresse.Vorname) adresseEl.att('Vorname', adresse.Vorname);
  if (adresse.Anrede) adresseEl.att('Anrede', adresse.Anrede);
  if (adresse.Titel) adresseEl.att('Titel', adresse.Titel);
  if (adresse.Firma) adresseEl.att('Firma', adresse.Firma);
  if (adresse.Strasse) adresseEl.att('Strasse', adresse.Strasse);
  if (adresse.PLZ) adresseEl.att('PLZ', adresse.PLZ);
  if (adresse.Ort) adresseEl.att('Ort', adresse.Ort);
  if (adresse.Land) adresseEl.att('Land', adresse.Land);
  if (adresse.Telefon) adresseEl.att('Telefon', adresse.Telefon);
  if (adresse.Fax) adresseEl.att('Fax', adresse.Fax);
  if (adresse.Email) adresseEl.att('Email', adresse.Email);
  if (adresse.UID) adresseEl.att('UID', adresse.UID);
  if (adresse.GLN) adresseEl.att('GLN', adresse.GLN);
}

/**
 * Validates XML structure before generation
 *
 * @param importData - RZA import data to validate
 * @returns Array of validation errors (empty if valid)
 */
export function validateRzaImportData(importData: RzaImportData): string[] {
  const errors: string[] = [];
  const { transaktion } = importData;

  // Validate transaction
  if (!transaktion.Erstellungsdatum) {
    errors.push('Erstellungsdatum is required');
  }
  if (!transaktion.Modus) {
    errors.push('Modus is required');
  }
  if (!transaktion.Verarbeitung) {
    errors.push('Verarbeitung is required');
  }

  // Validate sender
  if (!transaktion.Absender || !transaktion.Absender.Name) {
    errors.push('Absender Name is required');
  }

  // Validate documents
  if (!transaktion.Dokument || transaktion.Dokument.length === 0) {
    errors.push('At least one document is required');
  } else {
    transaktion.Dokument.forEach((dokument, index) => {
      if (!dokument.RefID) {
        errors.push(`Document ${index + 1}: RefID is required`);
      }
      if (!dokument.Art) {
        errors.push(`Document ${index + 1}: Art is required`);
      }
      if (!dokument.AdresseRefID) {
        errors.push(`Document ${index + 1}: AdresseRefID is required`);
      }
      if (!dokument.Datum) {
        errors.push(`Document ${index + 1}: Datum is required`);
      }
      if (!dokument.Waehrung) {
        errors.push(`Document ${index + 1}: Waehrung is required`);
      }
      if (!dokument.Positionen || !dokument.Positionen.Position || dokument.Positionen.Position.length === 0) {
        errors.push(`Document ${index + 1}: At least one position is required`);
      }
    });
  }

  return errors;
}
