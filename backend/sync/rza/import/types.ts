/**
 * TypeScript types for RZA import XML structure
 * Based on the RZA import schema and example files
 */

// Import the WooCommerce Order type
import { Order } from '../../../services/woocommerce';

// Enums and simple types
export type JaNein = 'JA' | 'NEIN';

export type DokumentArt =
  | 'Rechnung'
  | 'Gutschrift'
  | 'Lieferschein'
  | 'Retourschein'
  | 'Auftrag'
  | 'Angebot'
  | 'Sonstiges'
  | 'Wareneingang'
  | 'Warenausgang'
  | 'Bestellung'
  | 'Anfrage';

export type TransaktionModus = 'KOMPLETT';
export type TransaktionVerarbeitung = 'TEST' | 'ECHT';

// Contact information
export interface RzaKontakt {
  Telefon?: string;
  Fax?: string;
  Email?: string;
  Ansprechpartner?: string;
}

// Header and footer lines
export interface RzaKopfzeilen {
  Zeile1?: string;
  Zeile2?: string;
  Zeile3?: string;
}

export interface RzaFusszeilen {
  Zeile1?: string;
  Zeile2?: string;
  Zeile3?: string;
}

// Sender information
export interface RzaAbsender {
  Name: string;
  Name2?: string;
  Strasse?: string;
  PLZ?: string;
  Ort?: string;
  Land?: string;
  UIDNummer?: string;
  GLN?: string;
  Kontakt?: RzaKontakt;
  Kopfzeilen?: RzaKopfzeilen;
  Fusszeilen?: RzaFusszeilen;
}

// Document sums
export interface RzaSummen {
  Nettosumme?: number;
  Steuersumme?: number;
  Bruttosumme?: number;
  Skontosumme?: number;
  Rabattsumme?: number;
}

// Tax rates
export interface RzaSteuersatz {
  Prozent: number;
  Nettobetrag: number;
  Steuerbetrag: number;
}

export interface RzaSteuersaetze {
  Steuersatz: RzaSteuersatz[];
}

// Payment terms
export interface RzaZahlungskondition {
  Zahlungsziel?: number;
  Skontotage?: number;
  Skontoprozent?: number;
  Zahlungsart?: string;
}

// Delivery terms
export interface RzaLieferkondition {
  Lieferart?: string;
  Lieferkosten?: number;
  Lieferdatum?: string;
}

// Additional data
export interface RzaZusatzdaten {
  Bezeichnung: string;
  Wert: string;
}

// Alternative address
export interface RzaAbweichendeAnschrift {
  Name?: string;
  Name2?: string;
  Strasse?: string;
  PLZ?: string;
  Ort?: string;
  Land?: string;
}

// Account distribution
export interface RzaKonto {
  Kontonummer: string;
  Betrag: number;
  Prozent?: number;
}

export interface RzaAufteilungKonten {
  Konto: RzaKonto[];
}

// Commission
export interface RzaProvision {
  Vertreter: string;
  Prozent: number;
  Betrag: number;
}

export interface RzaProvisionen {
  Provision: RzaProvision[];
}

// Document internet information
export interface RzaDokumentInternetInformationen {
  ID?: number;
  BelegkreisID: number;
  FiliallagerID: number;
  WaehrungID: number;
  Barfaktura?: JaNein;
  ReverseChargeRechnung?: JaNein;
  AufteilungKonten?: RzaAufteilungKonten;
  Provisionen?: RzaProvisionen;
}

// Position text
export interface RzaPositionstext {
  Text: string;
}

// Position article info
export interface RzaPositionArtikelinfo {
  ArtikelID?: number;
  EAN?: string;
  Artikelnummer?: string;
  Bezeichnung?: string;
}

// Position internet information
export interface RzaPositionInternetInformationen {
  LagerID?: number;
  KostenstelleID?: number;
  ProjektID?: number;
}

// Position
export interface RzaPosition {
  LfdNummer: number;
  Menge: number;
  Preis?: number;
  Einheit?: string;
  MWSt?: number;
  Positionstext?: RzaPositionstext;
  PositionArtikelinfo: RzaPositionArtikelinfo;
  PositionInternetInformationen?: RzaPositionInternetInformationen;
}

// Positions container
export interface RzaPositionen {
  Position: RzaPosition[];
}

// Document
export interface RzaDokument {
  RefID: number;
  Art: DokumentArt;
  AdresseRefID: number;
  LieferadresseRefID?: number;
  RechnungsadresseRefID?: number;
  Titel?: string;
  Belegnummer?: string;
  Datum: string; // ISO date string
  Lieferdatum?: string; // ISO date string
  Waehrung: string;
  Summen: RzaSummen;
  Steuersaetze?: RzaSteuersaetze;
  Zahlungskondition?: RzaZahlungskondition;
  Lieferkondition?: RzaLieferkondition;
  Einleitungstext?: string;
  Abschliessendtext?: string;
  AbweichendeAnschrift?: RzaAbweichendeAnschrift;
  Zusatzdaten?: RzaZusatzdaten[];
  DokumentInternetInformationen?: RzaDokumentInternetInformationen;
  Positionen: RzaPositionen;
}

// Address
export interface RzaAdresse {
  RefID: number;
  ID?: string; // Can be "NULL" for new addresses
  Zuname?: string;
  Vorname?: string;
  Anrede?: string;
  Titel?: string;
  Firma?: string;
  Strasse?: string;
  PLZ?: string;
  Ort?: string;
  Land?: string;
  Telefon?: string;
  Fax?: string;
  Email?: string;
  UID?: string;
  GLN?: string;
}

// Main transaction structure
export interface RzaTransaktion {
  Erstellungsdatum: string; // ISO datetime string
  ErstelltVon?: string;
  Modus: TransaktionModus;
  Verarbeitung: TransaktionVerarbeitung;
  Absender: RzaAbsender;
  Dokument: RzaDokument[];
  Adresse?: RzaAdresse[];
}

// WooCommerce Order interface (extended for our needs)
export interface WooCommerceOrderExtended extends Order {
  // Add any additional fields we might need
  tax_lines?: Array<{
    id: number;
    rate_code: string;
    rate_id: number;
    label: string;
    compound: boolean;
    tax_total: string;
    shipping_tax_total: string;
  }>;
  shipping_lines?: Array<{
    id: number;
    method_title: string;
    method_id: string;
    total: string;
    total_tax: string;
  }>;
  fee_lines?: Array<{
    id: number;
    name: string;
    tax_class: string;
    tax_status: string;
    total: string;
    total_tax: string;
  }>;
  coupon_lines?: Array<{
    id: number;
    code: string;
    discount: string;
    discount_tax: string;
  }>;
  meta_data?: Array<{
    id: number;
    key: string;
    value: string;
  }>;
}

// Mapping configuration
export interface RzaImportConfig {
  // Sender information
  senderName: string;
  senderStreet?: string;
  senderPLZ?: string;
  senderCity?: string;
  senderCountry?: string;
  senderUID?: string;

  // Document settings
  documentType: DokumentArt;
  currency: string;
  belegkreisID: number;
  filiallagerID: number;
  waehrungID: number;

  // Processing settings
  verarbeitung: TransaktionVerarbeitung;
  erstelltVon?: string;

  // Default values
  defaultUnit?: string;
  defaultTaxRate?: number;
}

// Export interface for the complete import structure
export interface RzaImportData {
  transaktion: RzaTransaktion;
  config: RzaImportConfig;
}