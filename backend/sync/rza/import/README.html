<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>RZA Import System</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="rza-import-system">RZA Import System</h1>
<p>This system handles the conversion of WooCommerce orders to RZA ERP import XML files. It creates new customers and transactions in the RZA system based on WooCommerce order data.</p>
<h2 id="architecture">Architecture</h2>
<p>The system is organized into separate modules for better maintainability:</p>
<ul>
<li><strong>Models</strong> (<code>models/</code>): TypeScript interfaces and types
<ul>
<li><code>common.model.ts</code>: Shared types and transaction structure</li>
<li><code>document.model.ts</code>: Document and position-related types</li>
<li><code>address.model.ts</code>: Address and customer-related types</li>
</ul>
</li>
<li><strong>Mappers</strong> (<code>mappers/</code>): Domain-specific mapping functions
<ul>
<li><code>documents.mapper.ts</code>: WooCommerce orders to RZA documents</li>
<li><code>addresses.mapper.ts</code>: WooCommerce customers to RZA addresses</li>
</ul>
</li>
<li><strong>Services</strong>: XML generation and orchestration
<ul>
<li><code>importService.ts</code>: Main service orchestrating the import process</li>
<li><code>xmlGenerator.ts</code>: XML generation from RZA data structures</li>
</ul>
</li>
<li><strong>Configuration</strong>: Flexible mapping configuration for different use cases</li>
</ul>
<h3 id="key-features">Key Features</h3>
<ul>
<li><strong>Complete Type Safety</strong>: Full TypeScript support for all RZA XML elements</li>
<li><strong>Domain Separation</strong>: Clean separation between document and address mapping</li>
<li><strong>Flexible Configuration</strong>: Configurable mapping behavior and validation rules</li>
<li><strong>Validation</strong>: Comprehensive validation for addresses, documents, and configuration</li>
<li><strong>File Management</strong>: Automatic filename generation and temporary file storage</li>
<li><strong>Error Handling</strong>: Detailed error reporting and validation messages</li>
<li><strong>Country Mapping</strong>: Comprehensive mapping from ISO codes to German country names</li>
</ul>
<h2 id="field-mapping-documentation">Field Mapping Documentation</h2>
<h3 id="document-mapping-woocommerce-order--rza-document">Document Mapping (WooCommerce Order → RZA Document)</h3>
<table>
<thead>
<tr>
<th>WooCommerce Field</th>
<th>RZA Field</th>
<th>Type</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>order.id</code></td>
<td><code>Dokument.RefID</code></td>
<td>number</td>
<td>Unique document reference</td>
</tr>
<tr>
<td><code>order.date_created</code></td>
<td><code>Dokument.Datum</code></td>
<td>string</td>
<td>Converted to DD.MM.YYYY format</td>
</tr>
<tr>
<td><code>order.total</code></td>
<td><code>Summen.EndsummeBrutto</code></td>
<td>number</td>
<td>Total order amount including tax</td>
</tr>
<tr>
<td><code>order.total_tax</code></td>
<td><code>Summen.EndsummeMWSt</code></td>
<td>number</td>
<td>Total tax amount</td>
</tr>
<tr>
<td><code>order.total - order.total_tax</code></td>
<td><code>Summen.EndsummeNetto</code></td>
<td>number</td>
<td>Net amount (calculated)</td>
</tr>
<tr>
<td><code>order.currency</code></td>
<td><code>Dokument.Waehrung</code></td>
<td>string</td>
<td>Currency code (EUR, USD, etc.)</td>
</tr>
</tbody>
</table>
<h4 id="line-items-mapping-woocommerce-line-items--rza-positions">Line Items Mapping (WooCommerce Line Items → RZA Positions)</h4>
<table>
<thead>
<tr>
<th>WooCommerce Field</th>
<th>RZA Field</th>
<th>Type</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>item.id</code></td>
<td><code>Position.PositionInterneInformationen.ID</code></td>
<td>number</td>
<td>Internal position ID</td>
</tr>
<tr>
<td><code>item.product_id</code></td>
<td><code>Position.PositionArtikelinfo.ArtikelID</code></td>
<td>number</td>
<td>Product reference</td>
</tr>
<tr>
<td><code>item.sku</code></td>
<td><code>Position.PositionArtikelinfo.Artikelnummer</code></td>
<td>string</td>
<td>Product SKU or product_id as fallback</td>
</tr>
<tr>
<td><code>item.name</code></td>
<td><code>Position.Positionstext.Textzeile[0].Text</code></td>
<td>string</td>
<td>Product name</td>
</tr>
<tr>
<td><code>item.quantity</code></td>
<td><code>Position.Menge</code></td>
<td>number</td>
<td>Quantity ordered</td>
</tr>
<tr>
<td><code>item.price</code></td>
<td><code>Position.Preis</code></td>
<td>number</td>
<td>Unit price</td>
</tr>
<tr>
<td><code>item.total</code></td>
<td><code>Position.Betrag</code></td>
<td>number</td>
<td>Line total (price × quantity)</td>
</tr>
<tr>
<td><code>item.taxes[0]</code></td>
<td><code>Position.MWSt</code></td>
<td>number</td>
<td>Tax rate percentage (calculated)</td>
</tr>
</tbody>
</table>
<p><strong>Why these mappings:</strong></p>
<ul>
<li><strong>RefID</strong>: Uses WooCommerce order ID for traceability</li>
<li><strong>Date format</strong>: RZA requires DD.MM.YYYY format instead of ISO dates</li>
<li><strong>Tax calculation</strong>: RZA expects percentage rates, calculated from tax amounts</li>
<li><strong>Currency</strong>: Direct mapping for international orders</li>
<li><strong>Line items</strong>: Each WooCommerce line item becomes an RZA position</li>
</ul>
<h3 id="address-mapping-woocommerce-customer--rza-address">Address Mapping (WooCommerce Customer → RZA Address)</h3>
<table>
<thead>
<tr>
<th>WooCommerce Field</th>
<th>RZA Field</th>
<th>Type</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>billing.last_name</code></td>
<td><code>Adresse.Zuname</code></td>
<td>string</td>
<td>Customer surname</td>
</tr>
<tr>
<td><code>billing.first_name</code></td>
<td><code>Adresse.Vorname</code></td>
<td>string</td>
<td>Customer first name</td>
</tr>
<tr>
<td><code>billing.company</code></td>
<td><code>Adresse.Firma</code></td>
<td>string</td>
<td>Company name (optional)</td>
</tr>
<tr>
<td><code>billing.address_1</code></td>
<td><code>Anschrift.Strasse</code></td>
<td>string</td>
<td>Street address</td>
</tr>
<tr>
<td><code>billing.address_2</code></td>
<td><code>Anschrift.Strasse</code></td>
<td>string</td>
<td>Combined with address_1 if enabled</td>
</tr>
<tr>
<td><code>billing.postcode</code></td>
<td><code>Anschrift.PLZ</code></td>
<td>string</td>
<td>Postal code</td>
</tr>
<tr>
<td><code>billing.city</code></td>
<td><code>Anschrift.Ort</code></td>
<td>string</td>
<td>City name</td>
</tr>
<tr>
<td><code>billing.country</code></td>
<td><code>Anschrift.Land</code></td>
<td>string</td>
<td>Country name (mapped from ISO codes)</td>
</tr>
<tr>
<td><code>billing.email</code></td>
<td><code>Kontakt.Email</code></td>
<td>string</td>
<td>Email address</td>
</tr>
<tr>
<td><code>billing.phone</code></td>
<td><code>Kontakt.Telefon1</code></td>
<td>string</td>
<td>Phone number (formatted)</td>
</tr>
</tbody>
</table>
<h4 id="shipping-address-mapping-alternative-delivery-address">Shipping Address Mapping (Alternative Delivery Address)</h4>
<p>When shipping address differs from billing:</p>
<table>
<thead>
<tr>
<th>WooCommerce Field</th>
<th>RZA Field</th>
<th>Type</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>shipping.first_name + last_name</code></td>
<td><code>AbweichendeLieferanschrift.Textzeile[0]</code></td>
<td>string</td>
<td>Full name</td>
</tr>
<tr>
<td><code>shipping.company</code></td>
<td><code>AbweichendeLieferanschrift.Textzeile[1]</code></td>
<td>string</td>
<td>Company (if present)</td>
</tr>
<tr>
<td><code>shipping.address_1</code></td>
<td><code>AbweichendeLieferanschrift.Textzeile[2]</code></td>
<td>string</td>
<td>Street address</td>
</tr>
<tr>
<td><code>shipping.address_2</code></td>
<td><code>AbweichendeLieferanschrift.Textzeile[3]</code></td>
<td>string</td>
<td>Additional address</td>
</tr>
<tr>
<td><code>shipping.postcode + city</code></td>
<td><code>AbweichendeLieferanschrift.Textzeile[4]</code></td>
<td>string</td>
<td>Combined postal code and city</td>
</tr>
<tr>
<td><code>shipping.country</code></td>
<td><code>AbweichendeLieferanschrift.Textzeile[5]</code></td>
<td>string</td>
<td>Country (if different from AT)</td>
</tr>
</tbody>
</table>
<p><strong>Why these mappings:</strong></p>
<ul>
<li><strong>Name fields</strong>: Direct mapping for customer identification</li>
<li><strong>Address combination</strong>: RZA allows combining address lines for better formatting</li>
<li><strong>Country mapping</strong>: Converts ISO 2-letter codes to German country names</li>
<li><strong>Phone formatting</strong>: Removes spaces/hyphens for consistent format</li>
<li><strong>Alternative delivery</strong>: Uses text lines for flexible address formatting</li>
</ul>
<h3 id="country-code-mapping">Country Code Mapping</h3>
<p>The system includes comprehensive country code mapping from WooCommerce ISO codes to German country names:</p>
<table>
<thead>
<tr>
<th>ISO Code</th>
<th>German Name</th>
<th>ISO Code</th>
<th>German Name</th>
</tr>
</thead>
<tbody>
<tr>
<td>AT</td>
<td>Österreich</td>
<td>DE</td>
<td>Deutschland</td>
</tr>
<tr>
<td>CH</td>
<td>Schweiz</td>
<td>IT</td>
<td>Italien</td>
</tr>
<tr>
<td>FR</td>
<td>Frankreich</td>
<td>ES</td>
<td>Spanien</td>
</tr>
<tr>
<td>NL</td>
<td>Niederlande</td>
<td>BE</td>
<td>Belgien</td>
</tr>
<tr>
<td>PL</td>
<td>Polen</td>
<td>CZ</td>
<td>Tschechien</td>
</tr>
<tr>
<td>HU</td>
<td>Ungarn</td>
<td>SK</td>
<td>Slowakei</td>
</tr>
<tr>
<td>SI</td>
<td>Slowenien</td>
<td>HR</td>
<td>Kroatien</td>
</tr>
<tr>
<td>DK</td>
<td>Dänemark</td>
<td>SE</td>
<td>Schweden</td>
</tr>
<tr>
<td>NO</td>
<td>Norwegen</td>
<td>FI</td>
<td>Finnland</td>
</tr>
<tr>
<td>GB</td>
<td>Großbritannien</td>
<td>IE</td>
<td>Irland</td>
</tr>
</tbody>
</table>
<p><strong>Why German names:</strong> RZA system expects localized country names for proper processing.</p>
<h2 id="configuration-options">Configuration Options</h2>
<h3 id="document-configuration-documentmappingconfig">Document Configuration (<code>DocumentMappingConfig</code>)</h3>
<pre><code class="language-typescript">{
  <span class="hljs-attr">documentType</span>: <span class="hljs-string">&#x27;Auftrag&#x27;</span>,           <span class="hljs-comment">// Document type in RZA</span>
  <span class="hljs-attr">defaultUnit</span>: <span class="hljs-string">&#x27;Stk.&#x27;</span>,              <span class="hljs-comment">// Default unit for positions</span>
  <span class="hljs-attr">defaultTaxRate</span>: <span class="hljs-number">20</span>,               <span class="hljs-comment">// Default tax rate (%)</span>
  defaultErlö<span class="hljs-attr">sekonto</span>: <span class="hljs-string">&#x27;4020&#x27;</span>,       <span class="hljs-comment">// Default revenue account</span>
  <span class="hljs-attr">defaultArtikelgruppe</span>: <span class="hljs-string">&#x27;01&#x27;</span>,       <span class="hljs-comment">// Default article group</span>
  <span class="hljs-attr">includeTaxInPrice</span>: <span class="hljs-literal">true</span>,          <span class="hljs-comment">// Whether prices include tax</span>
  <span class="hljs-attr">calculateMaterialkosten</span>: <span class="hljs-literal">true</span>,    <span class="hljs-comment">// Calculate material costs</span>
  <span class="hljs-attr">materialkostenPercentage</span>: <span class="hljs-number">60</span>      <span class="hljs-comment">// Material cost percentage</span>
}
</code></pre>
<h3 id="address-configuration-addressmappingconfig">Address Configuration (<code>AddressMappingConfig</code>)</h3>
<pre><code class="language-typescript">{
  <span class="hljs-attr">alwaysCreateNewCustomer</span>: <span class="hljs-literal">true</span>,    <span class="hljs-comment">// Always create new customer (ID = &#x27;0&#x27;)</span>
  <span class="hljs-attr">includeShippingAddress</span>: <span class="hljs-literal">true</span>,     <span class="hljs-comment">// Include alternative delivery address</span>
  <span class="hljs-attr">combineAddressLines</span>: <span class="hljs-literal">true</span>,        <span class="hljs-comment">// Combine address_1 and address_2</span>
  <span class="hljs-attr">defaultWaehrungID</span>: <span class="hljs-number">21</span>,           <span class="hljs-comment">// Default currency ID (EUR)</span>
  <span class="hljs-attr">defaultFiliallagerID</span>: <span class="hljs-number">1</span>,         <span class="hljs-comment">// Default branch/warehouse ID</span>
  <span class="hljs-attr">mapCompanyToFirma</span>: <span class="hljs-literal">true</span>,         <span class="hljs-comment">// Map company field</span>
  <span class="hljs-attr">mapPhoneToTelefon1</span>: <span class="hljs-literal">true</span>,        <span class="hljs-comment">// Map phone to Telefon1</span>
  <span class="hljs-attr">requireEmail</span>: <span class="hljs-literal">true</span>,              <span class="hljs-comment">// Email is required</span>
  <span class="hljs-attr">requireName</span>: <span class="hljs-literal">true</span>,               <span class="hljs-comment">// Name is required</span>
  <span class="hljs-attr">requireAddress</span>: <span class="hljs-literal">true</span>             <span class="hljs-comment">// Address is required</span>
}
</code></pre>
<h2 id="usage-examples">Usage Examples</h2>
<h3 id="basic-import">Basic Import</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">import</span> { <span class="hljs-title class_">RzaImportService</span> } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./importService&#x27;</span>;
<span class="hljs-keyword">import</span> { createDefaultDocumentConfig, createDefaultAddressConfig } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./index&#x27;</span>;

<span class="hljs-keyword">const</span> service = <span class="hljs-keyword">new</span> <span class="hljs-title class_">RzaImportService</span>(wooCommerceService, {
  <span class="hljs-attr">tmpDirectory</span>: <span class="hljs-string">&#x27;/tmp/rza-imports&#x27;</span>,
  <span class="hljs-attr">rzaConfig</span>: {
    <span class="hljs-attr">currency</span>: <span class="hljs-string">&#x27;EUR&#x27;</span>,
    <span class="hljs-attr">belegkreisID</span>: <span class="hljs-number">1</span>,
    <span class="hljs-attr">filiallagerID</span>: <span class="hljs-number">1</span>,
    <span class="hljs-attr">waehrungID</span>: <span class="hljs-number">21</span>,
    <span class="hljs-attr">verarbeitung</span>: <span class="hljs-string">&#x27;ECHT&#x27;</span>
  },
  <span class="hljs-attr">documentConfig</span>: <span class="hljs-title function_">createDefaultDocumentConfig</span>(),
  <span class="hljs-attr">addressConfig</span>: <span class="hljs-title function_">createDefaultAddressConfig</span>()
});

<span class="hljs-comment">// Import specific orders</span>
<span class="hljs-keyword">const</span> result = <span class="hljs-keyword">await</span> service.<span class="hljs-title function_">importSpecificOrders</span>([<span class="hljs-number">12345</span>, <span class="hljs-number">12346</span>]);
</code></pre>
<h3 id="custom-configuration">Custom Configuration</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">const</span> customDocumentConfig = <span class="hljs-title function_">createDefaultDocumentConfig</span>({
  <span class="hljs-attr">documentType</span>: <span class="hljs-string">&#x27;Rechnung&#x27;</span>,
  <span class="hljs-attr">defaultTaxRate</span>: <span class="hljs-number">19</span>,
  <span class="hljs-attr">calculateMaterialkosten</span>: <span class="hljs-literal">false</span>
});

<span class="hljs-keyword">const</span> customAddressConfig = <span class="hljs-title function_">createDefaultAddressConfig</span>({
  <span class="hljs-attr">alwaysCreateNewCustomer</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">combineAddressLines</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">requireEmail</span>: <span class="hljs-literal">false</span>
});
</code></pre>
<h2 id="file-output">File Output</h2>
<p>Generated XML files are saved in the configured <code>tmpDirectory</code> with the naming pattern:</p>
<pre><code>rza-import-order-{ORDER_ID}-{TIMESTAMP}.xml
</code></pre>
<p>Example: <code>rza-import-order-12345-2024-01-15T10-30-00.xml</code></p>
<h2 id="error-handling">Error Handling</h2>
<p>The system provides comprehensive error handling and validation:</p>
<ul>
<li><strong>Configuration validation</strong>: Checks required fields and formats</li>
<li><strong>Address validation</strong>: Validates required address fields and email format</li>
<li><strong>Document validation</strong>: Ensures all required document fields are present</li>
<li><strong>XML generation</strong>: Validates data before XML creation</li>
</ul>
<p>All errors are logged and returned in the <code>ImportResult</code> structure for proper error handling.</p>
<h2 id="best-practices">Best Practices</h2>
<ol>
<li><strong>Always validate configuration</strong> before processing orders</li>
<li><strong>Use batch processing</strong> for multiple orders to improve performance</li>
<li><strong>Monitor the tmp directory</strong> for generated files</li>
<li><strong>Implement proper error handling</strong> for failed imports</li>
<li><strong>Test with small batches</strong> before processing large order volumes</li>
<li><strong>Keep configuration consistent</strong> across different environments</li>
</ol>
<h2 id="xml-structure-example">XML Structure Example</h2>
<p>Based on the real <code>import.example.xml</code> file, the generated XML follows this structure:</p>
<pre><code class="language-xml"><span class="hljs-meta">&lt;?xml version=<span class="hljs-string">&quot;1.0&quot;</span> encoding=<span class="hljs-string">&quot;UTF-8&quot;</span>?&gt;</span>
<span class="hljs-tag">&lt;<span class="hljs-name">Transaktion</span> <span class="hljs-attr">RefID</span>=<span class="hljs-string">&quot;12345&quot;</span> <span class="hljs-attr">Modus</span>=<span class="hljs-string">&quot;KOMPLETT&quot;</span> <span class="hljs-attr">Verarbeitung</span>=<span class="hljs-string">&quot;ECHT&quot;</span>
             <span class="hljs-attr">Zeitstempel</span>=<span class="hljs-string">&quot;03.07.2025 10:30&quot;</span>&gt;</span>

  <span class="hljs-tag">&lt;<span class="hljs-name">Dokument</span> <span class="hljs-attr">Art</span>=<span class="hljs-string">&quot;Auftrag&quot;</span> <span class="hljs-attr">RefID</span>=<span class="hljs-string">&quot;12345&quot;</span> <span class="hljs-attr">AdresseRefID</span>=<span class="hljs-string">&quot;112345&quot;</span>
            <span class="hljs-attr">Datum</span>=<span class="hljs-string">&quot;03.07.2025&quot;</span> <span class="hljs-attr">Waehrung</span>=<span class="hljs-string">&quot;EUR&quot;</span> <span class="hljs-attr">Belegnummer</span>=<span class="hljs-string">&quot;12345&quot;</span>&gt;</span>

    <span class="hljs-tag">&lt;<span class="hljs-name">Summen</span> <span class="hljs-attr">EndsummeNetto</span>=<span class="hljs-string">&quot;108.33&quot;</span> <span class="hljs-attr">EndsummeMWSt</span>=<span class="hljs-string">&quot;21.66&quot;</span> <span class="hljs-attr">EndsummeBrutto</span>=<span class="hljs-string">&quot;129.99&quot;</span>/&gt;</span>

    <span class="hljs-tag">&lt;<span class="hljs-name">DokumentInterneInformationen</span> <span class="hljs-attr">BelegkreisID</span>=<span class="hljs-string">&quot;1&quot;</span> <span class="hljs-attr">FiliallagerID</span>=<span class="hljs-string">&quot;1&quot;</span> <span class="hljs-attr">WaehrungID</span>=<span class="hljs-string">&quot;21&quot;</span>/&gt;</span>

    <span class="hljs-tag">&lt;<span class="hljs-name">Positionen</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">Position</span> <span class="hljs-attr">LfdNummer</span>=<span class="hljs-string">&quot;1&quot;</span> <span class="hljs-attr">Menge</span>=<span class="hljs-string">&quot;2&quot;</span> <span class="hljs-attr">Preis</span>=<span class="hljs-string">&quot;49.99&quot;</span> <span class="hljs-attr">Einheit</span>=<span class="hljs-string">&quot;Stk.&quot;</span> <span class="hljs-attr">MWSt</span>=<span class="hljs-string">&quot;20&quot;</span>&gt;</span>
        <span class="hljs-tag">&lt;<span class="hljs-name">PositionArtikelinfo</span> <span class="hljs-attr">ArtikelID</span>=<span class="hljs-string">&quot;123&quot;</span> <span class="hljs-attr">Artikelnummer</span>=<span class="hljs-string">&quot;SKU-001&quot;</span>
                           <span class="hljs-attr">Artikelgruppe</span>=<span class="hljs-string">&quot;01&quot;</span> <span class="hljs-attr">Untergruppe</span>=<span class="hljs-string">&quot;001&quot;</span>/&gt;</span>
        <span class="hljs-tag">&lt;<span class="hljs-name">Positionstext</span>&gt;</span>
          <span class="hljs-tag">&lt;<span class="hljs-name">Textzeile</span> <span class="hljs-attr">Zeile</span>=<span class="hljs-string">&quot;1&quot;</span> <span class="hljs-attr">Text</span>=<span class="hljs-string">&quot;Product Name&quot;</span>/&gt;</span>
        <span class="hljs-tag">&lt;/<span class="hljs-name">Positionstext</span>&gt;</span>
        <span class="hljs-tag">&lt;<span class="hljs-name">PositionInterneInformationen</span> <span class="hljs-attr">ID</span>=<span class="hljs-string">&quot;1&quot;</span> <span class="hljs-attr">Erl</span>ö<span class="hljs-attr">sekonto</span>=<span class="hljs-string">&quot;4020&quot;</span>
                                    <span class="hljs-attr">Materialkosten</span>=<span class="hljs-string">&quot;60.00&quot;</span>/&gt;</span>
      <span class="hljs-tag">&lt;/<span class="hljs-name">Position</span>&gt;</span>
    <span class="hljs-tag">&lt;/<span class="hljs-name">Positionen</span>&gt;</span>

  <span class="hljs-tag">&lt;/<span class="hljs-name">Dokument</span>&gt;</span>

  <span class="hljs-tag">&lt;<span class="hljs-name">Adresse</span> <span class="hljs-attr">RefID</span>=<span class="hljs-string">&quot;112345&quot;</span> <span class="hljs-attr">ID</span>=<span class="hljs-string">&quot;0&quot;</span> <span class="hljs-attr">Zuname</span>=<span class="hljs-string">&quot;Mustermann&quot;</span> <span class="hljs-attr">Vorname</span>=<span class="hljs-string">&quot;Max&quot;</span>
           <span class="hljs-attr">Firma</span>=<span class="hljs-string">&quot;Example GmbH&quot;</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">Anschrift</span> <span class="hljs-attr">Strasse</span>=<span class="hljs-string">&quot;Musterstraße 123&quot;</span> <span class="hljs-attr">PLZ</span>=<span class="hljs-string">&quot;1010&quot;</span> <span class="hljs-attr">Ort</span>=<span class="hljs-string">&quot;Wien&quot;</span> <span class="hljs-attr">Land</span>=<span class="hljs-string">&quot;Österreich&quot;</span>/&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">Kontakt</span> <span class="hljs-attr">Email</span>=<span class="hljs-string">&quot;<EMAIL>&quot;</span> <span class="hljs-attr">Telefon1</span>=<span class="hljs-string">&quot;+43123456789&quot;</span>/&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">AdresseInterneInformationen</span> <span class="hljs-attr">WaehrungID</span>=<span class="hljs-string">&quot;21&quot;</span> <span class="hljs-attr">FiliallagerID</span>=<span class="hljs-string">&quot;1&quot;</span>/&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">Adresse</span>&gt;</span>

<span class="hljs-tag">&lt;/<span class="hljs-name">Transaktion</span>&gt;</span>
</code></pre>
<h2 id="integration-notes">Integration Notes</h2>
<ul>
<li><strong>File Transport</strong>: Generated XML files need to be transported to the RZA system (implementation not included)</li>
<li><strong>Scheduling</strong>: Consider implementing automated scheduling for regular imports</li>
<li><strong>Monitoring</strong>: Add logging and monitoring for import processes</li>
<li><strong>Error Recovery</strong>: Implement retry mechanisms for failed imports</li>
<li><strong>Performance</strong>: System is optimized for individual order processing</li>
</ul>

            
            
        </body>
        </html>