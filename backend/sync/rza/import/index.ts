/**
 * Main export file for RZA import system
 * Provides clean imports for all functionality
 */

// Model exports
export * from './models/common.model';
export * from './models/document.model';
export * from './models/address.model';

// Mapper exports
export * from './mappers/documents.mapper';
export * from './mappers/addresses.mapper';

// Service exports
export * from './importService';
export * from './xmlGenerator';

// Explicit exports for commonly used types
export type { RzaTransaktion, RzaImportConfig, RzaImportData } from './models/common.model';
export type { RzaDokument, WooCommerceOrder, DocumentMappingConfig } from './models/document.model';
export type { RzaAdresse, AddressMappingConfig, AddressValidationResult } from './models/address.model';

// Convenience function aliases
export {
  mapWooOrderToRzaDocument as mapOrderToDocument,
  mapOrderLineItems as mapLineItems,
  calculateDocumentSums as calculateSums,
  validateRzaDocument as validateDocument,
  createDefaultDocumentConfig as createDocumentConfig
} from './mappers/documents.mapper';

export {
  mapOrderBillingToRzaAddress as mapBillingToAddress,
  mapWooCustomerToRzaAddress as mapCustomerToAddress,
  validateRzaAddress as validateAddress,
  createDefaultAddressConfig as createAddressConfig
} from './mappers/addresses.mapper';

// Main service functions
export {
  RzaImportService,
  ImportServiceConfig,
  ImportResult
} from './importService';

export {
  generateRzaImportXML,
  XMLGeneratorConfig
} from './xmlGenerator';
