/**
 * Test file for RZA import functionality
 * Demonstrates mapping WooCommerce orders to RZA import XML
 */

import {
  RzaImportConfig,
  WooCommerceOrderExtended,
  DokumentArt,
  TransaktionVerarbeitung
} from './types';
import {
  mapWooOrderToRzaImport,
  createDefaultRzaConfig,
  validateRzaConfig,
  generateImportFilename
} from './mapper';
import {
  generateRzaImportXML,
  validateRzaImportData
} from './xmlGenerator';

// Example WooCommerce order data
const exampleWooOrder: WooCommerceOrderExtended = {
  id: 12345,
  status: 'processing',
  currency: 'EUR',
  total: '129.99',
  date_created: '2025-07-03T10:30:00',
  date_modified: '2025-07-03T10:30:00',
  customer_id: 67890,
  billing: {
    first_name: 'Max',
    last_name: '<PERSON>erman<PERSON>',
    email: '<EMAIL>',
    phone: '+43 1 234 5678',
    address_1: 'Hauptstraße 123',
    address_2: 'Apartment 4B',
    city: 'Wien',
    state: 'Wien',
    postcode: '1010',
    country: 'AT'
  },
  shipping: {
    first_name: '<PERSON>',
    last_name: 'Mustermann',
    address_1: 'Hauptstraße 123',
    address_2: 'Apartment 4B',
    city: 'Wien',
    state: 'Wien',
    postcode: '1010',
    country: 'AT'
  },
  line_items: [
    {
      id: 1,
      name: 'Premium Widget',
      product_id: 101,
      variation_id: 0,
      quantity: 2,
      price: '49.99',
      total: '99.98',
      sku: 'WIDGET-PREMIUM-001'
    },
    {
      id: 2,
      name: 'Standard Gadget',
      product_id: 102,
      variation_id: 0,
      quantity: 1,
      price: '30.01',
      total: '30.01',
      sku: 'GADGET-STD-002'
    }
  ],
  tax_lines: [
    {
      id: 1,
      rate_code: 'AT-VAT-20',
      rate_id: 1,
      label: 'VAT',
      compound: false,
      tax_total: '21.67',
      shipping_tax_total: '0.00'
    }
  ],
  shipping_lines: [
    {
      id: 1,
      method_title: 'Standard Shipping',
      method_id: 'flat_rate',
      total: '8.34',
      total_tax: '1.67'
    }
  ]
};

// Example RZA import configuration
const exampleRzaConfig: RzaImportConfig = {
  senderName: 'Musterfirma GmbH',
  senderStreet: 'Firmenstraße 1',
  senderPLZ: '1010',
  senderCity: 'Wien',
  senderCountry: 'AT',
  senderUID: 'ATU12345678',
  documentType: 'Rechnung' as DokumentArt,
  currency: 'EUR',
  belegkreisID: 10,
  filiallagerID: 1,
  waehrungID: 1,
  verarbeitung: 'TEST' as TransaktionVerarbeitung,
  erstelltVon: 'WooCommerce-Sync-Test',
  defaultUnit: 'Stk',
  defaultTaxRate: 20
};

/**
 * Test: Map WooCommerce order to RZA import format
 */
function testOrderMapping(): void {
  console.log('=== Order Mapping Test ===');
  
  const importData = mapWooOrderToRzaImport(exampleWooOrder, exampleRzaConfig);
  
  console.log('Order ID:', exampleWooOrder.id);
  console.log('Document RefID:', importData.transaktion.Dokument[0].RefID);
  console.log('Address RefID:', importData.transaktion.Dokument[0].AdresseRefID);
  console.log('Positions count:', importData.transaktion.Dokument[0].Positionen.Position.length);
  console.log('Total amount:', importData.transaktion.Dokument[0].Summen.Bruttosumme);
  console.log('Customer name:', `${importData.transaktion.Adresse![0].Vorname} ${importData.transaktion.Adresse![0].Zuname}`);
}

/**
 * Test: Generate XML from mapped data
 */
function testXMLGeneration(): void {
  console.log('\n=== XML Generation Test ===');
  
  const importData = mapWooOrderToRzaImport(exampleWooOrder, exampleRzaConfig);
  const xmlContent = generateRzaImportXML(importData);
  
  console.log('XML generated successfully');
  console.log('XML length:', xmlContent.length, 'characters');
  console.log('XML preview (first 500 chars):');
  console.log(xmlContent.substring(0, 500) + '...');
}

/**
 * Test: Configuration validation
 */
function testConfigValidation(): void {
  console.log('\n=== Configuration Validation Test ===');
  
  // Test valid configuration
  const validErrors = validateRzaConfig(exampleRzaConfig);
  console.log('Valid config errors:', validErrors.length === 0 ? 'None' : validErrors.join(', '));
  
  // Test invalid configuration
  const invalidConfig = { ...exampleRzaConfig };
  invalidConfig.senderName = '';
  invalidConfig.belegkreisID = 0;
  
  const invalidErrors = validateRzaConfig(invalidConfig);
  console.log('Invalid config errors:', invalidErrors.join(', '));
}

/**
 * Test: Import data validation
 */
function testImportDataValidation(): void {
  console.log('\n=== Import Data Validation Test ===');
  
  const importData = mapWooOrderToRzaImport(exampleWooOrder, exampleRzaConfig);
  
  // Test valid data
  const validErrors = validateRzaImportData(importData);
  console.log('Valid import data errors:', validErrors.length === 0 ? 'None' : validErrors.join(', '));
  
  // Test invalid data
  const invalidData = { ...importData };
  invalidData.transaktion.Erstellungsdatum = '';
  invalidData.transaktion.Dokument[0].Datum = '';
  
  const invalidErrors = validateRzaImportData(invalidData);
  console.log('Invalid import data errors:', invalidErrors.join(', '));
}

/**
 * Test: Filename generation
 */
function testFilenameGeneration(): void {
  console.log('\n=== Filename Generation Test ===');
  
  // Single order
  const singleFilename = generateImportFilename([12345]);
  console.log('Single order filename:', singleFilename);
  
  // Multiple orders
  const multipleFilename = generateImportFilename([12345, 12346, 12347]);
  console.log('Multiple orders filename:', multipleFilename);
  
  // With custom timestamp
  const customTimestamp = new Date('2025-07-03T15:30:45');
  const customFilename = generateImportFilename([99999], customTimestamp);
  console.log('Custom timestamp filename:', customFilename);
}

/**
 * Test: Default configuration creation
 */
function testDefaultConfig(): void {
  console.log('\n=== Default Configuration Test ===');
  
  const defaultConfig = createDefaultRzaConfig();
  console.log('Default sender name:', defaultConfig.senderName);
  console.log('Default document type:', defaultConfig.documentType);
  console.log('Default currency:', defaultConfig.currency);
  console.log('Default processing mode:', defaultConfig.verarbeitung);
  
  // Test with overrides
  const customConfig = createDefaultRzaConfig({
    senderName: 'Custom Company',
    documentType: 'Auftrag' as DokumentArt,
    verarbeitung: 'ECHT' as TransaktionVerarbeitung
  });
  console.log('Custom sender name:', customConfig.senderName);
  console.log('Custom document type:', customConfig.documentType);
  console.log('Custom processing mode:', customConfig.verarbeitung);
}

/**
 * Test: Complete workflow
 */
function testCompleteWorkflow(): void {
  console.log('\n=== Complete Workflow Test ===');
  
  try {
    // 1. Validate configuration
    const configErrors = validateRzaConfig(exampleRzaConfig);
    if (configErrors.length > 0) {
      throw new Error(`Configuration invalid: ${configErrors.join(', ')}`);
    }
    console.log('✓ Configuration validated');
    
    // 2. Map order to RZA format
    const importData = mapWooOrderToRzaImport(exampleWooOrder, exampleRzaConfig);
    console.log('✓ Order mapped to RZA format');
    
    // 3. Validate import data
    const dataErrors = validateRzaImportData(importData);
    if (dataErrors.length > 0) {
      throw new Error(`Import data invalid: ${dataErrors.join(', ')}`);
    }
    console.log('✓ Import data validated');
    
    // 4. Generate XML
    const xmlContent = generateRzaImportXML(importData);
    console.log('✓ XML generated');
    
    // 5. Generate filename
    const filename = generateImportFilename([exampleWooOrder.id]);
    console.log('✓ Filename generated:', filename);
    
    console.log('\n🎉 Complete workflow test passed!');
    console.log('Final XML size:', xmlContent.length, 'characters');
    
  } catch (error) {
    console.error('❌ Workflow test failed:', error instanceof Error ? error.message : error);
  }
}

/**
 * Run all tests
 */
function runAllTests(): void {
  testOrderMapping();
  testXMLGeneration();
  testConfigValidation();
  testImportDataValidation();
  testFilenameGeneration();
  testDefaultConfig();
  testCompleteWorkflow();
  
  console.log('\n=== All Tests Completed ===');
}

// Export for use in other modules
export {
  exampleWooOrder,
  exampleRzaConfig,
  testOrderMapping,
  testXMLGeneration,
  testConfigValidation,
  testImportDataValidation,
  testFilenameGeneration,
  testDefaultConfig,
  testCompleteWorkflow,
  runAllTests
};

// Run tests when this file is executed directly
runAllTests();
