<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:rza="https://www.rza.at/XML/Fakt/"
           targetNamespace="https://www.rza.at/XML/Fakt/"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified">

  <!-- Typ-Definitionen gemäß Dokumentation -->
  <xs:simpleType name="JaNein">
    <xs:restriction base="xs:string">
      <xs:enumeration value="JA"/>
      <xs:enumeration value="NEIN"/>
    </xs:restriction>
  </xs:simpleType>

  <xs:simpleType name="DokumentArt">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Rechnung"/>
      <xs:enumeration value="Gutschrift"/>
      <xs:enumeration value="Lieferschein"/>
      <xs:enumeration value="Retourschein"/>
      <xs:enumeration value="Auftrag"/>
      <xs:enumeration value="Angebot"/>
      <xs:enumeration value="Sonstiges"/>
      <xs:enumeration value="Wareneingang"/>
      <xs:enumeration value="Warenausgang"/>
      <xs:enumeration value="Bestellung"/>
      <xs:enumeration value="Anfrage"/>
    </xs:restriction>
  </xs:simpleType>

  <!-- Stammelement Transaktion -->
  <xs:element name="Transaktion">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="rza:Absender"/>
        <xs:element ref="rza:Dokument" maxOccurs="unbounded"/>
        <xs:element ref="rza:Adresse" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attribute name="Erstellungsdatum" type="xs:dateTime" use="required"/>
      <xs:attribute name="ErstelltVon" type="xs:string"/>
      <xs:attribute name="Modus" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="KOMPLETT"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
      <xs:attribute name="Verarbeitung" use="required">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:enumeration value="TEST"/>
            <xs:enumeration value="ECHT"/>
          </xs:restriction>
        </xs:simpleType>
      </xs:attribute>
    </xs:complexType>
  </xs:element>

  <!-- Absender -->
  <xs:element name="Absender">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="rza:Kontakt" minOccurs="0"/>
        <xs:element ref="rza:Kopfzeilen" minOccurs="0"/>
        <xs:element ref="rza:Fusszeilen" minOccurs="0"/>
      </xs:sequence>
      <xs:attribute name="Name" type="xs:string" use="required"/>
      <xs:attribute name="Name2" type="xs:string"/>
      <xs:attribute name="Strasse" type="xs:string"/>
      <xs:attribute name="PLZ" type="xs:string"/>
      <xs:attribute name="Ort" type="xs:string"/>
      <xs:attribute name="Land" type="xs:string"/>
      <xs:attribute name="UIDNummer" type="xs:string"/>
      <xs:attribute name="GLN" type="xs:string"/>
    </xs:complexType>
  </xs:element>

  <!-- Dokument -->
  <xs:element name="Dokument">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="rza:Summen"/>
        <xs:element ref="rza:Steuersaetze" minOccurs="0"/>
        <xs:element ref="rza:Zahlungskondition" minOccurs="0"/>
        <xs:element ref="rza:Lieferkondition" minOccurs="0"/>
        <xs:element ref="rza:Einleitungstext" minOccurs="0"/>
        <xs:element ref="rza:Abschliessendtext" minOccurs="0"/>
        <xs:element ref="rza:AbweichendeAnschrift" minOccurs="0"/>
        <xs:element ref="rza:Zusatzdaten" minOccurs="0" maxOccurs="5"/>
        <xs:element ref="rza:DokumentInternetInformationen" minOccurs="0"/>
        <xs:element ref="rza:Positionen"/>
      </xs:sequence>
      <xs:attribute name="RefID" type="xs:long" use="required"/>
      <xs:attribute name="Art" type="rza:DokumentArt" use="required"/>
      <xs:attribute name="AdresseRefID" type="xs:long" use="required"/>
      <xs:attribute name="LieferadresseRefID" type="xs:long"/>
      <xs:attribute name="RechnungsadresseRefID" type="xs:long"/>
      <xs:attribute name="Titel" type="xs:string"/>
      <xs:attribute name="Belegnummer" type="xs:string"/>
      <xs:attribute name="Datum" type="xs:date" use="required"/>
      <xs:attribute name="Lieferdatum" type="xs:date"/>
      <xs:attribute name="Waehrung" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>

  <!-- Dokumentinterne Informationen -->
  <xs:element name="DokumentInternetInformationen">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="rza:AufteilungKonten" minOccurs="0"/>
        <xs:element ref="rza:Provisionen" minOccurs="0"/>
      </xs:sequence>
      <xs:attribute name="ID" type="xs:long"/>
      <xs:attribute name="BelegkreisID" type="xs:long" use="required"/>
      <xs:attribute name="FiliallagerID" type="xs:long" use="required"/>
      <xs:attribute name="WaehrungID" type="xs:long" use="required"/>
      <xs:attribute name="Barfaktura" type="rza:JaNein"/>
      <xs:attribute name="ReverseChargeRechnung" type="rza:JaNein"/>
    </xs:complexType>
  </xs:element>

  <!-- Positionen -->
  <xs:element name="Positionen">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="rza:Position" maxOccurs="unbounded"/>
      </xs:sequence>
    </xs:complexType>
  </xs:element>

  <!-- Position -->
  <xs:element name="Position">
    <xs:complexType>
      <xs:sequence>
        <xs:element ref="rza:Positionstext" minOccurs="0"/>
        <xs:element ref="rza:PositionArtikelinfo"/>
        <xs:element ref="rza:PositionInternetInformationen" minOccurs="0"/>
      </xs:sequence>
      <xs:attribute name="LfdNummer" type="xs:int" use="required"/>
      <xs:attribute name="Menge" type="xs:double" use="required"/>
      <xs:attribute name="Preis" type="xs:double"/>
      <xs:attribute name="Einheit" type="xs:string"/>
      <xs:attribute name="MWSt" type="xs:double"/>
    </xs:complexType>
  </xs:element>

  <!-- Weitere Elemente gemäß Dokumentation hier ergänzen -->
  <!-- ... (Für vollständiges Schema alle Elemente implementieren) -->

</xs:schema>