/**
 * RZA Import Service
 * Orchestrates the process of fetching WooCommerce orders and generating RZA import XML files
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { createModuleLogger } from '../../../infrastructure/logger';
import { WooCommerceService } from '../../../services/woocommerce';
import {
  RzaImportConfig,
  RzaTransaktion
} from './models/common.model';
import {
  WooCommerceOrder,
  DocumentMappingConfig
} from './models/document.model';
import {
  AddressMappingConfig
} from './models/address.model';
import {
  mapWooOrderToRzaDocument,
  createDefaultDocumentConfig,
  validateRzaDocument,
  formatDateTimeForRza
} from './mappers/documents.mapper';
import {
  mapOrderBillingToRzaAddress,
  createDefaultAddressConfig,
  validateRzaAddress
} from './mappers/addresses.mapper';
import { generateRzaImportXML } from './xmlGenerator';

const logger = createModuleLogger('rza-import');

export interface ImportServiceConfig {
  tmpDirectory: string;
  rzaConfig: RzaImportConfig;
  documentConfig?: DocumentMappingConfig;
  addressConfig?: AddressMappingConfig;
}

export interface ImportResult {
  success: boolean;
  filename?: string;
  filepath?: string;
  orderIds: number[];
  errors: string[];
  xmlContent?: string;
}

export class RzaImportService {
  private wooCommerceService: WooCommerceService;
  private config: ImportServiceConfig;
  private documentConfig: DocumentMappingConfig;
  private addressConfig: AddressMappingConfig;

  constructor(wooCommerceService: WooCommerceService, config: ImportServiceConfig) {
    this.wooCommerceService = wooCommerceService;
    this.config = config;
    this.documentConfig = config.documentConfig || createDefaultDocumentConfig();
    this.addressConfig = config.addressConfig || createDefaultAddressConfig();
  }

  /**
   * Validates the service configuration
   *
   * @returns Array of validation errors
   */
  private validateConfig(): string[] {
    const errors: string[] = [];

    if (!this.config.tmpDirectory) {
      errors.push('tmpDirectory is required');
    }

    if (!this.config.rzaConfig) {
      errors.push('rzaConfig is required');
    } else {
      if (!this.config.rzaConfig.currency) {
        errors.push('rzaConfig.currency is required');
      }
      if (!this.config.rzaConfig.belegkreisID) {
        errors.push('rzaConfig.belegkreisID is required');
      }
    }

    return errors;
  }

  /**
   * Processes a single WooCommerce order and generates RZA import XML
   *
   * @param order - WooCommerce order to process
   * @returns Import result with file information
   */
  async processOrder(order: WooCommerceOrder): Promise<ImportResult> {
    try {
      logger.info('Processing order for RZA import', { orderId: order.id });

      // Generate unique reference IDs
      const addressRefID = order.id + 100000; // Offset to avoid conflicts

      // Map order to RZA address
      const rzaAddress = mapOrderBillingToRzaAddress(order, addressRefID, this.addressConfig);

      // Validate address
      const addressValidation = validateRzaAddress(rzaAddress, this.addressConfig);
      if (!addressValidation.isValid) {
        return {
          success: false,
          orderIds: [order.id],
          errors: [`Address validation failed: ${addressValidation.errors.join(', ')}`]
        };
      }

      // Map order to RZA document
      const rzaDocument = mapWooOrderToRzaDocument(
        order,
        this.config.rzaConfig,
        this.documentConfig,
        addressRefID
      );

      // Validate document
      const documentErrors = validateRzaDocument(rzaDocument);
      if (documentErrors.length > 0) {
        return {
          success: false,
          orderIds: [order.id],
          errors: [`Document validation failed: ${documentErrors.join(', ')}`]
        };
      }

      // Create RZA transaction
      const transaction: RzaTransaktion = {
        Erstellungsdatum: formatDateTimeForRza(new Date().toISOString()),
        ErstelltVon: this.config.rzaConfig.erstelltVon || 'WooCommerce Import',
        Modus: 'KOMPLETT',
        Verarbeitung: this.config.rzaConfig.verarbeitung,
        Absender: {
          Name: 'WooCommerce Import Service',
          Name2: 'Automated Order Processing'
        },
        Dokument: [rzaDocument],
        Adresse: [rzaAddress]
      };

      // Create import data structure
      const importData = {
        transaktion: transaction,
        config: this.config.rzaConfig
      };

      // Generate XML
      const xmlContent = generateRzaImportXML(importData);

      // Generate filename
      const filename = this.generateFilename(order);
      const filepath = path.join(this.config.tmpDirectory, filename);

      // Ensure directory exists
      await fs.mkdir(this.config.tmpDirectory, { recursive: true });

      // Save XML file
      await fs.writeFile(filepath, xmlContent, 'utf8');

      logger.info('Successfully generated RZA import XML', {
        orderId: order.id,
        filename,
        filepath
      });

      return {
        success: true,
        orderIds: [order.id],
        errors: [],
        filename,
        filepath,
        xmlContent
      };

    } catch (error) {
      const errorMessage = `Failed to process order ${order.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error(errorMessage, { orderId: order.id, error });

      return {
        success: false,
        orderIds: [order.id],
        errors: [errorMessage]
      };
    }
  }

  /**
   * Imports specific orders by their IDs
   *
   * @param orderIds - Array of WooCommerce order IDs to import
   * @returns Import result with file information
   */
  async importSpecificOrders(orderIds: number[]): Promise<ImportResult> {
    try {
      logger.info('Starting RZA import for specific orders', { orderIds });

      // Validate configuration
      const configErrors = this.validateConfig();
      if (configErrors.length > 0) {
        return {
          success: false,
          orderIds: [],
          errors: [`Configuration errors: ${configErrors.join(', ')}`]
        };
      }

      // Fetch specific orders from WooCommerce
      const orders: WooCommerceOrder[] = [];
      const fetchErrors: string[] = [];

      for (const orderId of orderIds) {
        try {
          const order = await this.wooCommerceService.getOrder(orderId) as unknown as WooCommerceOrder;
          orders.push(order);
        } catch (error) {
          fetchErrors.push(`Failed to fetch order ${orderId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      if (orders.length === 0) {
        return {
          success: false,
          orderIds: [],
          errors: ['No orders could be fetched', ...fetchErrors]
        };
      }

      logger.info(`Fetched ${orders.length} orders for import`);

      // Process each order separately
      const results: ImportResult[] = [];

      for (const order of orders) {
        const result = await this.processOrder(order);
        results.push(result);
      }

      // Return combined result
      const allOrderIds = results.flatMap(r => r.orderIds);
      const allErrors = [...fetchErrors, ...results.flatMap(r => r.errors)];
      const successfulResults = results.filter(r => r.success);

      return {
        success: successfulResults.length === results.length && fetchErrors.length === 0,
        orderIds: allOrderIds,
        errors: allErrors
      };

    } catch (error) {
      logger.error('Failed to import specific orders:', error);
      return {
        success: false,
        orderIds: [],
        errors: [error instanceof Error ? error.message : 'Unknown error occurred']
      };
    }
  }

  /**
   * Updates the RZA configuration
   *
   * @param newConfig - New RZA configuration
   */
  updateRzaConfig(newConfig: Partial<RzaImportConfig>): void {
    this.config.rzaConfig = { ...this.config.rzaConfig, ...newConfig };
  }

  /**
   * Gets the current configuration
   *
   * @returns Current import service configuration
   */
  getConfig(): ImportServiceConfig {
    return { ...this.config };
  }

  /**
   * Generates a filename for the RZA import XML
   *
   * @param order - WooCommerce order
   * @returns Generated filename
   */
  private generateFilename(order: WooCommerceOrder): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    return `rza-import-order-${order.id}-${timestamp}.xml`;
  }
}
