/**
 * Mapper functions to convert WooCommerce orders to RZA import XML format
 */

import {
  RzaTransaktion,
  RzaDokument,
  RzaAdresse,
  RzaPosition,
  RzaPositionArtikelinfo,
  RzaSummen,
  RzaImportConfig,
  RzaImportData,
  WooCommerceOrderExtended,
  DokumentArt,
  TransaktionVerarbeitung
} from './types';

/**
 * Maps a WooCommerce order to RZA import format
 * 
 * @param order - WooCommerce order to convert
 * @param config - RZA import configuration
 * @returns RZA import data structure
 */
export function mapWooOrderToRzaImport(
  order: WooCommerceOrderExtended,
  config: RzaImportConfig
): RzaImportData {
  const currentDate = new Date().toISOString();
  const orderDate = new Date(order.date_created).toISOString().split('T')[0]; // YYYY-MM-DD format

  // Generate unique RefIDs
  const documentRefID = order.id;
  const addressRefID = order.id + 10000; // Offset to avoid conflicts

  // Map order positions
  const positions = mapOrderLineItems(order);

  // Calculate sums
  const sums = calculateOrderSums(order);

  // Create address from billing information
  const address = mapBillingToAddress(order, addressRefID);

  // Create document
  const document: RzaDokument = {
    RefID: documentRefID,
    Art: config.documentType,
    AdresseRefID: addressRefID,
    Datum: orderDate,
    Waehrung: config.currency,
    Belegnummer: order.id.toString(),
    Summen: sums,
    DokumentInternetInformationen: {
      BelegkreisID: config.belegkreisID,
      FiliallagerID: config.filiallagerID,
      WaehrungID: config.waehrungID
    },
    Positionen: {
      Position: positions
    }
  };

  // Create transaction
  const transaktion: RzaTransaktion = {
    Erstellungsdatum: currentDate,
    ErstelltVon: config.erstelltVon || 'WooCommerce-Sync',
    Modus: 'KOMPLETT',
    Verarbeitung: config.verarbeitung,
    Absender: {
      Name: config.senderName,
      Strasse: config.senderStreet,
      PLZ: config.senderPLZ,
      Ort: config.senderCity,
      Land: config.senderCountry,
      UIDNummer: config.senderUID
    },
    Dokument: [document],
    Adresse: [address]
  };

  return {
    transaktion,
    config
  };
}

/**
 * Maps multiple WooCommerce orders to RZA import format
 * 
 * @param orders - Array of WooCommerce orders
 * @param config - RZA import configuration
 * @returns Array of RZA import data structures
 */
export function mapWooOrdersToRzaImports(
  orders: WooCommerceOrderExtended[],
  config: RzaImportConfig
): RzaImportData[] {
  return orders.map(order => mapWooOrderToRzaImport(order, config));
}

/**
 * Maps WooCommerce line items to RZA positions
 * 
 * @param order - WooCommerce order
 * @returns Array of RZA positions
 */
export function mapOrderLineItems(order: WooCommerceOrderExtended): RzaPosition[] {
  return order.line_items.map((item, index) => {
    const articleInfo: RzaPositionArtikelinfo = {
      Artikelnummer: item.sku || item.product_id.toString(),
      Bezeichnung: item.name,
      EAN: extractEANFromMeta(item)
    };

    const position: RzaPosition = {
      LfdNummer: index + 1,
      Menge: item.quantity,
      Preis: parseFloat(item.price),
      Einheit: 'Stk', // Default unit, could be configurable
      PositionArtikelinfo: articleInfo
    };

    return position;
  });
}

/**
 * Maps WooCommerce billing address to RZA address
 * 
 * @param order - WooCommerce order
 * @param refID - Reference ID for the address
 * @returns RZA address
 */
export function mapBillingToAddress(
  order: WooCommerceOrderExtended,
  refID: number
): RzaAdresse {
  const billing = order.billing;
  
  return {
    RefID: refID,
    ID: 'NULL', // Always create new customer as requested
    Zuname: billing.last_name,
    Vorname: billing.first_name,
    Firma: (billing as any).company || undefined,
    Strasse: `${billing.address_1}${billing.address_2 ? ' ' + billing.address_2 : ''}`,
    PLZ: billing.postcode,
    Ort: billing.city,
    Land: billing.country,
    Telefon: billing.phone || undefined,
    Email: billing.email
  };
}

/**
 * Calculates order sums for RZA format
 * 
 * @param order - WooCommerce order
 * @returns RZA sums structure
 */
export function calculateOrderSums(order: WooCommerceOrderExtended): RzaSummen {
  const total = parseFloat(order.total);
  
  // Calculate tax total from tax lines if available
  let taxTotal = 0;
  if (order.tax_lines && order.tax_lines.length > 0) {
    taxTotal = order.tax_lines.reduce((sum, taxLine) => {
      return sum + parseFloat(taxLine.tax_total);
    }, 0);
  }

  // Calculate net total
  const netTotal = total - taxTotal;

  return {
    Nettosumme: netTotal,
    Steuersumme: taxTotal,
    Bruttosumme: total
  };
}

/**
 * Extracts EAN from product meta data if available
 * 
 * @param item - WooCommerce line item
 * @returns EAN string or undefined
 */
export function extractEANFromMeta(item: any): string | undefined {
  // This would need to be customized based on how EAN is stored in WooCommerce
  // Common meta keys: _ean, ean, barcode, gtin
  if (item.meta_data) {
    const eanMeta = item.meta_data.find((meta: any) => 
      ['_ean', 'ean', 'barcode', 'gtin'].includes(meta.key.toLowerCase())
    );
    return eanMeta?.value;
  }
  return undefined;
}

/**
 * Creates a default RZA import configuration
 * 
 * @param overrides - Configuration overrides
 * @returns Default RZA import configuration
 */
export function createDefaultRzaConfig(overrides: Partial<RzaImportConfig> = {}): RzaImportConfig {
  return {
    senderName: 'Default Company',
    documentType: 'Rechnung' as DokumentArt,
    currency: 'EUR',
    belegkreisID: 1,
    filiallagerID: 1,
    waehrungID: 1,
    verarbeitung: 'ECHT' as TransaktionVerarbeitung,
    defaultUnit: 'Stk',
    defaultTaxRate: 20,
    ...overrides
  };
}

/**
 * Validates RZA import configuration
 * 
 * @param config - Configuration to validate
 * @returns Array of validation errors (empty if valid)
 */
export function validateRzaConfig(config: RzaImportConfig): string[] {
  const errors: string[] = [];

  if (!config.senderName || config.senderName.trim() === '') {
    errors.push('Sender name is required');
  }

  if (!config.currency || config.currency.trim() === '') {
    errors.push('Currency is required');
  }

  if (!config.belegkreisID || config.belegkreisID <= 0) {
    errors.push('Valid BelegkreisID is required');
  }

  if (!config.filiallagerID || config.filiallagerID <= 0) {
    errors.push('Valid FiliallagerID is required');
  }

  if (!config.waehrungID || config.waehrungID <= 0) {
    errors.push('Valid WaehrungID is required');
  }

  return errors;
}

/**
 * Generates a unique filename for the RZA import XML
 * 
 * @param orderIds - Array of order IDs included in the import
 * @param timestamp - Optional timestamp (defaults to current time)
 * @returns Filename string
 */
export function generateImportFilename(orderIds: number[], timestamp?: Date): string {
  const date = timestamp || new Date();
  const dateStr = date.toISOString().split('T')[0].replace(/-/g, '');
  const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '');
  
  if (orderIds.length === 1) {
    return `rza_import_order_${orderIds[0]}_${dateStr}_${timeStr}.xml`;
  } else {
    return `rza_import_orders_${orderIds.length}_${dateStr}_${timeStr}.xml`;
  }
}
