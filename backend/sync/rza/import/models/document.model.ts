/**
 * Document-related types for RZA import system
 * Based on analysis of import.example.xml
 */

import {
  DokumentArt,
  RzaSummen,
  RzaZahlungskondition,
  RzaLieferkondition,
  RzaSteuersaetze,
  RzaZusatzdaten,
  RzaTextzeile,
  Ja<PERSON>ein
} from './common.model';

/**
 * Document internal information
 * Maps to DokumentInterneInformationen in XML
 */
export interface RzaDokumentInterneInformationen {
  ID?: number;
  BelegkreisID: number;
  FiliallagerID: number;
  BenutzernamenID?: number;
  WaehrungID: number;
  Barfaktura?: JaNein;
  ReverseChargeRechnung?: Ja<PERSON><PERSON>;
}

/**
 * Position article information
 * Maps to PositionArtikelinfo in XML
 */
export interface RzaPositionArtikelinfo {
  ArtikelID?: number;
  Artikelgruppe?: string;
  Untergruppe?: string;
  Artikelnummer?: string;
  EAN?: string;
  Bezeichnung?: string;
}

/**
 * Position text with multiple lines
 * Maps to Positionstext in XML
 */
export interface RzaPositionstext {
  Textzeile: RzaTextzeile[];
}

/**
 * Position internal information
 * Maps to PositionInterneInformationen in XML
 */
export interface RzaPositionInterneInformationen {
  ID?: number;
  Erloesekonto?: string;
  Materialkosten?: number;
  LagerID?: number;
  KostenstelleID?: number;
  ProjektID?: number;
}

/**
 * Document position/line item
 * Maps to Position in XML
 */
export interface RzaPosition {
  LfdNummer: number;
  Menge: number;
  Preis?: number;
  RabattProzent?: number;
  Betrag?: number;
  MWSt?: number;
  Einheit?: string;
  Rabattfaehig?: JaNein;
  PositionArtikelinfo: RzaPositionArtikelinfo;
  Positionstext?: RzaPositionstext;
  PositionInterneInformationen?: RzaPositionInterneInformationen;
}

/**
 * Container for all positions
 * Maps to Positionen in XML
 */
export interface RzaPositionen {
  Position: RzaPosition[];
}

/**
 * Main document structure
 * Maps to Dokument in XML
 */
export interface RzaDokument {
  RefID: number;
  AdresseRefID: number;
  LieferadresseRefID?: number;
  RechnungsadresseRefID?: number;
  Art: DokumentArt;
  Datum: string; // Format: "DD.MM.YYYY"
  Lieferdatum?: string; // Format: "DD.MM.YYYY"
  Waehrung: string;
  Titel?: string;
  Belegnummer?: string;
  Summen: RzaSummen;
  Zahlungskondition?: RzaZahlungskondition;
  Lieferkondition?: RzaLieferkondition;
  Steuersaetze?: RzaSteuersaetze;
  Einleitungstext?: string;
  Abschliessendtext?: string;
  Zusatzdaten?: RzaZusatzdaten[];
  DokumentInterneInformationen?: RzaDokumentInterneInformationen;
  Positionen: RzaPositionen;
}

/**
 * WooCommerce Order interface for mapping
 * Extended with fields needed for RZA import
 */
export interface WooCommerceOrder {
  id: number;
  parent_id: number;
  status: string;
  currency: string;
  version: string;
  prices_include_tax: boolean;
  date_created: string;
  date_modified: string;
  discount_total: string;
  discount_tax: string;
  shipping_total: string;
  shipping_tax: string;
  cart_tax: string;
  total: string;
  total_tax: string;
  customer_id: number;
  order_key: string;
  billing: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
    email: string;
    phone: string;
  };
  shipping: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  payment_method: string;
  payment_method_title: string;
  transaction_id: string;
  customer_ip_address: string;
  customer_user_agent: string;
  created_via: string;
  customer_note: string;
  date_completed: string | null;
  date_paid: string | null;
  cart_hash: string;
  number: string;
  line_items: Array<{
    id: number;
    name: string;
    product_id: number;
    variation_id: number;
    quantity: number;
    tax_class: string;
    subtotal: string;
    subtotal_tax: string;
    total: string;
    total_tax: string;
    taxes: Array<{
      id: number;
      total: string;
      subtotal: string;
    }>;
    meta_data: Array<{
      id: number;
      key: string;
      value: string;
      display_key: string;
      display_value: string;
    }>;
    sku: string;
    price: number;
    image: {
      id: string;
      src: string;
    };
    parent_name: string | null;
  }>;
  tax_lines: Array<{
    id: number;
    rate_code: string;
    rate_id: number;
    label: string;
    compound: boolean;
    tax_total: string;
    shipping_tax_total: string;
    rate_percent: number;
    meta_data: Array<{
      id: number;
      key: string;
      value: string;
    }>;
  }>;
  shipping_lines: Array<{
    id: number;
    method_title: string;
    method_id: string;
    instance_id: string;
    total: string;
    total_tax: string;
    taxes: Array<{
      id: number;
      total: string;
    }>;
    meta_data: Array<{
      id: number;
      key: string;
      value: string;
    }>;
  }>;
  fee_lines: Array<{
    id: number;
    name: string;
    tax_class: string;
    tax_status: string;
    amount: string;
    total: string;
    total_tax: string;
    taxes: Array<{
      id: number;
      total: string;
      subtotal: string;
    }>;
    meta_data: Array<{
      id: number;
      key: string;
      value: string;
    }>;
  }>;
  coupon_lines: Array<{
    id: number;
    code: string;
    discount: string;
    discount_tax: string;
    meta_data: Array<{
      id: number;
      key: string;
      value: string;
    }>;
  }>;
  refunds: Array<{
    id: number;
    reason: string;
    total: string;
  }>;
  payment_url: string;
  is_editable: boolean;
  needs_payment: boolean;
  needs_processing: boolean;
  date_created_gmt: string;
  date_modified_gmt: string;
  date_completed_gmt: string | null;
  date_paid_gmt: string | null;
  currency_symbol: string;
  meta_data: Array<{
    id: number;
    key: string;
    value: string;
  }>;
}

/**
 * Configuration for document mapping
 */
export interface DocumentMappingConfig {
  // Document type mapping
  documentType: DokumentArt;
  
  // Default values
  defaultUnit: string;
  defaultTaxRate: number;
  defaultErlösekonto: string;
  
  // Article group mapping
  defaultArtikelgruppe: string;
  
  // Tax handling
  includeTaxInPrice: boolean;
  
  // Position settings
  includeProductImages: boolean;
  includeProductMeta: boolean;
  
  // Calculation settings
  calculateMaterialkosten: boolean;
  materialkostenPercentage: number;
}
