/**
 * Common types and interfaces for RZA import system
 * Shared across documents and addresses
 */

// Basic enums and types
export type JaNein = 'JA' | 'NEIN';

export type DokumentArt =
  | 'Rechnung'
  | 'Gutschrift'
  | 'Lieferschein'
  | 'Retourschein'
  | 'Auftrag'
  | 'Angebot'
  | 'Sonstiges'
  | 'Wareneingang'
  | 'Warenausgang'
  | 'Bestellung'
  | 'Anfrage';

export type TransaktionModus = 'KOMPLETT';
export type TransaktionVerarbeitung = 'TEST' | 'ECHT';

/**
 * Text line structure used in various places
 */
export interface RzaTextzeile {
  Zeile: number;
  Text: string;
}

/**
 * Contact information structure
 */
export interface RzaKontakt {
  Telefon1?: string;
  Telefon2?: string;
  Fax?: string;
  Email?: string;
  Ansprechpartner?: string;
}

/**
 * Address structure used in multiple contexts
 */
export interface RzaAnschrift {
  Strasse?: string;
  PLZ?: string;
  Ort?: string;
  Land?: string;
}

/**
 * Alternative delivery address with text lines
 */
export interface RzaAbweichendeLieferanschrift {
  Textzeile: RzaTextzeile[];
}

/**
 * Sender information for the transaction
 */
export interface RzaAbsender {
  Name: string;
  Name2?: string;
  Strasse?: string;
  PLZ?: string;
  Ort?: string;
  Land?: string;
  UIDNummer?: string;
  GLN?: string;
  Kontakt?: RzaKontakt;
}

/**
 * Document sums structure
 */
export interface RzaSummen {
  EndsummeNetto?: number;
  EndsummeMWSt?: number;
  EndsummeBrutto?: number;
  Skontosumme?: number;
  Rabattsumme?: number;
}

/**
 * Payment terms
 */
export interface RzaZahlungskondition {
  Zieltage?: number;
  Skontotage?: number;
  Skontoprozent?: number;
  Zahlungsart?: string;
}

/**
 * Delivery terms
 */
export interface RzaLieferkondition {
  Lieferart?: string;
  Lieferkosten?: number;
  Lieferdatum?: string;
}

/**
 * Tax rate structure
 */
export interface RzaSteuersatz {
  Prozent: number;
  Nettobetrag: number;
  Steuerbetrag: number;
}

export interface RzaSteuersaetze {
  Steuersatz: RzaSteuersatz[];
}

/**
 * Additional data key-value pairs
 */
export interface RzaZusatzdaten {
  Bezeichnung: string;
  Wert: string;
}

/**
 * Configuration for RZA import mapping
 */
export interface RzaImportConfig {
  // Sender information
  senderName: string;
  senderStreet?: string;
  senderPLZ?: string;
  senderCity?: string;
  senderCountry?: string;
  senderUID?: string;

  // Document settings
  documentType: DokumentArt;
  currency: string;
  belegkreisID: number;
  filiallagerID: number;
  waehrungID: number;
  benutzernamenID?: number;

  // Processing settings
  verarbeitung: TransaktionVerarbeitung;
  erstelltVon?: string;

  // Default values
  defaultUnit?: string;
  defaultTaxRate?: number;
  defaultErlösekonto?: string;
}

/**
 * Main transaction structure
 */
export interface RzaTransaktion {
  Erstellungsdatum: string; // Format: "DD.MM.YYYY HH:mm"
  ErstelltVon?: string;
  Modus: TransaktionModus;
  Verarbeitung: TransaktionVerarbeitung;
  Absender: RzaAbsender;
  Dokument: import('./document.model').RzaDokument[];
  Adresse?: import('./address.model').RzaAdresse[];
}

/**
 * Complete RZA import data structure
 */
export interface RzaImportData {
  transaktion: RzaTransaktion;
  config: RzaImportConfig;
}
