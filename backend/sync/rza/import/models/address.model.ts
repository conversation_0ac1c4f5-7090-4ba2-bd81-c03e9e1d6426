/**
 * Address-related types for RZA import system
 * Based on analysis of import.example.xml
 */

import {
  RzaAnschrift,
  RzaKontakt,
  RzaAbweichendeLieferanschrift,
  JaNein
} from './common.model';

/**
 * Address internal information
 * Maps to AdresseInterneInformationen in XML
 */
export interface RzaAdresseInterneInformationen {
  KundenAdresse?: JaNein;
  WaehrungID?: number;
  FiliallagerID?: number;
  Selektionen?: string;
  Erlösekonto?: string;
  Kontonummer?: string;
  StandardZahlungskonditionID?: number;
  FreiesFeld1?: string;
  FreiesFeld2?: string;
  FreiesFeld3?: string;
  FreiesFeld4?: string;
  FreiesFeld5?: string;
}

/**
 * Main address structure
 * Maps to Adresse in XML
 */
export interface RzaAdresse {
  RefID: number;
  ID?: string; // Can be "0" for new addresses or existing customer ID
  Zuname?: string;
  Vorname?: string;
  Anrede?: string;
  Titel?: string;
  Firma?: string;
  Anschrift?: RzaAnschrift;
  Kontakt?: RzaKontakt;
  AbweichendeLieferanschrift?: RzaAbweichendeLieferanschrift;
  UID?: string;
  GLN?: string;
  AdresseInterneInformationen?: RzaAdresseInterneInformationen;
}

/**
 * WooCommerce Customer data for mapping
 */
export interface WooCommerceCustomer {
  id: number;
  date_created: string;
  date_created_gmt: string;
  date_modified: string;
  date_modified_gmt: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  username: string;
  billing: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
    email: string;
    phone: string;
  };
  shipping: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  is_paying_customer: boolean;
  avatar_url: string;
  meta_data: Array<{
    id: number;
    key: string;
    value: string;
  }>;
}

/**
 * Configuration for address mapping
 */
export interface AddressMappingConfig {
  // Customer creation settings
  alwaysCreateNewCustomer: boolean;
  defaultCustomerID: string;
  
  // Address handling
  includeShippingAddress: boolean;
  combineAddressLines: boolean;
  
  // Default values
  defaultWaehrungID: number;
  defaultFiliallagerID: number;
  defaultSelektionen: string;
  defaultErlösekonto: string;
  defaultZahlungskonditionID: number;
  
  // Field mapping
  mapCompanyToFirma: boolean;
  mapPhoneToTelefon1: boolean;
  
  // Custom fields
  useFreieFelder: boolean;
  freieFelderMapping: {
    feld1?: string; // WooCommerce meta key
    feld2?: string; // WooCommerce meta key
    feld3?: string; // WooCommerce meta key
    feld4?: string; // WooCommerce meta key
    feld5?: string; // WooCommerce meta key
  };
  
  // Validation settings
  requireEmail: boolean;
  requireName: boolean;
  requireAddress: boolean;
}

/**
 * Address validation result
 */
export interface AddressValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Country code mapping for RZA system
 */
export interface CountryMapping {
  wooCommerceCode: string; // ISO 2-letter code (e.g., "AT", "DE")
  rzaCode: string; // RZA country code
  rzaName: string; // Full country name in German
}

/**
 * Default country mappings
 */
export const DEFAULT_COUNTRY_MAPPINGS: CountryMapping[] = [
  { wooCommerceCode: 'AT', rzaCode: 'AT', rzaName: 'Österreich' },
  { wooCommerceCode: 'DE', rzaCode: 'DE', rzaName: 'Deutschland' },
  { wooCommerceCode: 'CH', rzaCode: 'CH', rzaName: 'Schweiz' },
  { wooCommerceCode: 'IT', rzaCode: 'IT', rzaName: 'Italien' },
  { wooCommerceCode: 'FR', rzaCode: 'FR', rzaName: 'Frankreich' },
  { wooCommerceCode: 'NL', rzaCode: 'NL', rzaName: 'Niederlande' },
  { wooCommerceCode: 'BE', rzaCode: 'BE', rzaName: 'Belgien' },
  { wooCommerceCode: 'LU', rzaCode: 'LU', rzaName: 'Luxemburg' },
  { wooCommerceCode: 'CZ', rzaCode: 'CZ', rzaName: 'Tschechien' },
  { wooCommerceCode: 'SK', rzaCode: 'SK', rzaName: 'Slowakei' },
  { wooCommerceCode: 'HU', rzaCode: 'HU', rzaName: 'Ungarn' },
  { wooCommerceCode: 'SI', rzaCode: 'SI', rzaName: 'Slowenien' },
  { wooCommerceCode: 'HR', rzaCode: 'HR', rzaName: 'Kroatien' },
  { wooCommerceCode: 'PL', rzaCode: 'PL', rzaName: 'Polen' },
  { wooCommerceCode: 'ES', rzaCode: 'ES', rzaName: 'Spanien' },
  { wooCommerceCode: 'PT', rzaCode: 'PT', rzaName: 'Portugal' },
  { wooCommerceCode: 'GB', rzaCode: 'GB', rzaName: 'Großbritannien' },
  { wooCommerceCode: 'IE', rzaCode: 'IE', rzaName: 'Irland' },
  { wooCommerceCode: 'DK', rzaCode: 'DK', rzaName: 'Dänemark' },
  { wooCommerceCode: 'SE', rzaCode: 'SE', rzaName: 'Schweden' },
  { wooCommerceCode: 'NO', rzaCode: 'NO', rzaName: 'Norwegen' },
  { wooCommerceCode: 'FI', rzaCode: 'FI', rzaName: 'Finnland' }
];

/**
 * Phone number formatting options
 */
export interface PhoneFormatConfig {
  removeSpaces: boolean;
  removeHyphens: boolean;
  addCountryPrefix: boolean;
  defaultCountryPrefix: string;
}
