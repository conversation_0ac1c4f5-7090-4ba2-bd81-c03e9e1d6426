/**
 * Standalone test file for RZA import functionality
 * No external dependencies except xmlbuilder2
 */

import { create } from 'xmlbuilder2';

// Simplified types for testing
interface TestWooOrder {
  id: number;
  status: string;
  currency: string;
  total: string;
  date_created: string;
  billing: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    address_1: string;
    address_2?: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
    company?: string;
  };
  line_items: Array<{
    id: number;
    name: string;
    product_id: number;
    quantity: number;
    price: string;
    total: string;
    sku: string;
  }>;
}

interface TestRzaConfig {
  senderName: string;
  senderStreet?: string;
  senderPLZ?: string;
  senderCity?: string;
  senderCountry?: string;
  documentType: string;
  currency: string;
  belegkreisID: number;
  filiallagerID: number;
  waehrungID: number;
  verarbeitung: string;
}

// Test data
const testOrder: TestWooOrder = {
  id: 12345,
  status: 'processing',
  currency: 'EUR',
  total: '129.99',
  date_created: '2025-07-03T10:30:00',
  billing: {
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    phone: '+43 1 234 5678',
    address_1: 'Hauptstraße 123',
    address_2: 'Apartment 4B',
    city: 'Wien',
    state: 'Wien',
    postcode: '1010',
    country: 'AT',
    company: 'Test Company GmbH'
  },
  line_items: [
    {
      id: 1,
      name: 'Premium Widget',
      product_id: 101,
      quantity: 2,
      price: '49.99',
      total: '99.98',
      sku: 'WIDGET-PREMIUM-001'
    },
    {
      id: 2,
      name: 'Standard Gadget',
      product_id: 102,
      quantity: 1,
      price: '30.01',
      total: '30.01',
      sku: 'GADGET-STD-002'
    }
  ]
};

const testConfig: TestRzaConfig = {
  senderName: 'Musterfirma GmbH',
  senderStreet: 'Firmenstraße 1',
  senderPLZ: '1010',
  senderCity: 'Wien',
  senderCountry: 'AT',
  documentType: 'Rechnung',
  currency: 'EUR',
  belegkreisID: 10,
  filiallagerID: 1,
  waehrungID: 1,
  verarbeitung: 'TEST'
};

/**
 * Simple mapping function for testing
 */
function mapOrderToRzaXML(order: TestWooOrder, config: TestRzaConfig): string {
  const currentDate = new Date().toISOString();
  const orderDate = new Date(order.date_created).toISOString().split('T')[0];
  
  const documentRefID = order.id;
  const addressRefID = order.id + 10000;
  
  // Calculate totals
  const total = parseFloat(order.total);
  const taxRate = 0.20; // 20% VAT
  const netTotal = total / (1 + taxRate);
  const taxTotal = total - netTotal;

  // Create XML document
  const root = create({ version: '1.0', encoding: 'UTF-8' })
    .ele('rza:Transaktion', {
      'xmlns:rza': 'https://www.rza.at/XML/Fakt/',
      'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
      'xsi:schemaLocation': 'https://www.rza.at/XML/Fakt/ Transaktion.xsd',
      'Erstellungsdatum': currentDate,
      'Modus': 'KOMPLETT',
      'Verarbeitung': config.verarbeitung,
      'ErstelltVon': 'WooCommerce-Sync-Test'
    });

  // Add sender
  root.ele('rza:Absender', {
    'Name': config.senderName,
    'Strasse': config.senderStreet,
    'PLZ': config.senderPLZ,
    'Ort': config.senderCity,
    'Land': config.senderCountry
  });

  // Add document
  const dokument = root.ele('rza:Dokument', {
    'Art': config.documentType,
    'RefID': documentRefID.toString(),
    'AdresseRefID': addressRefID.toString(),
    'Datum': orderDate,
    'Waehrung': config.currency,
    'Belegnummer': order.id.toString()
  });

  // Add sums
  dokument.ele('rza:Summen', {
    'Nettosumme': netTotal.toFixed(2),
    'Steuersumme': taxTotal.toFixed(2),
    'Bruttosumme': total.toFixed(2)
  });

  // Add document internet information
  dokument.ele('rza:DokumentInternetInformationen', {
    'BelegkreisID': config.belegkreisID.toString(),
    'FiliallagerID': config.filiallagerID.toString(),
    'WaehrungID': config.waehrungID.toString()
  });

  // Add positions
  const positionen = dokument.ele('rza:Positionen');
  
  order.line_items.forEach((item, index) => {
    const position = positionen.ele('rza:Position', {
      'LfdNummer': (index + 1).toString(),
      'Menge': item.quantity.toString(),
      'Preis': parseFloat(item.price).toFixed(2),
      'Einheit': 'Stk'
    });

    position.ele('rza:PositionArtikelinfo', {
      'Artikelnummer': item.sku,
      'Bezeichnung': item.name,
      'EAN': ''
    });
  });

  // Add address
  root.ele('rza:Adresse', {
    'RefID': addressRefID.toString(),
    'ID': 'NULL',
    'Zuname': order.billing.last_name,
    'Vorname': order.billing.first_name,
    'Firma': order.billing.company || '',
    'Strasse': `${order.billing.address_1}${order.billing.address_2 ? ' ' + order.billing.address_2 : ''}`,
    'PLZ': order.billing.postcode,
    'Ort': order.billing.city,
    'Land': order.billing.country,
    'Telefon': order.billing.phone || '',
    'Email': order.billing.email
  });

  return root.end({ prettyPrint: true });
}

/**
 * Test the XML generation
 */
function testXMLGeneration(): void {
  console.log('=== RZA Import XML Generation Test ===');
  
  try {
    const xmlContent = mapOrderToRzaXML(testOrder, testConfig);
    
    console.log('✓ XML generated successfully');
    console.log('Order ID:', testOrder.id);
    console.log('Customer:', `${testOrder.billing.first_name} ${testOrder.billing.last_name}`);
    console.log('Total amount:', testOrder.total, testOrder.currency);
    console.log('Line items:', testOrder.line_items.length);
    console.log('XML length:', xmlContent.length, 'characters');
    
    console.log('\n--- Generated XML ---');
    console.log(xmlContent);
    
    console.log('\n🎉 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

/**
 * Test filename generation
 */
function testFilenameGeneration(): void {
  console.log('\n=== Filename Generation Test ===');
  
  const date = new Date();
  const dateStr = date.toISOString().split('T')[0].replace(/-/g, '');
  const timeStr = date.toTimeString().split(' ')[0].replace(/:/g, '');
  
  const filename = `rza_import_order_${testOrder.id}_${dateStr}_${timeStr}.xml`;
  
  console.log('Generated filename:', filename);
  console.log('✓ Filename generation test passed');
}

/**
 * Test configuration validation
 */
function testConfigValidation(): void {
  console.log('\n=== Configuration Validation Test ===');
  
  const errors: string[] = [];
  
  if (!testConfig.senderName || testConfig.senderName.trim() === '') {
    errors.push('Sender name is required');
  }
  
  if (!testConfig.currency || testConfig.currency.trim() === '') {
    errors.push('Currency is required');
  }
  
  if (!testConfig.belegkreisID || testConfig.belegkreisID <= 0) {
    errors.push('Valid BelegkreisID is required');
  }
  
  if (!testConfig.filiallagerID || testConfig.filiallagerID <= 0) {
    errors.push('Valid FiliallagerID is required');
  }
  
  if (!testConfig.waehrungID || testConfig.waehrungID <= 0) {
    errors.push('Valid WaehrungID is required');
  }
  
  if (errors.length === 0) {
    console.log('✓ Configuration validation passed');
  } else {
    console.log('❌ Configuration validation failed:', errors.join(', '));
  }
}

/**
 * Run all tests
 */
function runAllTests(): void {
  testConfigValidation();
  testFilenameGeneration();
  testXMLGeneration();
  
  console.log('\n=== All Tests Completed ===');
}

// Run tests
runAllTests();
