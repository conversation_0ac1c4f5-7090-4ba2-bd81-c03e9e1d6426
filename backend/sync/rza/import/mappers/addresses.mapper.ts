/**
 * Address mapper for RZA import system
 * Maps WooCommerce customer/billing data to RZA addresses
 */

import {
  RzaAdresse,
  RzaAdresseInterneInformationen,
  WooCommerceCustomer,
  AddressMappingConfig,
  AddressValidationResult,
  CountryMapping,
  DEFAULT_COUNTRY_MAPPINGS,
  PhoneFormatConfig
} from '../models/address.model';
import {
  RzaAnschrift,
  RzaKontakt,
  RzaAbweichendeLieferanschrift,
  RzaTextzeile
} from '../models/common.model';
import { WooCommerceOrder } from '../models/document.model';

/**
 * Maps WooCommerce order billing information to RZA address according to API contract
 * API Contract: Customers are ALWAYS created as new (ID='0')
 * API Contract field mappings:
 * - WooCommerce billing.last_name -> RZA Adresse.Zuname (direct)
 * - WooCommerce billing.first_name -> RZA Adresse.Vorname (direct)
 * - WooCommerce billing.company -> RZA Adresse.Firma (direct)
 * - WooCommerce billing.address_1 -> RZA Anschrift.Strasse (direct)
 * - WooCommerce billing.address_2 -> RZA Anschrift.Strasse (combined with address_1)
 * - WooCommerce billing.postcode -> RZA Anschrift.PLZ (direct)
 * - WooCommerce billing.city -> RZA Anschrift.Ort (direct)
 * - WooCommerce billing.country -> RZA Anschrift.Land (ISO -> Deutsche Namen)
 * - WooCommerce billing.email -> RZA Kontakt.Email (direct)
 * - WooCommerce billing.phone -> RZA Kontakt.Telefon1 (formatted)
 *
 * @param order - WooCommerce order with billing information
 * @param refID - Reference ID for the address
 * @param config - Address mapping configuration
 * @returns RZA address structure
 */
export function mapOrderBillingToRzaAddress(
  order: WooCommerceOrder,
  refID: number,
  config: AddressMappingConfig
): RzaAdresse {
  const billing = order.billing;
  const shipping = order.shipping;
  
  // Create main address structure according to API contract
  const anschrift: RzaAnschrift = {
    Strasse: config.combineAddressLines
      ? `${billing.address_1}${billing.address_2 ? ' ' + billing.address_2 : ''}` // WooCommerce billing.address_1 + billing.address_2 -> RZA Anschrift.Strasse (combined)
      : billing.address_1, // WooCommerce billing.address_1 -> RZA Anschrift.Strasse (direct)
    PLZ: billing.postcode, // WooCommerce billing.postcode -> RZA Anschrift.PLZ (direct)
    Ort: billing.city, // WooCommerce billing.city -> RZA Anschrift.Ort (direct)
    Land: mapCountryCode(billing.country) // WooCommerce billing.country -> RZA Anschrift.Land (ISO -> Deutsche Namen)
  };
  
  // Create contact information according to API contract
  const kontakt: RzaKontakt = {
    Email: billing.email // WooCommerce billing.email -> RZA Kontakt.Email (direct)
  };

  if (billing.phone && config.mapPhoneToTelefon1) {
    kontakt.Telefon1 = formatPhoneNumber(billing.phone, { // WooCommerce billing.phone -> RZA Kontakt.Telefon1 (formatted)
      removeSpaces: true,
      removeHyphens: true,
      addCountryPrefix: false,
      defaultCountryPrefix: '+43'
    });
  }
  
  // Create alternative delivery address if different from billing
  let abweichendeLieferanschrift: RzaAbweichendeLieferanschrift | undefined;
  if (config.includeShippingAddress && hasShippingAddress(order)) {
    abweichendeLieferanschrift = createAlternativeDeliveryAddress(shipping);
  }
  
  // Create internal information
  const interneInformationen: RzaAdresseInterneInformationen = {
    KundenAdresse: 'JA',
    WaehrungID: config.defaultWaehrungID,
    FiliallagerID: config.defaultFiliallagerID,
    Selektionen: config.defaultSelektionen,
    Erlösekonto: config.defaultErlösekonto,
    StandardZahlungskonditionID: config.defaultZahlungskonditionID
  };
  
  // Map custom fields if enabled
  if (config.useFreieFelder && order.meta_data) {
    mapCustomFields(order.meta_data, interneInformationen, config);
  }
  
  const address: RzaAdresse = {
    RefID: refID,
    ID: '0', // API Contract: Customers are ALWAYS created as new (ID='0')
    Zuname: billing.last_name, // WooCommerce billing.last_name -> RZA Adresse.Zuname (direct)
    Vorname: billing.first_name, // WooCommerce billing.first_name -> RZA Adresse.Vorname (direct)
    Anschrift: anschrift,
    Kontakt: kontakt,
    AdresseInterneInformationen: interneInformationen
  };

  // Add company if present according to API contract
  if (billing.company && config.mapCompanyToFirma) {
    address.Firma = billing.company; // WooCommerce billing.company -> RZA Adresse.Firma (direct)
  }
  
  // Add alternative delivery address if created
  if (abweichendeLieferanschrift) {
    address.AbweichendeLieferanschrift = abweichendeLieferanschrift;
  }
  
  return address;
}

/**
 * Maps WooCommerce customer to RZA address
 * 
 * @param customer - WooCommerce customer data
 * @param refID - Reference ID for the address
 * @param config - Address mapping configuration
 * @returns RZA address structure
 */
export function mapWooCustomerToRzaAddress(
  customer: WooCommerceCustomer,
  refID: number,
  config: AddressMappingConfig
): RzaAdresse {
  const billing = customer.billing;
  const shipping = customer.shipping;
  
  // Create main address structure
  const anschrift: RzaAnschrift = {
    Strasse: config.combineAddressLines 
      ? `${billing.address_1}${billing.address_2 ? ' ' + billing.address_2 : ''}`
      : billing.address_1,
    PLZ: billing.postcode,
    Ort: billing.city,
    Land: mapCountryCode(billing.country)
  };
  
  // Create contact information
  const kontakt: RzaKontakt = {
    Email: customer.email
  };
  
  if (billing.phone && config.mapPhoneToTelefon1) {
    kontakt.Telefon1 = formatPhoneNumber(billing.phone, {
      removeSpaces: true,
      removeHyphens: true,
      addCountryPrefix: false,
      defaultCountryPrefix: '+43'
    });
  }
  
  // Create alternative delivery address if different from billing
  let abweichendeLieferanschrift: RzaAbweichendeLieferanschrift | undefined;
  if (config.includeShippingAddress && hasCustomerShippingAddress(customer)) {
    abweichendeLieferanschrift = createAlternativeDeliveryAddressFromCustomer(shipping);
  }
  
  // Create internal information
  const interneInformationen: RzaAdresseInterneInformationen = {
    KundenAdresse: 'JA',
    WaehrungID: config.defaultWaehrungID,
    FiliallagerID: config.defaultFiliallagerID,
    Selektionen: config.defaultSelektionen,
    Erlösekonto: config.defaultErlösekonto,
    StandardZahlungskonditionID: config.defaultZahlungskonditionID
  };
  
  // Map custom fields if enabled
  if (config.useFreieFelder && customer.meta_data) {
    mapCustomFields(customer.meta_data, interneInformationen, config);
  }
  
  const address: RzaAdresse = {
    RefID: refID,
    ID: config.alwaysCreateNewCustomer ? '0' : customer.id.toString(),
    Zuname: billing.last_name || customer.last_name,
    Vorname: billing.first_name || customer.first_name,
    Anschrift: anschrift,
    Kontakt: kontakt,
    AdresseInterneInformationen: interneInformationen
  };
  
  // Add company if present
  if (billing.company && config.mapCompanyToFirma) {
    address.Firma = billing.company;
  }
  
  // Add alternative delivery address if created
  if (abweichendeLieferanschrift) {
    address.AbweichendeLieferanschrift = abweichendeLieferanschrift;
  }
  
  return address;
}

/**
 * Creates alternative delivery address from shipping information according to API contract
 * API Contract shipping address mapping:
 * - WooCommerce shipping.first_name + last_name -> RZA AbweichendeLieferanschrift.Textzeile[0] (full name combined)
 * - WooCommerce shipping.company -> RZA AbweichendeLieferanschrift.Textzeile[1] (direct, if present)
 * - WooCommerce shipping.address_1 -> RZA AbweichendeLieferanschrift.Textzeile[2] (direct)
 * - WooCommerce shipping.address_2 -> RZA AbweichendeLieferanschrift.Textzeile[3] (direct, if present)
 * - WooCommerce shipping.postcode + city -> RZA AbweichendeLieferanschrift.Textzeile[4] (combined)
 * - WooCommerce shipping.country -> RZA AbweichendeLieferanschrift.Textzeile[5] (only if ≠ AT)
 *
 * @param shipping - WooCommerce shipping address
 * @returns Alternative delivery address structure
 */
export function createAlternativeDeliveryAddress(
  shipping: WooCommerceOrder['shipping']
): RzaAbweichendeLieferanschrift {
  const textLines: RzaTextzeile[] = [];
  let lineNumber = 1;

  // API Contract: shipping.first_name + last_name -> Textzeile[0] (full name combined)
  if (shipping.first_name || shipping.last_name) {
    const name = `${shipping.first_name} ${shipping.last_name}`.trim();
    if (name) {
      textLines.push({ Zeile: lineNumber++, Text: name });
    }
  }

  // API Contract: shipping.company -> Textzeile[1] (direct, if present)
  if (shipping.company) {
    textLines.push({ Zeile: lineNumber++, Text: shipping.company });
  }

  // API Contract: shipping.address_1 -> Textzeile[2] (direct)
  if (shipping.address_1) {
    textLines.push({ Zeile: lineNumber++, Text: shipping.address_1 });
  }

  // API Contract: shipping.address_2 -> Textzeile[3] (direct, if present)
  if (shipping.address_2) {
    textLines.push({ Zeile: lineNumber++, Text: shipping.address_2 });
  }

  // API Contract: shipping.postcode + city -> Textzeile[4] (combined)
  const cityLine = `${shipping.postcode} ${shipping.city}`.trim();
  if (cityLine) {
    textLines.push({ Zeile: lineNumber++, Text: cityLine });
  }

  // API Contract: shipping.country -> Textzeile[5] (only if ≠ AT)
  if (shipping.country && shipping.country !== 'AT') {
    const countryName = mapCountryCode(shipping.country);
    if (countryName !== shipping.country) {
      textLines.push({ Zeile: lineNumber++, Text: countryName });
    }
  }

  return { Textzeile: textLines };
}

/**
 * Creates alternative delivery address from customer shipping information
 * 
 * @param shipping - WooCommerce customer shipping address
 * @returns Alternative delivery address structure
 */
export function createAlternativeDeliveryAddressFromCustomer(
  shipping: WooCommerceCustomer['shipping']
): RzaAbweichendeLieferanschrift {
  const textLines: RzaTextzeile[] = [];
  let lineNumber = 1;
  
  // Add name if present
  if (shipping.first_name || shipping.last_name) {
    const name = `${shipping.first_name} ${shipping.last_name}`.trim();
    if (name) {
      textLines.push({ Zeile: lineNumber++, Text: name });
    }
  }
  
  // Add company if present
  if (shipping.company) {
    textLines.push({ Zeile: lineNumber++, Text: shipping.company });
  }
  
  // Add address lines
  if (shipping.address_1) {
    textLines.push({ Zeile: lineNumber++, Text: shipping.address_1 });
  }
  
  if (shipping.address_2) {
    textLines.push({ Zeile: lineNumber++, Text: shipping.address_2 });
  }
  
  // Add city and postal code
  const cityLine = `${shipping.postcode} ${shipping.city}`.trim();
  if (cityLine) {
    textLines.push({ Zeile: lineNumber++, Text: cityLine });
  }
  
  // Add country if different from default
  if (shipping.country && shipping.country !== 'AT') {
    const countryName = mapCountryCode(shipping.country);
    if (countryName !== shipping.country) {
      textLines.push({ Zeile: lineNumber++, Text: countryName });
    }
  }
  
  return { Textzeile: textLines };
}

/**
 * Maps country code from WooCommerce to RZA format
 *
 * @param wooCountryCode - WooCommerce country code (ISO 2-letter)
 * @returns RZA country name or original code if not found
 */
export function mapCountryCode(wooCountryCode: string): string {
  const mapping = DEFAULT_COUNTRY_MAPPINGS.find(
    m => m.wooCommerceCode === wooCountryCode.toUpperCase()
  );
  return mapping ? mapping.rzaName : wooCountryCode;
}

/**
 * Formats phone number according to configuration
 *
 * @param phone - Original phone number
 * @param config - Phone formatting configuration
 * @returns Formatted phone number
 */
export function formatPhoneNumber(phone: string, config: PhoneFormatConfig): string {
  let formatted = phone;

  if (config.removeSpaces) {
    formatted = formatted.replace(/\s/g, '');
  }

  if (config.removeHyphens) {
    formatted = formatted.replace(/-/g, '');
  }

  if (config.addCountryPrefix && !formatted.startsWith('+')) {
    formatted = config.defaultCountryPrefix + formatted;
  }

  return formatted;
}

/**
 * Checks if order has shipping address different from billing
 *
 * @param order - WooCommerce order
 * @returns True if shipping address exists and is different
 */
export function hasShippingAddress(order: WooCommerceOrder): boolean {
  const shipping = order.shipping;
  const billing = order.billing;

  return !!(
    shipping.address_1 &&
    (shipping.address_1 !== billing.address_1 ||
     shipping.city !== billing.city ||
     shipping.postcode !== billing.postcode ||
     shipping.country !== billing.country)
  );
}

/**
 * Checks if customer has shipping address different from billing
 *
 * @param customer - WooCommerce customer
 * @returns True if shipping address exists and is different
 */
export function hasCustomerShippingAddress(customer: WooCommerceCustomer): boolean {
  const shipping = customer.shipping;
  const billing = customer.billing;

  return !!(
    shipping.address_1 &&
    (shipping.address_1 !== billing.address_1 ||
     shipping.city !== billing.city ||
     shipping.postcode !== billing.postcode ||
     shipping.country !== billing.country)
  );
}

/**
 * Maps custom fields from WooCommerce meta data to RZA free fields
 *
 * @param metaData - WooCommerce meta data array
 * @param interneInformationen - RZA internal information to update
 * @param config - Address mapping configuration
 */
export function mapCustomFields(
  metaData: Array<{ key: string; value: string }>,
  interneInformationen: RzaAdresseInterneInformationen,
  config: AddressMappingConfig
): void {
  const mapping = config.freieFelderMapping;

  if (mapping.feld1) {
    const meta = metaData.find(m => m.key === mapping.feld1);
    if (meta) interneInformationen.FreiesFeld1 = meta.value;
  }

  if (mapping.feld2) {
    const meta = metaData.find(m => m.key === mapping.feld2);
    if (meta) interneInformationen.FreiesFeld2 = meta.value;
  }

  if (mapping.feld3) {
    const meta = metaData.find(m => m.key === mapping.feld3);
    if (meta) interneInformationen.FreiesFeld3 = meta.value;
  }

  if (mapping.feld4) {
    const meta = metaData.find(m => m.key === mapping.feld4);
    if (meta) interneInformationen.FreiesFeld4 = meta.value;
  }

  if (mapping.feld5) {
    const meta = metaData.find(m => m.key === mapping.feld5);
    if (meta) interneInformationen.FreiesFeld5 = meta.value;
  }
}

/**
 * Validates RZA address data according to API contract
 * API Contract validation rules:
 * - Adressen-Validierung: first_name, last_name, address_1, postcode, city are required
 * - E-Mail-Validierung: Gültiges E-Mail-Format
 * - Länder-Validierung: ISO-Code muss in Mapping-Tabelle existieren
 * - PLZ-Validierung: Länder-spezifische Postleitzahl-Formate
 *
 * @param address - RZA address to validate
 * @param config - Address mapping configuration
 * @returns Validation result
 */
export function validateRzaAddress(
  address: RzaAdresse,
  config: AddressMappingConfig
): AddressValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required field validations
  if (!address.RefID || address.RefID <= 0) {
    errors.push('Address RefID is required and must be positive');
  }

  // API Contract: first_name, last_name are required
  if (!address.Zuname || address.Zuname.trim() === '') {
    errors.push('Last name (Zuname/last_name) is required');
  }

  if (!address.Vorname || address.Vorname.trim() === '') {
    errors.push('First name (Vorname/first_name) is required');
  }

  // API Contract: E-Mail-Validierung - Gültiges E-Mail-Format
  if (!address.Kontakt?.Email || address.Kontakt.Email.trim() === '') {
    errors.push('Email address is required');
  } else if (!isValidEmail(address.Kontakt.Email)) {
    errors.push('Email address format is invalid');
  }

  // API Contract: address_1, postcode, city are required
  if (!address.Anschrift?.Strasse || address.Anschrift.Strasse.trim() === '') {
    errors.push('Street address (address_1) is required');
  }

  if (!address.Anschrift?.PLZ || address.Anschrift.PLZ.trim() === '') {
    errors.push('Postal code (postcode) is required');
  }

  if (!address.Anschrift?.Ort || address.Anschrift.Ort.trim() === '') {
    errors.push('City is required');
  }

  // API Contract: PLZ-Validierung - Basic postal code format validation
  if (address.Anschrift?.PLZ) {
    const plz = address.Anschrift.PLZ;
    // Basic validation - should contain digits and be reasonable length
    if (!/^\d{3,10}$/.test(plz.replace(/[\s\-]/g, ''))) {
      warnings.push('Postal code format may be invalid');
    }
  }

  // API Contract: Länder-Validierung - Country code validation
  if (address.Anschrift?.Land) {
    const land = address.Anschrift.Land;
    // Should be either 2-letter ISO code or German country name
    if (land.length === 2 && !/^[A-Z]{2}$/.test(land)) {
      warnings.push('Country code should be 2-letter ISO format');
    }
  }

  // Warnings for missing optional data
  if (!address.Kontakt?.Telefon1 || address.Kontakt.Telefon1.trim() === '') {
    warnings.push('Phone number is missing');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Creates default address mapping configuration according to API contract
 * API Contract: Customers are ALWAYS created as new (alwaysCreateNewCustomer: true)
 *
 * @param overrides - Configuration overrides
 * @returns Default address mapping configuration
 */
export function createDefaultAddressConfig(
  overrides: Partial<AddressMappingConfig> = {}
): AddressMappingConfig {
  return {
    alwaysCreateNewCustomer: true, // API Contract: Customers are ALWAYS created as new
    defaultCustomerID: '0', // API Contract: Always use '0' for new customers
    includeShippingAddress: true,
    combineAddressLines: true,
    defaultWaehrungID: 21,
    defaultFiliallagerID: 1,
    defaultSelektionen: '001',
    defaultErlösekonto: '4000',
    defaultZahlungskonditionID: 1,
    mapCompanyToFirma: true,
    mapPhoneToTelefon1: true,
    useFreieFelder: false,
    freieFelderMapping: {},
    requireEmail: true,
    requireName: true,
    requireAddress: true,
    ...overrides
  };
}

/**
 * Simple email validation
 *
 * @param email - Email address to validate
 * @returns True if email format is valid
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
