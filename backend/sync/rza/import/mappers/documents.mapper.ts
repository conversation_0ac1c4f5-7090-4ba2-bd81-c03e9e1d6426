/**
 * Document mapper for RZA import system
 * Maps WooCommerce orders to RZA documents (invoices)
 */

import {
  RzaDokument,
  RzaPosition,
  RzaPositionArtikelinfo,
  RzaPositionstext,
  RzaPositionInterneInformationen,
  RzaSummen,
  RzaDokumentInterneInformationen,
  WooCommerceOrder,
  DocumentMappingConfig
} from '../models/document.model';
import { RzaImportConfig, RzaTextzeile } from '../models/common.model';

/**
 * Maps a WooCommerce order to an RZA document according to API contract
 * API Contract field mappings:
 * - WooCommerce order.id -> RZA Dokument.RefID (direct)
 * - WooCommerce order.date_created -> RZA Dokument.Datum (DD.MM.YYYY format)
 * - WooCommerce order.total -> RZA Summen.EndsummeBrutto (direct)
 * - WooCommerce order.total_tax -> RZA Summen.EndsummeMWSt (direct)
 * - WooCommerce order.total - order.total_tax -> RZA Summen.EndsummeNetto (calculated)
 * - WooCommerce order.currency -> RZA Dokument.Waehrung (direct)
 *
 * @param order - WooCommerce order to convert
 * @param config - RZA import configuration
 * @param documentConfig - Document-specific configuration
 * @param addressRefID - Reference ID for the associated address
 * @returns RZA document structure
 */
export function mapWooOrderToRzaDocument(
  order: WooCommerceOrder,
  config: RzaImportConfig,
  documentConfig: DocumentMappingConfig,
  addressRefID: number
): RzaDokument {
  const documentRefID = order.id; // WooCommerce order.id -> RZA Dokument.RefID (direct)

  // Format date from WooCommerce to RZA format (DD.MM.YYYY)
  const orderDate = formatDateForRza(order.date_created); // WooCommerce order.date_created -> RZA Dokument.Datum (DD.MM.YYYY format)

  // Map order positions according to API contract
  const positions = mapOrderLineItems(order, documentConfig);

  // Calculate document sums according to API contract
  const sums = calculateDocumentSums(order);

  // Create document internal information
  const interneInformationen: RzaDokumentInterneInformationen = {
    ID: 0, // Always 0 for new documents
    BelegkreisID: config.belegkreisID,
    FiliallagerID: config.filiallagerID,
    BenutzernamenID: config.benutzernamenID || 2,
    WaehrungID: config.waehrungID
  };

  const document: RzaDokument = {
    RefID: documentRefID,
    AdresseRefID: addressRefID,
    Art: documentConfig.documentType,
    Datum: orderDate,
    Waehrung: order.currency || config.currency, // WooCommerce order.currency -> RZA Dokument.Waehrung (direct)
    Summen: sums,
    Zahlungskondition: {
      Zieltage: 30 // Default payment terms, could be configurable
    },
    DokumentInterneInformationen: interneInformationen,
    Positionen: {
      Position: positions
    }
  };

  return document;
}

/**
 * Maps WooCommerce line items to RZA positions according to API contract
 * API Contract field mappings:
 * - WooCommerce item.id -> RZA Position.PositionInterneInformationen.ID (direct)
 * - WooCommerce item.product_id -> RZA Position.PositionArtikelinfo.ArtikelID (direct)
 * - WooCommerce item.sku -> RZA Position.PositionArtikelinfo.Artikelnummer (SKU or product_id)
 * - WooCommerce item.name -> RZA Position.Positionstext.Textzeile[0].Text (direct)
 * - WooCommerce item.quantity -> RZA Position.Menge (direct)
 * - WooCommerce item.price -> RZA Position.Preis (direct)
 * - WooCommerce item.total -> RZA Position.Betrag (direct)
 * - WooCommerce item.taxes[0] -> RZA Position.MWSt (percentage calculated)
 *
 * @param order - WooCommerce order
 * @param config - Document mapping configuration
 * @returns Array of RZA positions
 */
export function mapOrderLineItems(
  order: WooCommerceOrder,
  config: DocumentMappingConfig
): RzaPosition[] {
  return order.line_items.map((item, index) => {
    // Create article information according to API contract
    const articleInfo: RzaPositionArtikelinfo = {
      ArtikelID: item.product_id, // WooCommerce item.product_id -> RZA Position.PositionArtikelinfo.ArtikelID (direct)
      Artikelgruppe: config.defaultArtikelgruppe,
      Untergruppe: '', // Could be mapped from product categories
      Artikelnummer: item.sku || item.product_id.toString(), // WooCommerce item.sku -> RZA Position.PositionArtikelinfo.Artikelnummer (SKU or product_id)
      EAN: extractEANFromMeta(item)
    };

    // Create position text according to API contract
    const positionstext: RzaPositionstext = {
      Textzeile: [
        {
          Zeile: 1,
          Text: item.name // WooCommerce item.name -> RZA Position.Positionstext.Textzeile[0].Text (direct)
        }
      ]
    };

    // Calculate material costs if enabled
    let materialkosten: number | undefined;
    if (config.calculateMaterialkosten) {
      const itemTotal = parseFloat(item.total);
      materialkosten = itemTotal * (config.materialkostenPercentage / 100);
    }

    // Create position internal information according to API contract
    const interneInformationen: RzaPositionInterneInformationen = {
      ID: item.id, // WooCommerce item.id -> RZA Position.PositionInterneInformationen.ID (direct)
      Erloesekonto: config.defaultErlösekonto,
      Materialkosten: materialkosten
    };

    // Calculate tax rate from item according to API contract
    const taxRate = calculateItemTaxRate(item); // WooCommerce item.taxes[0] -> RZA Position.MWSt (percentage calculated)

    // Calculate position values according to API contract
    const preis = item.price; // WooCommerce item.price -> RZA Position.Preis (direct)
    const menge = item.quantity; // WooCommerce item.quantity -> RZA Position.Menge (direct)
    const betrag = parseFloat(item.total); // WooCommerce item.total -> RZA Position.Betrag (direct)

    const position: RzaPosition = {
      LfdNummer: index + 1,
      Menge: menge,
      Preis: preis,
      Betrag: betrag,
      MWSt: taxRate,
      Einheit: config.defaultUnit,
      Rabattfaehig: 'JA',
      PositionArtikelinfo: articleInfo,
      Positionstext: positionstext,
      PositionInterneInformationen: interneInformationen
    };

    return position;
  });
}

/**
 * Calculates document sums from WooCommerce order according to API contract
 * API Contract field mappings:
 * - WooCommerce order.total -> RZA Summen.EndsummeBrutto (direct)
 * - WooCommerce order.total_tax -> RZA Summen.EndsummeMWSt (direct)
 * - WooCommerce order.total - order.total_tax -> RZA Summen.EndsummeNetto (calculated)
 *
 * @param order - WooCommerce order
 * @returns RZA sums structure
 */
export function calculateDocumentSums(order: WooCommerceOrder): RzaSummen {
  const bruttosumme = parseFloat(order.total); // WooCommerce order.total -> RZA Summen.EndsummeBrutto (direct)
  const steuersumme = parseFloat(order.total_tax); // WooCommerce order.total_tax -> RZA Summen.EndsummeMWSt (direct)
  const nettosumme = bruttosumme - steuersumme; // WooCommerce order.total - order.total_tax -> RZA Summen.EndsummeNetto (calculated)

  return {
    EndsummeNetto: nettosumme,
    EndsummeMWSt: steuersumme,
    EndsummeBrutto: bruttosumme
  };
}

/**
 * Extracts EAN from product meta data
 * 
 * @param item - WooCommerce line item
 * @returns EAN string or undefined
 */
export function extractEANFromMeta(item: WooCommerceOrder['line_items'][0]): string | undefined {
  if (!item.meta_data) return undefined;
  
  const eanKeys = ['_ean', 'ean', 'barcode', 'gtin', '_gtin'];
  const eanMeta = item.meta_data.find(meta => 
    eanKeys.includes(meta.key.toLowerCase())
  );
  
  return eanMeta?.value;
}

/**
 * Calculates tax rate for a line item
 * 
 * @param item - WooCommerce line item
 * @returns Tax rate as percentage
 */
export function calculateItemTaxRate(item: WooCommerceOrder['line_items'][0]): number {
  if (!item.taxes || item.taxes.length === 0) {
    return 0;
  }
  
  // Calculate tax rate from first tax entry
  const tax = item.taxes[0];
  const subtotal = parseFloat(item.subtotal);
  const taxAmount = parseFloat(tax.total);
  
  if (subtotal === 0) return 0;
  
  return Math.round((taxAmount / subtotal) * 100);
}

/**
 * Formats a date string to RZA format (DD.MM.YYYY)
 * 
 * @param dateString - ISO date string from WooCommerce
 * @returns Formatted date string
 */
export function formatDateForRza(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}.${month}.${year}`;
}

/**
 * Formats a datetime string to RZA format (DD.MM.YYYY HH:mm)
 * 
 * @param dateString - ISO datetime string
 * @returns Formatted datetime string
 */
export function formatDateTimeForRza(dateString: string): string {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  return `${day}.${month}.${year} ${hours}:${minutes}`;
}

/**
 * Creates default document mapping configuration
 * 
 * @param overrides - Configuration overrides
 * @returns Default document mapping configuration
 */
export function createDefaultDocumentConfig(
  overrides: Partial<DocumentMappingConfig> = {}
): DocumentMappingConfig {
  return {
    documentType: 'Auftrag',
    defaultUnit: 'Stk.',
    defaultTaxRate: 20,
    defaultErlösekonto: '4020',
    defaultArtikelgruppe: '01',
    includeTaxInPrice: true,
    includeProductImages: false,
    includeProductMeta: true,
    calculateMaterialkosten: true,
    materialkostenPercentage: 60,
    ...overrides
  };
}

/**
 * Validates document data before XML generation according to API contract
 * API Contract validation rules:
 * - Bestellungs-Validierung: order.id, order.date_created, order.total are required
 * - Währungs-Validierung: Unterstützte Währungen (EUR, USD, CHF)
 * - Summen-Validierung: Netto + MwSt = Brutto
 * - Positions-Validierung: Mindestens eine Position erforderlich
 *
 * @param document - RZA document to validate
 * @returns Array of validation errors
 */
export function validateRzaDocument(document: RzaDokument): string[] {
  const errors: string[] = [];

  // API Contract: Bestellungs-Validierung - order.id is required
  if (!document.RefID || document.RefID <= 0) {
    errors.push('Document RefID (order.id) is required and must be positive');
  }

  if (!document.AdresseRefID || document.AdresseRefID <= 0) {
    errors.push('Address RefID is required and must be positive');
  }

  // API Contract: order.date_created is required and must be in DD.MM.YYYY format
  if (!document.Datum || !document.Datum.match(/^\d{2}\.\d{2}\.\d{4}$/)) {
    errors.push('Document date (order.date_created) must be in DD.MM.YYYY format');
  }

  // API Contract: Währungs-Validierung - Unterstützte Währungen (EUR, USD, CHF)
  const supportedCurrencies = ['EUR', 'USD', 'CHF'];
  if (!document.Waehrung || !supportedCurrencies.includes(document.Waehrung)) {
    errors.push(`Currency must be one of: ${supportedCurrencies.join(', ')}`);
  }

  // API Contract: Positions-Validierung - Mindestens eine Position erforderlich
  if (!document.Positionen || !document.Positionen.Position || document.Positionen.Position.length === 0) {
    errors.push('At least one position is required');
  }

  // API Contract: Summen-Validierung - Netto + MwSt = Brutto
  if (document.Summen) {
    const netto = document.Summen.EndsummeNetto;
    const mwst = document.Summen.EndsummeMWSt;
    const brutto = document.Summen.EndsummeBrutto;
    const calculatedBrutto = netto + mwst;

    if (Math.abs(brutto - calculatedBrutto) > 0.01) { // Allow for small rounding differences
      errors.push(`Sum validation failed: Netto (${netto}) + MwSt (${mwst}) ≠ Brutto (${brutto})`);
    }
  }

  // Validate positions according to API contract
  document.Positionen?.Position?.forEach((position, index) => {
    if (!position.LfdNummer || position.LfdNummer <= 0) {
      errors.push(`Position ${index + 1}: LfdNummer is required and must be positive`);
    }

    // API Contract: Mengen-Validierung - Menge > 0
    if (!position.Menge || position.Menge <= 0) {
      errors.push(`Position ${index + 1}: Quantity (Menge) must be > 0`);
    }

    // API Contract: Preis-Validierung - Preis ≥ 0
    if (position.Preis !== undefined && position.Preis < 0) {
      errors.push(`Position ${index + 1}: Price (Preis) must be ≥ 0`);
    }

    // API Contract: MwSt-Validierung - Gültiger Steuersatz (0-100%)
    if (position.MWSt !== undefined && (position.MWSt < 0 || position.MWSt > 100)) {
      errors.push(`Position ${index + 1}: Tax rate (MWSt) must be between 0 and 100%`);
    }

    if (!position.PositionArtikelinfo) {
      errors.push(`Position ${index + 1}: PositionArtikelinfo is required`);
    } else {
      // API Contract: product_id or sku, name, quantity, price are required
      if (!position.PositionArtikelinfo.ArtikelID && !position.PositionArtikelinfo.Artikelnummer) {
        errors.push(`Position ${index + 1}: Either ArtikelID (product_id) or Artikelnummer (sku) is required`);
      }
    }
  });

  return errors;
}

/**
 * Extracts discount information from WooCommerce order
 *
 * @param order - WooCommerce order
 * @returns Discount percentage or undefined
 */
export function extractDiscountFromOrder(order: WooCommerceOrder): number | undefined {
  const discountTotal = parseFloat(order.discount_total);
  if (discountTotal <= 0) return undefined;

  const subtotal = order.line_items.reduce((sum, item) => {
    return sum + parseFloat(item.subtotal);
  }, 0);

  if (subtotal <= 0) return undefined;

  return Math.round((discountTotal / subtotal) * 100);
}
