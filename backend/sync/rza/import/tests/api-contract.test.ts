/**
 * Integration tests for RZA import according to API contract
 * Tests verify that WooCommerce to RZA mapping matches the exact specifications in API_Contract.md
 */

import {
  mapWooOrderToRzaDocument,
  calculateDocumentSums,
  mapOrderLineItems,
  validateRzaDocument,
  mapOrderBillingToRzaAddress,
  createAlternativeDeliveryAddress,
  validateRzaAddress,
  createDefaultDocumentConfig,
  createDefaultAddressConfig
} from '../index';

import { WooCommerceOrder } from '../models/document.model';
import { RzaImportConfig } from '../models/common.model';

describe('API Contract Compliance Tests - Import', () => {
  
  const mockRzaConfig: RzaImportConfig = {
    currency: 'EUR',
    belegkreisID: 1,
    filiallagerID: 1,
    waehrungID: 21,
    verarbeitung: 'ECHT'
  };

  describe('Document Field Mappings according to API Contract', () => {
    
    test('should map order fields to RZA document correctly', () => {
      const wooOrder: WooCommerceOrder = {
        id: 1001,
        date_created: '2022-02-28T10:00:00',
        total: '120.00',
        total_tax: '20.00',
        currency: 'EUR',
        line_items: [
          {
            id: 1,
            product_id: 774,
            sku: 'ARTIKEL-123',
            name: 'Test Product',
            quantity: 2,
            price: 50.00,
            total: '100.00',
            taxes: [{ total: '20.00' }],
            meta_data: []
          }
        ],
        billing: {
          first_name: 'Max',
          last_name: 'Mustermann',
          email: '<EMAIL>',
          phone: '0664/1234567',
          address_1: 'Musterstraße 1',
          address_2: '',
          postcode: '1010',
          city: 'Wien',
          country: 'AT',
          company: ''
        },
        shipping: {
          first_name: 'Max',
          last_name: 'Mustermann',
          address_1: 'Musterstraße 1',
          address_2: '',
          postcode: '1010',
          city: 'Wien',
          country: 'AT',
          company: ''
        }
      };

      const documentConfig = createDefaultDocumentConfig();
      const rzaDocument = mapWooOrderToRzaDocument(wooOrder, mockRzaConfig, documentConfig, 1);

      // API Contract field mappings verification
      expect(rzaDocument.RefID).toBe(1001); // order.id -> Dokument.RefID (direct)
      expect(rzaDocument.Datum).toBe('28.02.2022'); // order.date_created -> Dokument.Datum (DD.MM.YYYY format)
      expect(rzaDocument.Waehrung).toBe('EUR'); // order.currency -> Dokument.Waehrung (direct)
      
      // Verify sums according to API contract
      expect(rzaDocument.Summen.EndsummeBrutto).toBe(120.00); // order.total -> Summen.EndsummeBrutto (direct)
      expect(rzaDocument.Summen.EndsummeMWSt).toBe(20.00); // order.total_tax -> Summen.EndsummeMWSt (direct)
      expect(rzaDocument.Summen.EndsummeNetto).toBe(100.00); // order.total - order.total_tax -> Summen.EndsummeNetto (calculated)
    });

    test('should map line items according to API contract', () => {
      const wooOrder: WooCommerceOrder = {
        id: 1001,
        date_created: '2022-02-28T10:00:00',
        total: '120.00',
        total_tax: '20.00',
        currency: 'EUR',
        line_items: [
          {
            id: 1,
            product_id: 774,
            sku: 'ARTIKEL-123',
            name: 'Test Product',
            quantity: 2,
            price: 50.00,
            total: '100.00',
            taxes: [{ total: '20.00' }],
            meta_data: []
          }
        ],
        billing: {} as any,
        shipping: {} as any
      };

      const documentConfig = createDefaultDocumentConfig();
      const positions = mapOrderLineItems(wooOrder, documentConfig);

      expect(positions).toHaveLength(1);
      
      const position = positions[0];
      
      // API Contract position field mappings verification
      expect(position.PositionArtikelinfo.ArtikelID).toBe(774); // item.product_id -> Position.PositionArtikelinfo.ArtikelID (direct)
      expect(position.PositionArtikelinfo.Artikelnummer).toBe('ARTIKEL-123'); // item.sku -> Position.PositionArtikelinfo.Artikelnummer (SKU or product_id)
      expect(position.Positionstext.Textzeile[0].Text).toBe('Test Product'); // item.name -> Position.Positionstext.Textzeile[0].Text (direct)
      expect(position.Menge).toBe(2); // item.quantity -> Position.Menge (direct)
      expect(position.Preis).toBe(50.00); // item.price -> Position.Preis (direct)
      expect(position.Betrag).toBe(100.00); // item.total -> Position.Betrag (direct)
      expect(position.PositionInterneInformationen.ID).toBe(1); // item.id -> Position.PositionInterneInformationen.ID (direct)
    });
  });

  describe('Address Field Mappings according to API Contract', () => {
    
    test('should map billing address according to API contract', () => {
      const wooOrder: WooCommerceOrder = {
        id: 1001,
        billing: {
          first_name: 'Max',
          last_name: 'Mustermann',
          email: '<EMAIL>',
          phone: '0664/1234567',
          address_1: 'Musterstraße 1',
          address_2: 'Apartment 2',
          postcode: '1010',
          city: 'Wien',
          country: 'AT',
          company: 'Test Company'
        },
        shipping: {
          first_name: 'Max',
          last_name: 'Mustermann',
          address_1: 'Musterstraße 1',
          address_2: '',
          postcode: '1010',
          city: 'Wien',
          country: 'AT',
          company: ''
        }
      } as WooCommerceOrder;

      const addressConfig = createDefaultAddressConfig();
      const rzaAddress = mapOrderBillingToRzaAddress(wooOrder, 1, addressConfig);

      // API Contract address field mappings verification
      expect(rzaAddress.Zuname).toBe('Mustermann'); // billing.last_name -> Adresse.Zuname (direct)
      expect(rzaAddress.Vorname).toBe('Max'); // billing.first_name -> Adresse.Vorname (direct)
      expect(rzaAddress.Firma).toBe('Test Company'); // billing.company -> Adresse.Firma (direct)
      expect(rzaAddress.Anschrift.Strasse).toBe('Musterstraße 1 Apartment 2'); // billing.address_1 + billing.address_2 -> Anschrift.Strasse (combined)
      expect(rzaAddress.Anschrift.PLZ).toBe('1010'); // billing.postcode -> Anschrift.PLZ (direct)
      expect(rzaAddress.Anschrift.Ort).toBe('Wien'); // billing.city -> Anschrift.Ort (direct)
      expect(rzaAddress.Kontakt.Email).toBe('<EMAIL>'); // billing.email -> Kontakt.Email (direct)
      expect(rzaAddress.ID).toBe('0'); // API Contract: Customers are ALWAYS created as new (ID='0')
    });

    test('should create alternative delivery address according to API contract', () => {
      const shipping = {
        first_name: 'John',
        last_name: 'Doe',
        company: 'Delivery Company',
        address_1: 'Delivery Street 1',
        address_2: 'Building B',
        postcode: '2020',
        city: 'Delivery City',
        country: 'DE'
      };

      const alternativeAddress = createAlternativeDeliveryAddress(shipping);

      // API Contract shipping address mapping verification
      expect(alternativeAddress.Textzeile[0].Text).toBe('John Doe'); // shipping.first_name + last_name -> Textzeile[0] (full name combined)
      expect(alternativeAddress.Textzeile[1].Text).toBe('Delivery Company'); // shipping.company -> Textzeile[1] (direct, if present)
      expect(alternativeAddress.Textzeile[2].Text).toBe('Delivery Street 1'); // shipping.address_1 -> Textzeile[2] (direct)
      expect(alternativeAddress.Textzeile[3].Text).toBe('Building B'); // shipping.address_2 -> Textzeile[3] (direct, if present)
      expect(alternativeAddress.Textzeile[4].Text).toBe('2020 Delivery City'); // shipping.postcode + city -> Textzeile[4] (combined)
      expect(alternativeAddress.Textzeile[5].Text).toBe('Deutschland'); // shipping.country -> Textzeile[5] (only if ≠ AT)
    });
  });

  describe('Validation according to API Contract', () => {
    
    test('should validate document according to API contract', () => {
      const invalidDocument = {
        RefID: 0, // Invalid: must be positive
        AdresseRefID: 1,
        Art: 'Auftrag',
        Datum: 'invalid-date', // Invalid: must be DD.MM.YYYY format
        Waehrung: 'INVALID', // Invalid: must be EUR, USD, or CHF
        Summen: {
          EndsummeNetto: 100.00,
          EndsummeMWSt: 20.00,
          EndsummeBrutto: 130.00 // Invalid: Netto + MwSt ≠ Brutto
        },
        Positionen: {
          Position: [] // Invalid: at least one position required
        }
      } as any;

      const errors = validateRzaDocument(invalidDocument);

      expect(errors).toContain('Document RefID (order.id) is required and must be positive');
      expect(errors).toContain('Document date (order.date_created) must be in DD.MM.YYYY format');
      expect(errors).toContain('Currency must be one of: EUR, USD, CHF');
      expect(errors).toContain('At least one position is required');
      expect(errors.some(error => error.includes('Sum validation failed'))).toBe(true);
    });

    test('should validate address according to API contract', () => {
      const invalidAddress = {
        RefID: 1,
        ID: '0',
        Zuname: '', // Invalid: required
        Vorname: '', // Invalid: required
        Anschrift: {
          Strasse: '', // Invalid: required
          PLZ: '', // Invalid: required
          Ort: '' // Invalid: required
        },
        Kontakt: {
          Email: 'invalid-email' // Invalid: format
        }
      } as any;

      const addressConfig = createDefaultAddressConfig();
      const validation = validateRzaAddress(invalidAddress, addressConfig);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Last name (Zuname/last_name) is required');
      expect(validation.errors).toContain('First name (Vorname/first_name) is required');
      expect(validation.errors).toContain('Street address (address_1) is required');
      expect(validation.errors).toContain('Postal code (postcode) is required');
      expect(validation.errors).toContain('City is required');
      expect(validation.errors).toContain('Email address format is invalid');
    });

    test('should validate position fields according to API contract', () => {
      const wooOrder: WooCommerceOrder = {
        id: 1001,
        line_items: [
          {
            id: 1,
            product_id: 0, // Invalid: should be positive
            sku: '', // Invalid: either product_id or sku required
            name: 'Test Product',
            quantity: 0, // Invalid: must be > 0
            price: -10.00, // Invalid: must be ≥ 0
            total: '100.00',
            taxes: [{ total: '150.00' }], // Invalid: tax rate > 100%
            meta_data: []
          }
        ]
      } as any;

      const documentConfig = createDefaultDocumentConfig();
      const positions = mapOrderLineItems(wooOrder, documentConfig);
      
      // Create a mock document to test validation
      const document = {
        RefID: 1001,
        AdresseRefID: 1,
        Art: 'Auftrag',
        Datum: '28.02.2022',
        Waehrung: 'EUR',
        Summen: {
          EndsummeNetto: 100.00,
          EndsummeMWSt: 20.00,
          EndsummeBrutto: 120.00
        },
        Positionen: {
          Position: positions
        }
      } as any;

      const errors = validateRzaDocument(document);

      expect(errors.some(error => error.includes('Quantity (Menge) must be > 0'))).toBe(true);
      expect(errors.some(error => error.includes('Price (Preis) must be ≥ 0'))).toBe(true);
      expect(errors.some(error => error.includes('Either ArtikelID (product_id) or Artikelnummer (sku) is required'))).toBe(true);
    });
  });

  describe('Customer Creation according to API Contract', () => {
    
    test('should always create new customers with ID=0', () => {
      const wooOrder: WooCommerceOrder = {
        id: 1001,
        billing: {
          first_name: 'Max',
          last_name: 'Mustermann',
          email: '<EMAIL>',
          phone: '0664/1234567',
          address_1: 'Musterstraße 1',
          address_2: '',
          postcode: '1010',
          city: 'Wien',
          country: 'AT',
          company: ''
        },
        shipping: {
          first_name: 'Max',
          last_name: 'Mustermann',
          address_1: 'Musterstraße 1',
          address_2: '',
          postcode: '1010',
          city: 'Wien',
          country: 'AT',
          company: ''
        }
      } as WooCommerceOrder;

      const addressConfig = createDefaultAddressConfig();
      const rzaAddress = mapOrderBillingToRzaAddress(wooOrder, 1, addressConfig);

      // API Contract: Customers are ALWAYS created as new (ID='0')
      expect(rzaAddress.ID).toBe('0');
      expect(addressConfig.alwaysCreateNewCustomer).toBe(true);
    });
  });
});
