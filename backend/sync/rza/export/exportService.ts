/**
 * RZA Export Service
 * Orchestrates the complete export process from RZA to WooCommerce
 */

import { promises as fs } from 'fs';
import { parseStringPromise } from 'xml2js';
import {
  RzaExport,
  RzaArticle,
  RzaCustomer,
  RzaCategory,
  RzaGroupDefinition,
  WooCommerceProduct,
  WooCommerceCustomer,
  WooCommerceCategory,
  mapRzaArticleToWooProduct,
  mapRzaArticlesToWooProducts,
  mapRzaCustomerToWooCustomer,
  mapRzaCustomersToWooCustomers,
  mapRzaCategoriesToWooCategories,
  mapRzaGroupsToWooCategories,
  ArticleMappingConfig,
  CustomerMappingConfig,
  CategoryMappingConfig
} from './index';

/**
 * Configuration for the complete export process
 * Note: Stock export is now handled by the standalone lagerstand module
 */
export interface ExportServiceConfig {
  // File paths
  xmlFilePath: string;
  outputDirectory?: string;

  // Mapping configurations
  articleConfig?: Partial<ArticleMappingConfig>;
  customerConfig?: Partial<CustomerMappingConfig>;
  categoryConfig?: Partial<CategoryMappingConfig>;

  // Export options
  exportArticles: boolean;
  exportCustomers: boolean;
  exportCategories: boolean;
  exportGroups: boolean;

  // Output options
  generateJsonFiles: boolean;
  generateCsvFiles: boolean;
  logProgress: boolean;
}

/**
 * Default export configuration
 * Note: Stock export is now handled by the standalone lagerstand module
 */
export const DEFAULT_EXPORT_CONFIG: ExportServiceConfig = {
  xmlFilePath: '',
  outputDirectory: './tmp/rza-export',
  exportArticles: true,
  exportCustomers: true,
  exportCategories: true,
  exportGroups: true,
  generateJsonFiles: true,
  generateCsvFiles: false,
  logProgress: true
};

/**
 * Result of the export process
 */
export interface ExportResult {
  success: boolean;
  message: string;
  stats: {
    articlesProcessed: number;
    customersProcessed: number;
    categoriesProcessed: number;
    groupsProcessed: number;
    errors: string[];
  };
  outputFiles: string[];
}

/**
 * Main RZA Export Service class
 */
export class RzaExportService {
  private config: ExportServiceConfig;

  constructor(config: Partial<ExportServiceConfig>) {
    this.config = { ...DEFAULT_EXPORT_CONFIG, ...config };
  }

  /**
   * Executes the complete export process
   * 
   * @returns Export result with statistics and file paths
   */
  async executeExport(): Promise<ExportResult> {
    const result: ExportResult = {
      success: false,
      message: '',
      stats: {
        articlesProcessed: 0,
        customersProcessed: 0,
        categoriesProcessed: 0,
        groupsProcessed: 0,
        errors: []
      },
      outputFiles: []
    };

    try {
      this.log('Starting RZA export process...');

      // Parse XML file
      const rzaData = await this.parseRzaXml(this.config.xmlFilePath);
      this.log(`Parsed RZA XML with ${rzaData.articles.length} articles, ${rzaData.customers.length} customers`);

      // Create output directory
      if (this.config.outputDirectory) {
        await this.ensureDirectory(this.config.outputDirectory);
      }

      // Export articles (stock export is now handled by standalone lagerstand module)
      if (this.config.exportArticles && rzaData.articles.length > 0) {
        const articleResult = await this.exportArticles(rzaData.articles, rzaData.categories);
        result.stats.articlesProcessed = articleResult.count;
        result.outputFiles.push(...articleResult.files);
        result.stats.errors.push(...articleResult.errors);
      }

      // Export customers
      if (this.config.exportCustomers && rzaData.customers.length > 0) {
        const customerResult = await this.exportCustomers(rzaData.customers);
        result.stats.customersProcessed = customerResult.count;
        result.outputFiles.push(...customerResult.files);
        result.stats.errors.push(...customerResult.errors);
      }

      // Export categories
      if (this.config.exportCategories && rzaData.categories.length > 0) {
        const categoryResult = await this.exportCategories(rzaData.categories);
        result.stats.categoriesProcessed = categoryResult.count;
        result.outputFiles.push(...categoryResult.files);
        result.stats.errors.push(...categoryResult.errors);
      }

      // Export groups
      if (this.config.exportGroups && rzaData.groupDefinition) {
        const groupResult = await this.exportGroups(rzaData.groupDefinition);
        result.stats.groupsProcessed = groupResult.count;
        result.outputFiles.push(...groupResult.files);
        result.stats.errors.push(...groupResult.errors);
      }

      result.success = true;
      result.message = `Export completed successfully. Processed ${result.stats.articlesProcessed} articles, ${result.stats.customersProcessed} customers, ${result.stats.categoriesProcessed} categories.`;
      
      this.log(result.message);
      
      if (result.stats.errors.length > 0) {
        this.log(`Encountered ${result.stats.errors.length} errors during export.`);
      }

    } catch (error) {
      result.success = false;
      result.message = `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.stats.errors.push(result.message);
      this.log(result.message);
    }

    return result;
  }

  /**
   * Parses RZA XML file into structured data
   * 
   * @param filePath - Path to the RZA XML file
   * @returns Parsed RZA data structure
   */
  private async parseRzaXml(filePath: string): Promise<RzaExport> {
    const xmlContent = await fs.readFile(filePath, 'utf-8');
    const parsed = await parseStringPromise(xmlContent);

    // Extract data from parsed XML structure
    // This is a simplified parser - you may need to adjust based on actual XML structure
    const rzaData: RzaExport = {
      articles: this.extractArticles(parsed),
      customers: this.extractCustomers(parsed),
      orders: this.extractOrders(parsed),
      groupDefinition: this.extractGroupDefinition(parsed),
      categories: this.extractCategories(parsed),
      countries: this.extractCountries(parsed)
    };

    return rzaData;
  }

  /**
   * Exports articles to WooCommerce format
   */
  private async exportArticles(
    articles: RzaArticle[],
    categories: RzaCategory[]
  ): Promise<{ count: number; files: string[]; errors: string[] }> {
    const result: { count: number; files: string[]; errors: string[] } = { count: 0, files: [], errors: [] };

    try {
      const wooProducts = mapRzaArticlesToWooProducts(articles, categories, this.config.articleConfig);
      result.count = wooProducts.length;

      if (this.config.generateJsonFiles && this.config.outputDirectory) {
        const filePath = `${this.config.outputDirectory}/woocommerce-products.json`;
        await fs.writeFile(filePath, JSON.stringify(wooProducts, null, 2));
        result.files.push(filePath);
      }

    } catch (error) {
      result.errors.push(`Article export error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }



  /**
   * Exports customers to WooCommerce format
   */
  private async exportCustomers(
    customers: RzaCustomer[]
  ): Promise<{ count: number; files: string[]; errors: string[] }> {
    const result: { count: number; files: string[]; errors: string[] } = { count: 0, files: [], errors: [] };

    try {
      const wooCustomers = mapRzaCustomersToWooCustomers(customers, this.config.customerConfig);
      result.count = wooCustomers.length;

      if (this.config.generateJsonFiles && this.config.outputDirectory) {
        const filePath = `${this.config.outputDirectory}/woocommerce-customers.json`;
        await fs.writeFile(filePath, JSON.stringify(wooCustomers, null, 2));
        result.files.push(filePath);
      }

    } catch (error) {
      result.errors.push(`Customer export error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Exports categories to WooCommerce format
   */
  private async exportCategories(
    categories: RzaCategory[]
  ): Promise<{ count: number; files: string[]; errors: string[] }> {
    const result: { count: number; files: string[]; errors: string[] } = { count: 0, files: [], errors: [] };

    try {
      const wooCategories = mapRzaCategoriesToWooCategories(categories, this.config.categoryConfig);
      result.count = wooCategories.length;

      if (this.config.generateJsonFiles && this.config.outputDirectory) {
        const filePath = `${this.config.outputDirectory}/woocommerce-categories.json`;
        await fs.writeFile(filePath, JSON.stringify(wooCategories, null, 2));
        result.files.push(filePath);
      }

    } catch (error) {
      result.errors.push(`Category export error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Exports article groups to WooCommerce format
   */
  private async exportGroups(
    groupDefinition: RzaGroupDefinition
  ): Promise<{ count: number; files: string[]; errors: string[] }> {
    const result: { count: number; files: string[]; errors: string[] } = { count: 0, files: [], errors: [] };

    try {
      const wooCategories = mapRzaGroupsToWooCategories(groupDefinition, this.config.categoryConfig);
      result.count = wooCategories.length;

      if (this.config.generateJsonFiles && this.config.outputDirectory) {
        const filePath = `${this.config.outputDirectory}/woocommerce-groups.json`;
        await fs.writeFile(filePath, JSON.stringify(wooCategories, null, 2));
        result.files.push(filePath);
      }

    } catch (error) {
      result.errors.push(`Group export error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  // Placeholder extraction methods - these need to be implemented based on actual XML structure
  private extractArticles(parsed: any): RzaArticle[] {
    // TODO: Implement based on actual XML structure
    return [];
  }

  private extractCustomers(parsed: any): RzaCustomer[] {
    // TODO: Implement based on actual XML structure
    return [];
  }

  private extractOrders(parsed: any): Array<{ ordernumber: string; orderstatus: string }> {
    // TODO: Implement based on actual XML structure
    return [];
  }

  private extractGroupDefinition(parsed: any): RzaGroupDefinition {
    // TODO: Implement based on actual XML structure
    return { artGroups: [], artSubGroups: [] };
  }

  private extractCategories(parsed: any): RzaCategory[] {
    // TODO: Implement based on actual XML structure
    return [];
  }

  private extractCountries(parsed: any): Array<{ ID: number; name: string; ISO: string }> {
    // TODO: Implement based on actual XML structure
    return [];
  }

  private async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  private log(message: string): void {
    if (this.config.logProgress) {
      console.log(`[RzaExportService] ${message}`);
    }
  }
}
