/**
 * Article group and category mapping functions for RZA to WooCommerce conversion
 * Handles category hierarchy and group-based discounts
 */

import {
  RzaArtGroup,
  RzaArtSubGroup,
  RzaGroupDefinition
} from '../models/articleGroup.model';
import {
  RzaCategory,
  CategoryHierarchy,
  WooCommerceCategory,
  CategoryMappingConfig
} from '../models/category.model';
import { RzaDiscount } from '../models/common.model';

/**
 * Default configuration for category mapping
 */
export const DEFAULT_CATEGORY_CONFIG: CategoryMappingConfig = {
  createHierarchy: true,
  maxDepth: 5,
  slugPrefix: 'rza-',
  slugSuffix: '',
  includeIdInSlug: false,
  defaultDisplay: 'default',
  defaultDescription: '',
  includeDiscounts: false,
  updateExisting: true,
  deleteOrphaned: false,
  preserveCustomFields: true
};

/**
 * Maps RZA article groups to WooCommerce categories
 * 
 * @param groupDefinition - RZA group definition with main groups and sub-groups
 * @param config - Mapping configuration
 * @returns Array of WooCommerce categories
 */
export function mapRzaGroupsToWooCategories(
  groupDefinition: RzaGroupDefinition,
  config: Partial<CategoryMappingConfig> = {}
): WooCommerceCategory[] {
  const mappingConfig = { ...DEFAULT_CATEGORY_CONFIG, ...config };
  const categories: WooCommerceCategory[] = [];

  // Map main article groups
  groupDefinition.artGroups.forEach(group => {
    const category = mapArtGroupToWooCategory(group, mappingConfig);
    categories.push(category);
  });

  // Map sub-groups as child categories if hierarchy is enabled
  if (mappingConfig.createHierarchy) {
    groupDefinition.artSubGroups.forEach(subGroup => {
      const parentGroup = groupDefinition.artGroups.find(g => g.ID === subGroup.ID);
      const category = mapArtSubGroupToWooCategory(
        subGroup, 
        parentGroup?.ID,
        mappingConfig
      );
      categories.push(category);
    });
  } else {
    // Map sub-groups as top-level categories
    groupDefinition.artSubGroups.forEach(subGroup => {
      const category = mapArtSubGroupToWooCategory(subGroup, undefined, mappingConfig);
      categories.push(category);
    });
  }

  return categories;
}

/**
 * Maps RZA product categories to WooCommerce categories
 * 
 * @param rzaCategories - Array of RZA categories
 * @param config - Mapping configuration
 * @returns Array of WooCommerce categories with hierarchy
 */
export function mapRzaCategoriesToWooCategories(
  rzaCategories: RzaCategory[],
  config: Partial<CategoryMappingConfig> = {}
): WooCommerceCategory[] {
  const mappingConfig = { ...DEFAULT_CATEGORY_CONFIG, ...config };

  if (mappingConfig.createHierarchy) {
    return mapCategoriesWithHierarchy(rzaCategories, mappingConfig);
  } else {
    return rzaCategories.map(category => 
      mapRzaCategoryToWooCategory(category, mappingConfig)
    );
  }
}

/**
 * Maps a single RZA article group to WooCommerce category
 * 
 * @param artGroup - RZA article group
 * @param config - Mapping configuration
 * @returns WooCommerce category
 */
function mapArtGroupToWooCategory(
  artGroup: RzaArtGroup,
  config: CategoryMappingConfig
): WooCommerceCategory {
  const category: WooCommerceCategory = {
    name: artGroup.name,
    slug: generateCategorySlug(artGroup.name, artGroup.ID, config),
    description: buildGroupDescription(artGroup, config),
    display: config.defaultDisplay,
    menu_order: artGroup.number
  };

  return category;
}

/**
 * Maps a single RZA article sub-group to WooCommerce category
 * 
 * @param artSubGroup - RZA article sub-group
 * @param parentId - Parent category ID (optional)
 * @param config - Mapping configuration
 * @returns WooCommerce category
 */
function mapArtSubGroupToWooCategory(
  artSubGroup: RzaArtSubGroup,
  parentId: number | undefined,
  config: CategoryMappingConfig
): WooCommerceCategory {
  const category: WooCommerceCategory = {
    name: artSubGroup.name,
    slug: generateCategorySlug(artSubGroup.name, artSubGroup.ID, config),
    description: buildSubGroupDescription(artSubGroup, config),
    display: config.defaultDisplay,
    menu_order: artSubGroup.number
  };

  if (parentId !== undefined) {
    category.parent = parentId;
  }

  return category;
}

/**
 * Maps a single RZA category to WooCommerce category
 * 
 * @param rzaCategory - RZA category
 * @param config - Mapping configuration
 * @returns WooCommerce category
 */
function mapRzaCategoryToWooCategory(
  rzaCategory: RzaCategory,
  config: CategoryMappingConfig
): WooCommerceCategory {
  const category: WooCommerceCategory = {
    name: rzaCategory.name,
    slug: generateCategorySlug(rzaCategory.name, rzaCategory.ID, config),
    display: config.defaultDisplay
  };

  if (rzaCategory.ParentID !== undefined) {
    category.parent = rzaCategory.ParentID;
  }

  return category;
}

/**
 * Maps categories with proper hierarchy handling
 * 
 * @param rzaCategories - Array of RZA categories
 * @param config - Mapping configuration
 * @returns Array of WooCommerce categories in proper order
 */
function mapCategoriesWithHierarchy(
  rzaCategories: RzaCategory[],
  config: CategoryMappingConfig
): WooCommerceCategory[] {
  // Build hierarchy tree
  const hierarchy = buildCategoryHierarchy(rzaCategories);
  
  // Flatten hierarchy in proper order (parents before children)
  const orderedCategories: WooCommerceCategory[] = [];
  
  function processHierarchy(nodes: CategoryHierarchy[], level: number = 0) {
    nodes.forEach(node => {
      const wooCategory = mapRzaCategoryToWooCategory(node.category, config);
      wooCategory.menu_order = level * 100 + orderedCategories.length;
      orderedCategories.push(wooCategory);
      
      // Process children
      if (node.children.length > 0) {
        processHierarchy(node.children, level + 1);
      }
    });
  }
  
  processHierarchy(hierarchy);
  return orderedCategories;
}

/**
 * Builds category hierarchy tree from flat array
 * 
 * @param categories - Flat array of RZA categories
 * @returns Hierarchical tree structure
 */
function buildCategoryHierarchy(categories: RzaCategory[]): CategoryHierarchy[] {
  const categoryMap = new Map<number, CategoryHierarchy>();
  const rootCategories: CategoryHierarchy[] = [];

  // Create hierarchy nodes
  categories.forEach(category => {
    categoryMap.set(category.ID, {
      category,
      children: [],
      level: 0
    });
  });

  // Build parent-child relationships
  categories.forEach(category => {
    const node = categoryMap.get(category.ID)!;
    
    if (category.ParentID && categoryMap.has(category.ParentID)) {
      const parent = categoryMap.get(category.ParentID)!;
      parent.children.push(node);
      node.level = parent.level + 1;
    } else {
      rootCategories.push(node);
    }
  });

  return rootCategories;
}

/**
 * Generates a WooCommerce-compatible category slug
 * 
 * @param name - Category name
 * @param id - Category ID
 * @param config - Mapping configuration
 * @returns URL-safe slug
 */
function generateCategorySlug(
  name: string,
  id: number,
  config: CategoryMappingConfig
): string {
  let slug = name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

  // Add prefix if configured
  if (config.slugPrefix) {
    slug = config.slugPrefix + slug;
  }

  // Add ID if configured or slug is too short
  if (slug.length < 3) {
    slug = `${config.slugPrefix}category-${id}`;
  }

  return slug.substring(0, 100); // Limit length
}

/**
 * Builds description for article group category
 * 
 * @param artGroup - RZA article group
 * @param config - Mapping configuration
 * @returns Category description
 */
function buildGroupDescription(
  artGroup: RzaArtGroup,
  config: CategoryMappingConfig
): string {
  let description = `Article group: ${artGroup.name} (ID: ${artGroup.ID})`;

  if (config.includeDiscounts && artGroup.discounts.length > 0) {
    description += '\n\nActive discounts:\n';
    artGroup.discounts.forEach(discount => {
      description += `- ${discount.percent}% off (${discount.fromDate} - ${discount.toDate})\n`;
    });
  }

  return description;
}

/**
 * Builds description for article sub-group category
 * 
 * @param artSubGroup - RZA article sub-group
 * @param config - Mapping configuration
 * @returns Category description
 */
function buildSubGroupDescription(
  artSubGroup: RzaArtSubGroup,
  config: CategoryMappingConfig
): string {
  let description = `Article sub-group: ${artSubGroup.name} (ID: ${artSubGroup.ID})`;

  if (config.includeDiscounts && artSubGroup.discounts && artSubGroup.discounts.length > 0) {
    description += '\n\nActive discounts:\n';
    artSubGroup.discounts.forEach(discount => {
      description += `- ${discount.percent}% off (${discount.fromDate} - ${discount.toDate})\n`;
    });
  }

  return description;
}

/**
 * Extracts active discounts from article groups
 * 
 * @param groupDefinition - RZA group definition
 * @param currentDate - Current date for discount validation (optional)
 * @returns Array of active discounts with group information
 */
export function extractActiveDiscounts(
  groupDefinition: RzaGroupDefinition,
  currentDate: Date = new Date()
): Array<{
  groupId: number;
  groupName: string;
  groupType: 'main' | 'sub';
  discount: RzaDiscount;
}> {
  const activeDiscounts: Array<{
    groupId: number;
    groupName: string;
    groupType: 'main' | 'sub';
    discount: RzaDiscount;
  }> = [];

  // Check main groups
  groupDefinition.artGroups.forEach(group => {
    group.discounts.forEach(discount => {
      if (isDiscountActive(discount, currentDate)) {
        activeDiscounts.push({
          groupId: group.ID,
          groupName: group.name,
          groupType: 'main',
          discount
        });
      }
    });
  });

  // Check sub-groups
  groupDefinition.artSubGroups.forEach(subGroup => {
    if (subGroup.discounts) {
      subGroup.discounts.forEach(discount => {
        if (isDiscountActive(discount, currentDate)) {
          activeDiscounts.push({
            groupId: subGroup.ID,
            groupName: subGroup.name,
            groupType: 'sub',
            discount
          });
        }
      });
    }
  });

  return activeDiscounts;
}

/**
 * Checks if a discount is currently active
 * 
 * @param discount - RZA discount to check
 * @param currentDate - Current date for comparison
 * @returns True if discount is active
 */
function isDiscountActive(discount: RzaDiscount, currentDate: Date): boolean {
  const fromDate = new Date(discount.fromDate);
  const toDate = new Date(discount.toDate);
  
  return currentDate >= fromDate && currentDate <= toDate;
}

/**
 * Finds the best discount for a given article group and price group
 * 
 * @param groupDefinition - RZA group definition
 * @param artGroupId - Article group ID
 * @param artSubGroupId - Article sub-group ID
 * @param priceGroup - Price group to match
 * @param currentDate - Current date for discount validation
 * @returns Best matching discount or undefined
 */
export function findBestGroupDiscount(
  groupDefinition: RzaGroupDefinition,
  artGroupId: number,
  artSubGroupId: number,
  priceGroup: string,
  currentDate: Date = new Date()
): RzaDiscount | undefined {
  const activeDiscounts = extractActiveDiscounts(groupDefinition, currentDate);
  
  // Filter by group IDs and price group
  const matchingDiscounts = activeDiscounts.filter(item => 
    (item.groupId === artGroupId || item.groupId === artSubGroupId) &&
    item.discount.pricegroup === priceGroup
  );

  // Return the highest discount percentage
  return matchingDiscounts
    .sort((a, b) => b.discount.percent - a.discount.percent)[0]?.discount;
}
