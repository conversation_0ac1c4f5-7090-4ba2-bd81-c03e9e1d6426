/**
 * Customer mapping functions for RZA to WooCommerce conversion
 * Handles customer data transformation with best practices
 */

import {
  Rza<PERSON>ustomer,
  WooCommerceCustomer,
  CustomerMappingConfig,
  RzaPriceAgreement
} from '../models/customer.model';
import { RzaField } from '../models/common.model';

/**
 * Default configuration for customer mapping
 */
export const DEFAULT_CUSTOMER_CONFIG: CustomerMappingConfig = {
  defaultRole: 'customer',
  createUsername: true,
  sendWelcomeEmail: false,
  syncBillingAddress: true,
  syncShippingAddress: true,
  defaultCountryCode: 'AT'
};

/**
 * Maps a single RZA customer to WooCommerce customer
 * 
 * @param rzaCustomer - RZA customer data
 * @param config - Mapping configuration
 * @returns WooCommerce customer object
 */
export function mapRzaCustomerToWooCustomer(
  rzaCustomer: RzaCustomer,
  config: Partial<CustomerMappingConfig> = {}
): WooCommerceCustomer {
  const mappingConfig = { ...DEFAULT_CUSTOMER_CONFIG, ...config };

  // Generate username if needed
  const username = mappingConfig.createUsername 
    ? generateUsername(rzaCustomer)
    : undefined;

  // Build the WooCommerce customer
  const wooCustomer: WooCommerceCustomer = {
    email: rzaCustomer.Mail || generateFallbackEmail(rzaCustomer),
    first_name: rzaCustomer.Vorname,
    last_name: rzaCustomer.Zuname,
    username: username,
    role: mappingConfig.defaultRole,
    meta_data: buildCustomerMetaData(rzaCustomer)
  };

  // Add billing address if configured
  if (mappingConfig.syncBillingAddress) {
    wooCustomer.billing = mapToBillingAddress(rzaCustomer, mappingConfig);
  }

  // Add shipping address if configured
  if (mappingConfig.syncShippingAddress) {
    wooCustomer.shipping = mapToShippingAddress(rzaCustomer, mappingConfig);
  }

  return wooCustomer;
}

/**
 * Maps multiple RZA customers to WooCommerce customers
 * 
 * @param rzaCustomers - Array of RZA customers
 * @param config - Mapping configuration
 * @returns Array of WooCommerce customers
 */
export function mapRzaCustomersToWooCustomers(
  rzaCustomers: RzaCustomer[],
  config: Partial<CustomerMappingConfig> = {}
): WooCommerceCustomer[] {
  return rzaCustomers
    .filter(customer => shouldIncludeCustomer(customer))
    .map(customer => mapRzaCustomerToWooCustomer(customer, config));
}

/**
 * Maps RZA customer to WooCommerce billing address
 * 
 * @param rzaCustomer - RZA customer data
 * @param config - Mapping configuration
 * @returns Billing address object
 */
function mapToBillingAddress(
  rzaCustomer: RzaCustomer,
  config: CustomerMappingConfig
): NonNullable<WooCommerceCustomer['billing']> {
  return {
    first_name: rzaCustomer.Vorname,
    last_name: rzaCustomer.Zuname,
    company: buildCompanyName(rzaCustomer),
    address_1: rzaCustomer.Strasse,
    address_2: rzaCustomer.Namenszeile2 || undefined,
    city: rzaCustomer.Ort,
    state: '', // RZA doesn't have state/province
    postcode: rzaCustomer.PLZ,
    country: mapCountryCode(rzaCustomer.Land, config.defaultCountryCode),
    email: rzaCustomer.Mail,
    phone: rzaCustomer.Telefon1 || rzaCustomer.Telefon2
  };
}

/**
 * Maps RZA customer to WooCommerce shipping address
 * 
 * @param rzaCustomer - RZA customer data
 * @param config - Mapping configuration
 * @returns Shipping address object
 */
function mapToShippingAddress(
  rzaCustomer: RzaCustomer,
  config: CustomerMappingConfig
): NonNullable<WooCommerceCustomer['shipping']> {
  return {
    first_name: rzaCustomer.Vorname,
    last_name: rzaCustomer.Zuname,
    company: buildCompanyName(rzaCustomer),
    address_1: rzaCustomer.Strasse,
    address_2: rzaCustomer.Namenszeile2 || undefined,
    city: rzaCustomer.Ort,
    state: '', // RZA doesn't have state/province
    postcode: rzaCustomer.PLZ,
    country: mapCountryCode(rzaCustomer.Land, config.defaultCountryCode)
  };
}

/**
 * Determines if a customer should be included in the sync
 * 
 * @param customer - RZA customer to check
 * @returns True if customer should be included
 */
function shouldIncludeCustomer(customer: RzaCustomer): boolean {
  // Must have email or at least name
  if (!customer.Mail && (!customer.Vorname && !customer.Zuname)) {
    return false;
  }

  // Skip customers with delivery block if configured
  if (customer.Liefersperre === 1) {
    return false;
  }

  return true;
}

/**
 * Generates a username from customer data
 * 
 * @param customer - RZA customer
 * @returns Generated username
 */
function generateUsername(customer: RzaCustomer): string {
  // Use customer number if available
  if (customer.customernumber && customer.customernumber.trim()) {
    return customer.customernumber.toLowerCase().replace(/[^a-z0-9]/g, '');
  }

  // Use email prefix if available
  if (customer.Mail && customer.Mail.includes('@')) {
    return customer.Mail.split('@')[0].toLowerCase().replace(/[^a-z0-9]/g, '');
  }

  // Generate from name
  const firstName = customer.Vorname?.toLowerCase().replace(/[^a-z0-9]/g, '') || '';
  const lastName = customer.Zuname?.toLowerCase().replace(/[^a-z0-9]/g, '') || '';
  
  if (firstName && lastName) {
    return `${firstName}.${lastName}`;
  }
  
  return firstName || lastName || `customer_${customer.rzaAddressId}`;
}

/**
 * Generates a fallback email if none provided
 * 
 * @param customer - RZA customer
 * @returns Fallback email address
 */
function generateFallbackEmail(customer: RzaCustomer): string {
  const username = generateUsername(customer);
  return `${username}@example.com`;
}

/**
 * Builds company name from customer data
 * 
 * @param customer - RZA customer
 * @returns Company name or undefined
 */
function buildCompanyName(customer: RzaCustomer): string | undefined {
  // Use Namenszeile3 as company name if it looks like a company
  if (customer.Namenszeile3 && customer.Namenszeile3.trim()) {
    return customer.Namenszeile3.trim();
  }

  // Check if Namenszeile2 contains company indicators
  if (customer.Namenszeile2 && customer.Namenszeile2.trim()) {
    const line2 = customer.Namenszeile2.trim();
    const companyIndicators = ['GmbH', 'AG', 'KG', 'OG', 'Ltd', 'Inc', 'Corp', 'LLC'];
    
    if (companyIndicators.some(indicator => line2.includes(indicator))) {
      return line2;
    }
  }

  return undefined;
}

/**
 * Maps RZA country to WooCommerce country code
 * 
 * @param rzaCountry - RZA country string
 * @param defaultCode - Default country code
 * @returns WooCommerce country code
 */
function mapCountryCode(rzaCountry: string, defaultCode: string): string {
  if (!rzaCountry || rzaCountry.trim() === '') {
    return defaultCode;
  }

  // Common country mappings
  const countryMappings: Record<string, string> = {
    'Österreich': 'AT',
    'Austria': 'AT',
    'Deutschland': 'DE',
    'Germany': 'DE',
    'Schweiz': 'CH',
    'Switzerland': 'CH',
    'Italien': 'IT',
    'Italy': 'IT',
    'Frankreich': 'FR',
    'France': 'FR',
    'Niederlande': 'NL',
    'Netherlands': 'NL',
    'Belgien': 'BE',
    'Belgium': 'BE',
    'Spanien': 'ES',
    'Spain': 'ES',
    'Polen': 'PL',
    'Poland': 'PL',
    'Tschechien': 'CZ',
    'Czech Republic': 'CZ',
    'Ungarn': 'HU',
    'Hungary': 'HU',
    'Slowakei': 'SK',
    'Slovakia': 'SK',
    'Slowenien': 'SI',
    'Slovenia': 'SI'
  };

  // Check if it's already a country code (2 letters)
  if (rzaCountry.length === 2 && /^[A-Z]{2}$/.test(rzaCountry.toUpperCase())) {
    return rzaCountry.toUpperCase();
  }

  // Look up in mappings
  const mapped = countryMappings[rzaCountry];
  if (mapped) {
    return mapped;
  }

  // Return default if no mapping found
  return defaultCode;
}

/**
 * Builds meta data array for WooCommerce customer
 * 
 * @param customer - RZA customer
 * @returns Array of meta data objects
 */
function buildCustomerMetaData(customer: RzaCustomer): Array<{ key: string; value: string }> {
  const metaData: Array<{ key: string; value: string }> = [
    { key: '_rza_address_id', value: customer.rzaAddressId },
    { key: '_rza_customer_number', value: customer.customernumber || '' },
    { key: '_rza_price_group', value: customer.pricegroup },
    { key: '_rza_country_id', value: customer.countryID.toString() },
    { key: '_rza_credit_limit', value: customer.Kreditlimit.toString() },
    { key: '_rza_delivery_block', value: customer.Liefersperre.toString() }
  ];

  // Add title and salutation if present
  if (customer.Titel) {
    metaData.push({ key: '_rza_title', value: customer.Titel });
  }
  if (customer.Anrede) {
    metaData.push({ key: '_rza_salutation', value: customer.Anrede });
  }

  // Add VAT number if present
  if (customer.UID) {
    metaData.push({ key: '_rza_vat_number', value: customer.UID });
  }

  // Add additional contact information
  if (customer.Telefon2) {
    metaData.push({ key: '_rza_phone_2', value: customer.Telefon2 });
  }
  if (customer.Fax) {
    metaData.push({ key: '_rza_fax', value: customer.Fax });
  }

  // Add custom fields
  customer.fields
    .filter(field => field.value && field.value.trim() !== '')
    .forEach(field => {
      metaData.push({
        key: `_rza_customer_field_${field.fieldnumber}`,
        value: field.value
      });
    });

  // Add total discount information if present
  if (customer.totaldiscount.discount) {
    metaData.push({
      key: '_rza_total_discount',
      value: JSON.stringify(customer.totaldiscount)
    });
  }

  // Add price agreements if present
  if (customer.priceagreements.length > 0) {
    metaData.push({
      key: '_rza_price_agreements',
      value: JSON.stringify(customer.priceagreements)
    });
  }

  return metaData;
}

/**
 * Extracts customer pricing information for WooCommerce
 * 
 * @param customer - RZA customer
 * @returns Pricing information object
 */
export function extractCustomerPricing(customer: RzaCustomer): {
  priceGroup: string;
  totalDiscount?: number;
  priceAgreements: RzaPriceAgreement[];
} {
  const totalDiscountPercent = customer.totaldiscount.discount 
    ? parseFloat(customer.totaldiscount.discount) 
    : undefined;

  return {
    priceGroup: customer.pricegroup,
    totalDiscount: totalDiscountPercent,
    priceAgreements: customer.priceagreements
  };
}
