<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>RZA Export System</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex/dist/katex.min.css">
<link href="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.css" rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="rza-export-system">RZA Export System</h1>
<p>This directory contains the RZA export functionality for converting RZA ERP data to WooCommerce format. The system processes RZA XML exports and transforms them into WooCommerce-compatible data structures for seamless e-commerce integration.</p>
<h2 id="️-architecture">🏗️ Architecture</h2>
<p>The system has been restructured for better maintainability and follows domain separation principles with comprehensive TypeScript support:</p>
<h3 id="directory-structure">Directory Structure</h3>
<pre><code>backend/sync/rza/export/
├── models/                    # TypeScript model definitions
│   ├── common.model.ts       # Shared types (fields, prices, discounts, etc.)
│   ├── article.model.ts      # Article/product models and configurations
│   ├── customer.model.ts     # Customer models and address structures
│   ├── articleGroup.model.ts # Article group models with discount support
│   └── category.model.ts     # Category models with hierarchy support
├── mappers/                  # Domain-specific mapping functions
│   ├── articles.mapper.ts    # Article to WooCommerce product mapping
│   ├── customers.mapper.ts   # Customer to WooCommerce customer mapping
│   └── articleGroups.mapper.ts # Category/group to WooCommerce category mapping
├── exportService.ts          # Main orchestration service with XML parsing
├── testExport.ts            # Test demonstrations and usage examples
└── full_export.example.xml  # Complete RZA export XML structure example
</code></pre>
<h2 id="-quick-start">🚀 Quick Start</h2>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">import</span> {
  mapRzaArticleToWooProduct,
  mapRzaCustomerToWooCustomer,
  mapRzaCategoriesToWooCategories,
  mapRzaGroupsToWooCategories
} <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./mappers/articles.mapper&#x27;</span>;
<span class="hljs-keyword">import</span> { <span class="hljs-title class_">RzaExportService</span> } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./exportService&#x27;</span>;

<span class="hljs-comment">// Map a single article with configuration</span>
<span class="hljs-keyword">const</span> wooProduct = <span class="hljs-title function_">mapRzaArticleToWooProduct</span>(rzaArticle, categories, {
  <span class="hljs-attr">defaultPriceGroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>,
  <span class="hljs-attr">includeInactiveProducts</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">stockThreshold</span>: <span class="hljs-number">5</span>,
  <span class="hljs-attr">defaultTaxClass</span>: <span class="hljs-string">&#x27;standard&#x27;</span>
});

<span class="hljs-comment">// Map a customer with address synchronization</span>
<span class="hljs-keyword">const</span> wooCustomer = <span class="hljs-title function_">mapRzaCustomerToWooCustomer</span>(rzaCustomer, {
  <span class="hljs-attr">defaultRole</span>: <span class="hljs-string">&#x27;customer&#x27;</span>,
  <span class="hljs-attr">createUsername</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">syncBillingAddress</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">syncShippingAddress</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">defaultCountryCode</span>: <span class="hljs-string">&#x27;AT&#x27;</span>
});

<span class="hljs-comment">// Map categories with hierarchy and discount information</span>
<span class="hljs-keyword">const</span> wooCategories = <span class="hljs-title function_">mapRzaCategoriesToWooCategories</span>(rzaCategories, {
  <span class="hljs-attr">createHierarchy</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">slugPrefix</span>: <span class="hljs-string">&#x27;rza-&#x27;</span>,
  <span class="hljs-attr">includeDiscounts</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">maxDepth</span>: <span class="hljs-number">5</span>
});

<span class="hljs-comment">// Map article groups to categories</span>
<span class="hljs-keyword">const</span> wooGroupCategories = <span class="hljs-title function_">mapRzaGroupsToWooCategories</span>(groupDefinition, {
  <span class="hljs-attr">createHierarchy</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">includeDiscounts</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">defaultDisplay</span>: <span class="hljs-string">&#x27;products&#x27;</span>
});
</code></pre>
<h3 id="using-the-export-service">Using the Export Service</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">import</span> { <span class="hljs-title class_">RzaExportService</span> } <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;./exportService&#x27;</span>;

<span class="hljs-keyword">const</span> exportService = <span class="hljs-keyword">new</span> <span class="hljs-title class_">RzaExportService</span>({
  <span class="hljs-attr">xmlFilePath</span>: <span class="hljs-string">&#x27;./data/rza_export.xml&#x27;</span>,
  <span class="hljs-attr">outputDirectory</span>: <span class="hljs-string">&#x27;./tmp/rza-export&#x27;</span>,

  <span class="hljs-comment">// Export configuration</span>
  <span class="hljs-attr">exportArticles</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">exportCustomers</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">exportCategories</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">exportGroups</span>: <span class="hljs-literal">true</span>,

  <span class="hljs-comment">// Output options</span>
  <span class="hljs-attr">generateJsonFiles</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">generateCsvFiles</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">logProgress</span>: <span class="hljs-literal">true</span>,

  <span class="hljs-comment">// Mapping configurations</span>
  <span class="hljs-attr">articleConfig</span>: {
    <span class="hljs-attr">defaultPriceGroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>,
    <span class="hljs-attr">includeInactiveProducts</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">stockThreshold</span>: <span class="hljs-number">0</span>
  },
  <span class="hljs-attr">customerConfig</span>: {
    <span class="hljs-attr">defaultRole</span>: <span class="hljs-string">&#x27;customer&#x27;</span>,
    <span class="hljs-attr">createUsername</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">defaultCountryCode</span>: <span class="hljs-string">&#x27;AT&#x27;</span>
  },
  <span class="hljs-attr">categoryConfig</span>: {
    <span class="hljs-attr">createHierarchy</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">slugPrefix</span>: <span class="hljs-string">&#x27;rza-&#x27;</span>,
    <span class="hljs-attr">includeDiscounts</span>: <span class="hljs-literal">false</span>
  }
});

<span class="hljs-keyword">const</span> result = <span class="hljs-keyword">await</span> exportService.<span class="hljs-title function_">executeExport</span>();
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">`Export completed: <span class="hljs-subst">${result.message}</span>`</span>);
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">`Processed <span class="hljs-subst">${result.stats.articlesProcessed}</span> articles, <span class="hljs-subst">${result.stats.customersProcessed}</span> customers`</span>);
<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">`Output files: <span class="hljs-subst">${result.outputFiles.join(<span class="hljs-string">&#x27;, &#x27;</span>)}</span>`</span>);
</code></pre>
<h2 id="-features">📋 Features</h2>
<h3 id="article-mapping">Article Mapping</h3>
<ul>
<li>✅ Complete product information mapping</li>
<li>✅ Multi-tier pricing with quantity breaks</li>
<li>✅ Tax rate handling per country</li>
<li>✅ Stock management and availability</li>
<li>✅ Custom fields preservation</li>
<li>✅ Textile article variants support</li>
<li>✅ Multi-language translations</li>
<li>✅ Category assignments</li>
<li>✅ Supplier information</li>
</ul>
<h3 id="customer-mapping">Customer Mapping</h3>
<ul>
<li>✅ Complete customer profile mapping</li>
<li>✅ Billing and shipping addresses</li>
<li>✅ Price group assignments</li>
<li>✅ Special price agreements</li>
<li>✅ Credit limits and delivery blocks</li>
<li>✅ VAT number handling</li>
<li>✅ Custom customer fields</li>
<li>✅ Username generation</li>
<li>✅ Country code mapping</li>
</ul>
<h3 id="category-mapping">Category Mapping</h3>
<ul>
<li>✅ Hierarchical category structure</li>
<li>✅ Article group to category mapping</li>
<li>✅ Sub-group handling</li>
<li>✅ Discount information inclusion</li>
<li>✅ SEO-friendly slug generation</li>
<li>✅ Menu ordering</li>
</ul>
<h2 id="-configuration-options">🔧 Configuration Options</h2>
<h3 id="article-mapping-configuration">Article Mapping Configuration</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">interface</span> <span class="hljs-title class_">ArticleMappingConfig</span> {
  <span class="hljs-attr">defaultPriceGroup</span>: <span class="hljs-built_in">string</span>;        <span class="hljs-comment">// Default price group (e.g., &#x27;VK-Preis&#x27;)</span>
  <span class="hljs-attr">defaultLanguageId</span>: <span class="hljs-built_in">number</span>;        <span class="hljs-comment">// Default language for translations (default: 1)</span>
  <span class="hljs-attr">includeInactiveProducts</span>: <span class="hljs-built_in">boolean</span>; <span class="hljs-comment">// Include inactive products (default: false)</span>
  <span class="hljs-attr">stockThreshold</span>: <span class="hljs-built_in">number</span>;           <span class="hljs-comment">// Minimum stock for &#x27;instock&#x27; status (default: 0)</span>
  <span class="hljs-attr">defaultTaxClass</span>: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Default WooCommerce tax class (default: &#x27;&#x27;)</span>
}

<span class="hljs-comment">// Default configuration</span>
<span class="hljs-keyword">const</span> <span class="hljs-variable constant_">DEFAULT_ARTICLE_CONFIG</span> = {
  <span class="hljs-attr">defaultPriceGroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>,
  <span class="hljs-attr">defaultLanguageId</span>: <span class="hljs-number">1</span>,
  <span class="hljs-attr">includeInactiveProducts</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">stockThreshold</span>: <span class="hljs-number">0</span>,
  <span class="hljs-attr">defaultTaxClass</span>: <span class="hljs-string">&#x27;&#x27;</span>
};
</code></pre>
<h3 id="customer-mapping-configuration">Customer Mapping Configuration</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">interface</span> <span class="hljs-title class_">CustomerMappingConfig</span> {
  <span class="hljs-attr">defaultRole</span>: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Default WooCommerce user role (default: &#x27;customer&#x27;)</span>
  <span class="hljs-attr">createUsername</span>: <span class="hljs-built_in">boolean</span>;          <span class="hljs-comment">// Auto-generate usernames (default: true)</span>
  <span class="hljs-attr">sendWelcomeEmail</span>: <span class="hljs-built_in">boolean</span>;        <span class="hljs-comment">// Send welcome emails (default: false)</span>
  <span class="hljs-attr">syncBillingAddress</span>: <span class="hljs-built_in">boolean</span>;      <span class="hljs-comment">// Sync billing address (default: true)</span>
  <span class="hljs-attr">syncShippingAddress</span>: <span class="hljs-built_in">boolean</span>;     <span class="hljs-comment">// Sync shipping address (default: true)</span>
  <span class="hljs-attr">defaultCountryCode</span>: <span class="hljs-built_in">string</span>;       <span class="hljs-comment">// Default country code (default: &#x27;AT&#x27;)</span>
}

<span class="hljs-comment">// Default configuration</span>
<span class="hljs-keyword">const</span> <span class="hljs-variable constant_">DEFAULT_CUSTOMER_CONFIG</span> = {
  <span class="hljs-attr">defaultRole</span>: <span class="hljs-string">&#x27;customer&#x27;</span>,
  <span class="hljs-attr">createUsername</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">sendWelcomeEmail</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">syncBillingAddress</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">syncShippingAddress</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">defaultCountryCode</span>: <span class="hljs-string">&#x27;AT&#x27;</span>
};
</code></pre>
<h3 id="category-mapping-configuration">Category Mapping Configuration</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">interface</span> <span class="hljs-title class_">CategoryMappingConfig</span> {
  <span class="hljs-attr">createHierarchy</span>: <span class="hljs-built_in">boolean</span>;         <span class="hljs-comment">// Maintain parent-child relationships (default: true)</span>
  <span class="hljs-attr">maxDepth</span>: <span class="hljs-built_in">number</span>;                <span class="hljs-comment">// Maximum category depth (default: 5)</span>
  <span class="hljs-attr">slugPrefix</span>: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Prefix for category slugs (default: &#x27;rza-&#x27;)</span>
  <span class="hljs-attr">slugSuffix</span>: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Suffix for category slugs (default: &#x27;&#x27;)</span>
  <span class="hljs-attr">includeIdInSlug</span>: <span class="hljs-built_in">boolean</span>;        <span class="hljs-comment">// Include RZA ID in slug (default: false)</span>
  <span class="hljs-attr">defaultDisplay</span>: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Default category display type (default: &#x27;default&#x27;)</span>
  <span class="hljs-attr">defaultDescription</span>: <span class="hljs-built_in">string</span>;      <span class="hljs-comment">// Default description template (default: &#x27;&#x27;)</span>
  <span class="hljs-attr">includeDiscounts</span>: <span class="hljs-built_in">boolean</span>;       <span class="hljs-comment">// Include discount information (default: false)</span>
  <span class="hljs-attr">updateExisting</span>: <span class="hljs-built_in">boolean</span>;         <span class="hljs-comment">// Update existing categories (default: true)</span>
  <span class="hljs-attr">deleteOrphaned</span>: <span class="hljs-built_in">boolean</span>;         <span class="hljs-comment">// Delete categories not in RZA (default: false)</span>
  <span class="hljs-attr">preserveCustomFields</span>: <span class="hljs-built_in">boolean</span>;   <span class="hljs-comment">// Preserve WooCommerce custom fields (default: true)</span>
}

<span class="hljs-comment">// Default configuration</span>
<span class="hljs-keyword">const</span> <span class="hljs-variable constant_">DEFAULT_CATEGORY_CONFIG</span> = {
  <span class="hljs-attr">createHierarchy</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">maxDepth</span>: <span class="hljs-number">5</span>,
  <span class="hljs-attr">slugPrefix</span>: <span class="hljs-string">&#x27;rza-&#x27;</span>,
  <span class="hljs-attr">slugSuffix</span>: <span class="hljs-string">&#x27;&#x27;</span>,
  <span class="hljs-attr">includeIdInSlug</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">defaultDisplay</span>: <span class="hljs-string">&#x27;default&#x27;</span>,
  <span class="hljs-attr">defaultDescription</span>: <span class="hljs-string">&#x27;&#x27;</span>,
  <span class="hljs-attr">includeDiscounts</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">updateExisting</span>: <span class="hljs-literal">true</span>,
  <span class="hljs-attr">deleteOrphaned</span>: <span class="hljs-literal">false</span>,
  <span class="hljs-attr">preserveCustomFields</span>: <span class="hljs-literal">true</span>
};
</code></pre>
<h2 id="-data-mapping-details">📊 Data Mapping Details</h2>
<h3 id="article-field-mapping">Article Field Mapping</h3>
<p>The article mapper (<code>articles.mapper.ts</code>) performs comprehensive field mapping from RZA articles to WooCommerce products:</p>
<table>
<thead>
<tr>
<th>RZA Field</th>
<th>WooCommerce Field</th>
<th>Transformation Logic</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>name</code></td>
<td><code>name</code></td>
<td>Direct mapping</td>
<td>Product display name</td>
</tr>
<tr>
<td><code>ordernumber</code></td>
<td><code>sku</code></td>
<td>Direct mapping</td>
<td>Unique product identifier</td>
</tr>
<tr>
<td><code>ordernumber</code> + <code>name</code></td>
<td><code>slug</code></td>
<td>URL-safe conversion</td>
<td>Generated using <code>generateProductSlug()</code></td>
</tr>
<tr>
<td><code>description_long</code></td>
<td><code>description</code></td>
<td>HTML cleaning</td>
<td>CDATA wrapper removed, HTML preserved</td>
</tr>
<tr>
<td><code>description_long</code> + fields</td>
<td><code>short_description</code></td>
<td>Auto-generated</td>
<td>Uses custom fields or generated summary</td>
</tr>
<tr>
<td><code>instock</code></td>
<td><code>stock_quantity</code></td>
<td>Max(0, instock)</td>
<td>Available quantity (not actual stock)</td>
</tr>
<tr>
<td><code>stock</code></td>
<td><code>meta_data._rza_stock</code></td>
<td>Direct mapping</td>
<td>Actual stock level preserved</td>
</tr>
<tr>
<td><code>weight</code></td>
<td><code>weight</code></td>
<td>String conversion</td>
<td>Product weight in kg</td>
</tr>
<tr>
<td><code>active</code> + <code>onlineshopstatus</code></td>
<td><code>status</code></td>
<td>Combined logic</td>
<td>'publish' if both are 1, 'draft' otherwise</td>
</tr>
<tr>
<td><code>categories</code></td>
<td><code>categories</code></td>
<td>Category lookup</td>
<td>Maps IDs to category objects with names</td>
</tr>
<tr>
<td><code>tax</code></td>
<td><code>tax_status</code> + <code>tax_class</code></td>
<td>Tax configuration</td>
<td>'taxable' if tax &gt; 0, 'none' otherwise</td>
</tr>
<tr>
<td><code>textilartikel.kennzeichen</code></td>
<td><code>type</code></td>
<td>Product type logic</td>
<td>'variable' for main textile articles, 'simple' otherwise</td>
</tr>
</tbody>
</table>
<h3 id="price-mapping-logic">Price Mapping Logic</h3>
<p>The price mapping system (<code>findBestPrice()</code>) handles complex RZA pricing structures:</p>
<h4 id="price-group-selection">Price Group Selection</h4>
<ul>
<li><strong>Primary Price</strong>: Searches for <code>pricegroup</code> with <code>from: 1</code> (base quantity)</li>
<li><strong>Sale Price</strong>: Searches for <code>pricegroup</code> with <code>from: 10</code> (quantity break)</li>
<li><strong>Price Groups</strong>: Supports multiple customer price groups (VK-Preis, VK-DE-Preis, etc.)</li>
</ul>
<h4 id="quantity-break-logic">Quantity Break Logic</h4>
<pre><code class="language-typescript"><span class="hljs-comment">// Example: Find best price for quantity 25</span>
<span class="hljs-keyword">const</span> prices = [
  { <span class="hljs-attr">price</span>: <span class="hljs-number">10.75</span>, <span class="hljs-attr">pricegroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>, <span class="hljs-attr">from</span>: <span class="hljs-number">1</span> },
  { <span class="hljs-attr">price</span>: <span class="hljs-number">9.10</span>, <span class="hljs-attr">pricegroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>, <span class="hljs-attr">from</span>: <span class="hljs-number">10</span> },
  { <span class="hljs-attr">price</span>: <span class="hljs-number">8.58</span>, <span class="hljs-attr">pricegroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>, <span class="hljs-attr">from</span>: <span class="hljs-number">48</span> }
];
<span class="hljs-comment">// Returns: { price: 9.10, from: 10 } for quantity 25</span>
</code></pre>
<h4 id="woocommerce-price-fields">WooCommerce Price Fields</h4>
<ul>
<li><code>regular_price</code>: Base price from primary price group</li>
<li><code>sale_price</code>: Set only if quantity break price is lower than regular price</li>
<li><code>price</code>: Current effective price (regular or sale)</li>
</ul>
<h3 id="meta-data-preservation">Meta Data Preservation</h3>
<p>All RZA-specific data is preserved in WooCommerce meta fields for complete data integrity:</p>
<h4 id="core-article-meta-data">Core Article Meta Data</h4>
<ul>
<li><code>_rza_artikel_id</code> - RZA article ID (rzaArtikelID)</li>
<li><code>_rza_art_group_id</code> - Article group ID (artGroupID)</li>
<li><code>_rza_art_sub_group_id</code> - Article sub-group ID (artSubGroupID)</li>
<li><code>_rza_unit_id</code> - Unit of measurement (unitID)</li>
<li><code>_rza_ean</code> - EAN barcode</li>
<li><code>_rza_barcode</code> - Additional barcode</li>
<li><code>_rza_tax_rate</code> - Tax rate percentage</li>
<li><code>_rza_shipping_time</code> - Delivery time information</li>
<li><code>_rza_stock</code> - Actual stock level (different from available quantity)</li>
</ul>
<h4 id="supplier-and-custom-data">Supplier and Custom Data</h4>
<ul>
<li><code>_rza_supplier_numbers</code> - JSON array of supplier part numbers</li>
<li><code>_rza_field_1</code> to <code>_rza_field_10</code> - Custom fields (only populated fields)</li>
<li><code>_rza_textile_info</code> - JSON object with textile article data (if applicable)</li>
</ul>
<h3 id="customer-field-mapping">Customer Field Mapping</h3>
<p>The customer mapper (<code>customers.mapper.ts</code>) transforms RZA customer data to WooCommerce customers with comprehensive address handling:</p>
<table>
<thead>
<tr>
<th>RZA Field</th>
<th>WooCommerce Field</th>
<th>Transformation Logic</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Mail</code></td>
<td><code>email</code></td>
<td>Direct mapping or fallback</td>
<td>Generates fallback email if missing</td>
</tr>
<tr>
<td><code>Vorname</code></td>
<td><code>first_name</code></td>
<td>Direct mapping</td>
<td>Customer first name</td>
</tr>
<tr>
<td><code>Zuname</code></td>
<td><code>last_name</code></td>
<td>Direct mapping</td>
<td>Customer last name</td>
</tr>
<tr>
<td><code>customernumber</code> / <code>Mail</code> / Names</td>
<td><code>username</code></td>
<td>Smart generation</td>
<td>Uses customer number, email prefix, or name combination</td>
</tr>
<tr>
<td><code>pricegroup</code></td>
<td><code>meta_data._rza_price_group</code></td>
<td>Direct mapping</td>
<td>Customer price group for pricing</td>
</tr>
</tbody>
</table>
<h4 id="address-mapping-billing--shipping">Address Mapping (Billing &amp; Shipping)</h4>
<table>
<thead>
<tr>
<th>RZA Field</th>
<th>WooCommerce Field</th>
<th>Transformation Logic</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Vorname</code></td>
<td><code>billing.first_name</code></td>
<td>Direct mapping</td>
<td>First name in address</td>
</tr>
<tr>
<td><code>Zuname</code></td>
<td><code>billing.last_name</code></td>
<td>Direct mapping</td>
<td>Last name in address</td>
</tr>
<tr>
<td><code>Namenszeile2</code> / <code>Namenszeile3</code></td>
<td><code>billing.company</code></td>
<td>Company detection</td>
<td>Detects company indicators (GmbH, AG, etc.)</td>
</tr>
<tr>
<td><code>Strasse</code></td>
<td><code>billing.address_1</code></td>
<td>Direct mapping</td>
<td>Street address</td>
</tr>
<tr>
<td><code>Namenszeile2</code></td>
<td><code>billing.address_2</code></td>
<td>Conditional mapping</td>
<td>Additional address line if not company</td>
</tr>
<tr>
<td><code>Ort</code></td>
<td><code>billing.city</code></td>
<td>Direct mapping</td>
<td>City name</td>
</tr>
<tr>
<td><code>PLZ</code></td>
<td><code>billing.postcode</code></td>
<td>Direct mapping</td>
<td>Postal code</td>
</tr>
<tr>
<td><code>Land</code></td>
<td><code>billing.country</code></td>
<td>Country code mapping</td>
<td>Maps German/English names to ISO codes</td>
</tr>
<tr>
<td><code>Telefon1</code> / <code>Telefon2</code></td>
<td><code>billing.phone</code></td>
<td>First available</td>
<td>Primary or secondary phone</td>
</tr>
<tr>
<td><code>Mail</code></td>
<td><code>billing.email</code></td>
<td>Direct mapping</td>
<td>Email address</td>
</tr>
</tbody>
</table>
<h4 id="country-code-mapping">Country Code Mapping</h4>
<p>The system includes comprehensive country mapping for European markets:</p>
<pre><code class="language-typescript"><span class="hljs-keyword">const</span> countryMappings = {
  <span class="hljs-string">&#x27;Österreich&#x27;</span>: <span class="hljs-string">&#x27;AT&#x27;</span>, <span class="hljs-string">&#x27;Austria&#x27;</span>: <span class="hljs-string">&#x27;AT&#x27;</span>,
  <span class="hljs-string">&#x27;Deutschland&#x27;</span>: <span class="hljs-string">&#x27;DE&#x27;</span>, <span class="hljs-string">&#x27;Germany&#x27;</span>: <span class="hljs-string">&#x27;DE&#x27;</span>,
  <span class="hljs-string">&#x27;Schweiz&#x27;</span>: <span class="hljs-string">&#x27;CH&#x27;</span>, <span class="hljs-string">&#x27;Switzerland&#x27;</span>: <span class="hljs-string">&#x27;CH&#x27;</span>,
  <span class="hljs-string">&#x27;Italien&#x27;</span>: <span class="hljs-string">&#x27;IT&#x27;</span>, <span class="hljs-string">&#x27;Italy&#x27;</span>: <span class="hljs-string">&#x27;IT&#x27;</span>,
  <span class="hljs-string">&#x27;Frankreich&#x27;</span>: <span class="hljs-string">&#x27;FR&#x27;</span>, <span class="hljs-string">&#x27;France&#x27;</span>: <span class="hljs-string">&#x27;FR&#x27;</span>,
  <span class="hljs-comment">// ... and more European countries</span>
};
</code></pre>
<h4 id="customer-meta-data-preservation">Customer Meta Data Preservation</h4>
<ul>
<li><code>_rza_address_id</code> - Unique RZA customer/address ID</li>
<li><code>_rza_customer_number</code> - Shop customer number (if available)</li>
<li><code>_rza_price_group</code> - Customer price group</li>
<li><code>_rza_country_id</code> - RZA country ID reference</li>
<li><code>_rza_credit_limit</code> - Customer credit limit</li>
<li><code>_rza_delivery_block</code> - Delivery block status (0/1)</li>
<li><code>_rza_title</code> - Customer title (Dr., Prof., etc.)</li>
<li><code>_rza_salutation</code> - Salutation (Mr., Mrs., etc.)</li>
<li><code>_rza_vat_number</code> - VAT/UID number</li>
<li><code>_rza_phone_2</code> - Secondary phone number</li>
<li><code>_rza_fax</code> - Fax number</li>
<li><code>_rza_customer_field_1</code> to <code>_rza_customer_field_5</code> - Custom customer fields</li>
<li><code>_rza_total_discount</code> - JSON object with general discount configuration</li>
<li><code>_rza_price_agreements</code> - JSON array of special price agreements</li>
</ul>
<h3 id="category-and-article-group-mapping">Category and Article Group Mapping</h3>
<p>The article groups mapper (<code>articleGroups.mapper.ts</code>) handles both RZA categories and article groups with hierarchical support:</p>
<h4 id="article-group-to-category-mapping">Article Group to Category Mapping</h4>
<table>
<thead>
<tr>
<th>RZA Field</th>
<th>WooCommerce Field</th>
<th>Transformation Logic</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>name</code></td>
<td><code>name</code></td>
<td>Direct mapping</td>
<td>Group display name</td>
</tr>
<tr>
<td><code>name</code> + <code>ID</code></td>
<td><code>slug</code></td>
<td>URL-safe generation</td>
<td>Uses <code>generateCategorySlug()</code> with prefix</td>
</tr>
<tr>
<td><code>number</code></td>
<td><code>menu_order</code></td>
<td>Direct mapping</td>
<td>Display order in menus</td>
</tr>
<tr>
<td><code>discounts</code></td>
<td><code>description</code></td>
<td>Discount information</td>
<td>Optional discount details in description</td>
</tr>
</tbody>
</table>
<h4 id="category-hierarchy-mapping">Category Hierarchy Mapping</h4>
<table>
<thead>
<tr>
<th>RZA Field</th>
<th>WooCommerce Field</th>
<th>Transformation Logic</th>
<th>Notes</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>ID</code></td>
<td><code>id</code></td>
<td>Direct mapping</td>
<td>Category identifier</td>
</tr>
<tr>
<td><code>name</code></td>
<td><code>name</code></td>
<td>Direct mapping</td>
<td>Category display name</td>
</tr>
<tr>
<td><code>ParentID</code></td>
<td><code>parent</code></td>
<td>Hierarchy mapping</td>
<td>Parent-child relationships</td>
</tr>
<tr>
<td><code>name</code> + <code>ID</code></td>
<td><code>slug</code></td>
<td>URL-safe generation</td>
<td>Configurable prefix and suffix</td>
</tr>
</tbody>
</table>
<h4 id="hierarchy-processing">Hierarchy Processing</h4>
<p>The system builds proper category hierarchies using <code>buildCategoryHierarchy()</code>:</p>
<ol>
<li><strong>Tree Construction</strong>: Creates parent-child relationships from flat arrays</li>
<li><strong>Level Calculation</strong>: Assigns hierarchy levels for proper ordering</li>
<li><strong>Ordered Output</strong>: Ensures parents are created before children</li>
<li><strong>Menu Ordering</strong>: Assigns menu_order based on hierarchy level and position</li>
</ol>
<h4 id="discount-integration">Discount Integration</h4>
<p>Article groups can include active discount information:</p>
<pre><code class="language-typescript"><span class="hljs-comment">// Example discount structure</span>
<span class="hljs-keyword">const</span> discount = {
  <span class="hljs-attr">fromDate</span>: <span class="hljs-string">&#x27;10.03.2018&#x27;</span>,
  <span class="hljs-attr">toDate</span>: <span class="hljs-string">&#x27;31.03.2018&#x27;</span>,
  <span class="hljs-attr">percent</span>: <span class="hljs-number">5.00</span>,
  <span class="hljs-attr">pricegroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>
};
</code></pre>
<p>The <code>extractActiveDiscounts()</code> function filters discounts by date range and provides:</p>
<ul>
<li>Group-based discounts (main groups and sub-groups)</li>
<li>Price group specific discounts</li>
<li>Date range validation</li>
<li>Discount percentage and validity period</li>
</ul>
<h4 id="category-configuration-options">Category Configuration Options</h4>
<pre><code class="language-typescript"><span class="hljs-keyword">interface</span> <span class="hljs-title class_">CategoryMappingConfig</span> {
  <span class="hljs-attr">createHierarchy</span>: <span class="hljs-built_in">boolean</span>;     <span class="hljs-comment">// Maintain parent-child relationships</span>
  <span class="hljs-attr">maxDepth</span>: <span class="hljs-built_in">number</span>;            <span class="hljs-comment">// Maximum category depth (default: 5)</span>
  <span class="hljs-attr">slugPrefix</span>: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Prefix for slugs (default: &#x27;rza-&#x27;)</span>
  <span class="hljs-attr">slugSuffix</span>: <span class="hljs-built_in">string</span>;          <span class="hljs-comment">// Suffix for slugs</span>
  <span class="hljs-attr">includeIdInSlug</span>: <span class="hljs-built_in">boolean</span>;    <span class="hljs-comment">// Include RZA ID in slug</span>
  <span class="hljs-attr">defaultDisplay</span>: <span class="hljs-built_in">string</span>;      <span class="hljs-comment">// Category display type</span>
  <span class="hljs-attr">includeDiscounts</span>: <span class="hljs-built_in">boolean</span>;   <span class="hljs-comment">// Include discount info in descriptions</span>
  <span class="hljs-attr">updateExisting</span>: <span class="hljs-built_in">boolean</span>;     <span class="hljs-comment">// Update existing categories</span>
  <span class="hljs-attr">deleteOrphaned</span>: <span class="hljs-built_in">boolean</span>;     <span class="hljs-comment">// Delete categories not in RZA</span>
  <span class="hljs-attr">preserveCustomFields</span>: <span class="hljs-built_in">boolean</span>; <span class="hljs-comment">// Preserve WooCommerce custom fields</span>
}
</code></pre>
<h2 id="-export-service-architecture">🔧 Export Service Architecture</h2>
<p>The <code>RzaExportService</code> class orchestrates the complete export process from RZA XML to WooCommerce-compatible JSON files:</p>
<h3 id="service-configuration">Service Configuration</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">interface</span> <span class="hljs-title class_">ExportServiceConfig</span> {
  <span class="hljs-comment">// File paths</span>
  <span class="hljs-attr">xmlFilePath</span>: <span class="hljs-built_in">string</span>;              <span class="hljs-comment">// Path to RZA XML export file</span>
  outputDirectory?: <span class="hljs-built_in">string</span>;         <span class="hljs-comment">// Output directory for generated files</span>

  <span class="hljs-comment">// Export options</span>
  <span class="hljs-attr">exportArticles</span>: <span class="hljs-built_in">boolean</span>;          <span class="hljs-comment">// Export articles/products</span>
  <span class="hljs-attr">exportCustomers</span>: <span class="hljs-built_in">boolean</span>;         <span class="hljs-comment">// Export customers</span>
  <span class="hljs-attr">exportCategories</span>: <span class="hljs-built_in">boolean</span>;        <span class="hljs-comment">// Export product categories</span>
  <span class="hljs-attr">exportGroups</span>: <span class="hljs-built_in">boolean</span>;           <span class="hljs-comment">// Export article groups as categories</span>

  <span class="hljs-comment">// Output formats</span>
  <span class="hljs-attr">generateJsonFiles</span>: <span class="hljs-built_in">boolean</span>;       <span class="hljs-comment">// Generate JSON output files</span>
  <span class="hljs-attr">generateCsvFiles</span>: <span class="hljs-built_in">boolean</span>;        <span class="hljs-comment">// Generate CSV output files (future)</span>
  <span class="hljs-attr">logProgress</span>: <span class="hljs-built_in">boolean</span>;            <span class="hljs-comment">// Enable progress logging</span>

  <span class="hljs-comment">// Domain-specific configurations</span>
  articleConfig?: <span class="hljs-title class_">Partial</span>&lt;<span class="hljs-title class_">ArticleMappingConfig</span>&gt;;
  customerConfig?: <span class="hljs-title class_">Partial</span>&lt;<span class="hljs-title class_">CustomerMappingConfig</span>&gt;;
  categoryConfig?: <span class="hljs-title class_">Partial</span>&lt;<span class="hljs-title class_">CategoryMappingConfig</span>&gt;;
}
</code></pre>
<h3 id="export-process-flow">Export Process Flow</h3>
<ol>
<li><strong>XML Parsing</strong>: Parses RZA XML using <code>xml2js</code> library</li>
<li><strong>Data Extraction</strong>: Extracts articles, customers, categories, and groups</li>
<li><strong>Domain Mapping</strong>: Applies domain-specific mappers with configurations</li>
<li><strong>File Generation</strong>: Creates JSON/CSV output files in specified directory</li>
<li><strong>Result Reporting</strong>: Returns comprehensive statistics and file paths</li>
</ol>
<h3 id="export-result-structure">Export Result Structure</h3>
<pre><code class="language-typescript"><span class="hljs-keyword">interface</span> <span class="hljs-title class_">ExportResult</span> {
  <span class="hljs-attr">success</span>: <span class="hljs-built_in">boolean</span>;
  <span class="hljs-attr">message</span>: <span class="hljs-built_in">string</span>;
  <span class="hljs-attr">stats</span>: {
    <span class="hljs-attr">articlesProcessed</span>: <span class="hljs-built_in">number</span>;
    <span class="hljs-attr">customersProcessed</span>: <span class="hljs-built_in">number</span>;
    <span class="hljs-attr">categoriesProcessed</span>: <span class="hljs-built_in">number</span>;
    <span class="hljs-attr">groupsProcessed</span>: <span class="hljs-built_in">number</span>;
    <span class="hljs-attr">errors</span>: <span class="hljs-built_in">string</span>[];
  };
  <span class="hljs-attr">outputFiles</span>: <span class="hljs-built_in">string</span>[];
}
</code></pre>
<h3 id="generated-output-files">Generated Output Files</h3>
<p>The service generates the following files in the output directory:</p>
<ul>
<li><code>woocommerce-products.json</code> - Mapped product data</li>
<li><code>woocommerce-customers.json</code> - Mapped customer data</li>
<li><code>woocommerce-categories.json</code> - Mapped category data</li>
<li><code>woocommerce-groups.json</code> - Mapped article group categories</li>
</ul>
<h3 id="error-handling">Error Handling</h3>
<p>The service provides comprehensive error handling:</p>
<ul>
<li><strong>XML Parsing Errors</strong>: Invalid XML structure or encoding issues</li>
<li><strong>Mapping Errors</strong>: Data transformation failures with detailed error messages</li>
<li><strong>File System Errors</strong>: Directory creation and file writing issues</li>
<li><strong>Validation Errors</strong>: Missing required fields or invalid data formats</li>
</ul>
<p>Each error is captured with context and included in the export result for debugging.</p>
<h2 id="-rza-xml-data-structure">📋 RZA XML Data Structure</h2>
<p>The system processes RZA XML exports with the following structure:</p>
<h3 id="xml-root-elements">XML Root Elements</h3>
<pre><code class="language-xml"><span class="hljs-tag">&lt;<span class="hljs-name">root</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">articles</span>&gt;</span>          <span class="hljs-comment">&lt;!-- Product/article data --&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">customers</span>&gt;</span>         <span class="hljs-comment">&lt;!-- Customer/address data --&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">orders</span>&gt;</span>           <span class="hljs-comment">&lt;!-- Order status updates (ignored) --&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">groupDefinition</span>&gt;</span>  <span class="hljs-comment">&lt;!-- Article groups and sub-groups --&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">categories</span>&gt;</span>       <span class="hljs-comment">&lt;!-- Product categories --&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">countries</span>&gt;</span>        <span class="hljs-comment">&lt;!-- Country definitions (ignored) --&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">root</span>&gt;</span>
</code></pre>
<h3 id="article-xml-structure">Article XML Structure</h3>
<pre><code class="language-xml"><span class="hljs-tag">&lt;<span class="hljs-name">article</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">ordernumber</span>&gt;</span>GRASS-HOPPER<span class="hljs-tag">&lt;/<span class="hljs-name">ordernumber</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">rzaArtikelID</span>&gt;</span>774<span class="hljs-tag">&lt;/<span class="hljs-name">rzaArtikelID</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">artGroupID</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">artGroupID</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">artSubGroupID</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">artSubGroupID</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">categories</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">category</span>&gt;</span>271<span class="hljs-tag">&lt;/<span class="hljs-name">category</span>&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">categories</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">name</span>&gt;</span>Green Hippo Gras Hoper<span class="hljs-tag">&lt;/<span class="hljs-name">name</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">ean</span>&gt;</span>0<span class="hljs-tag">&lt;/<span class="hljs-name">ean</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">barcode</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">barcode</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">unitID</span>&gt;</span>stk<span class="hljs-tag">&lt;/<span class="hljs-name">unitID</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">instock</span>&gt;</span>0<span class="hljs-tag">&lt;/<span class="hljs-name">instock</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">stock</span>&gt;</span>0<span class="hljs-tag">&lt;/<span class="hljs-name">stock</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">weight</span>&gt;</span>0.000<span class="hljs-tag">&lt;/<span class="hljs-name">weight</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">active</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">active</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">onlineshopstatus</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">onlineshopstatus</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">description_long</span>&gt;</span>&lt;![CDATA[&lt;p&gt;Product description...&lt;/p&gt;]]&gt;<span class="hljs-tag">&lt;/<span class="hljs-name">description_long</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">tax</span>&gt;</span>20.00<span class="hljs-tag">&lt;/<span class="hljs-name">tax</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">taxRates</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">taxRate</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">countryID</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">countryID</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">tax</span>&gt;</span>19.00<span class="hljs-tag">&lt;/<span class="hljs-name">tax</span>&gt;</span>
    <span class="hljs-tag">&lt;/<span class="hljs-name">taxRate</span>&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">taxRates</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">suppliernumbers</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">suppliernumber</span>&gt;</span>Test1<span class="hljs-tag">&lt;/<span class="hljs-name">suppliernumber</span>&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">suppliernumbers</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">shippingtime</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">shippingtime</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">fields</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">field</span> <span class="hljs-attr">fieldnumber</span>=<span class="hljs-string">&quot;1&quot;</span>&gt;</span>Test1<span class="hljs-tag">&lt;/<span class="hljs-name">field</span>&gt;</span>
    <span class="hljs-comment">&lt;!-- ... up to field 10 --&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">fields</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">translations</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">translation</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">languageId</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">languageId</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">name</span>&gt;</span>Product Name - English<span class="hljs-tag">&lt;/<span class="hljs-name">name</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">longdescription</span>&gt;</span>&lt;![CDATA[&lt;p&gt;English description...&lt;/p&gt;]]&gt;<span class="hljs-tag">&lt;/<span class="hljs-name">longdescription</span>&gt;</span>
    <span class="hljs-tag">&lt;/<span class="hljs-name">translation</span>&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">translations</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">prices</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">price</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">price</span>&gt;</span>10.75<span class="hljs-tag">&lt;/<span class="hljs-name">price</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">pricegroup</span>&gt;</span>VK-Preis<span class="hljs-tag">&lt;/<span class="hljs-name">pricegroup</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">from</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">from</span>&gt;</span>
    <span class="hljs-tag">&lt;/<span class="hljs-name">price</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">price</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">price</span>&gt;</span>8.84<span class="hljs-tag">&lt;/<span class="hljs-name">price</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">pricegroup</span>&gt;</span>VK-Preis<span class="hljs-tag">&lt;/<span class="hljs-name">pricegroup</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">from</span>&gt;</span>10<span class="hljs-tag">&lt;/<span class="hljs-name">from</span>&gt;</span>
    <span class="hljs-tag">&lt;/<span class="hljs-name">price</span>&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">prices</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">textilartikel</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">kennzeichen</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">kennzeichen</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">textilartikelID</span>&gt;</span><span class="hljs-tag">&lt;/<span class="hljs-name">textilartikelID</span>&gt;</span>
    <span class="hljs-comment">&lt;!-- ... textile-specific fields --&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">textilartikel</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">article</span>&gt;</span>
</code></pre>
<h3 id="customer-xml-structure">Customer XML Structure</h3>
<pre><code class="language-xml"><span class="hljs-tag">&lt;<span class="hljs-name">customer</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">rzaAddressId</span>&gt;</span>12345<span class="hljs-tag">&lt;/<span class="hljs-name">rzaAddressId</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">customernumber</span>&gt;</span>CUST001<span class="hljs-tag">&lt;/<span class="hljs-name">customernumber</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">pricegroup</span>&gt;</span>Standard VK<span class="hljs-tag">&lt;/<span class="hljs-name">pricegroup</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">fields</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">field</span> <span class="hljs-attr">fieldnumber</span>=<span class="hljs-string">&quot;1&quot;</span>&gt;</span>Custom Value<span class="hljs-tag">&lt;/<span class="hljs-name">field</span>&gt;</span>
    <span class="hljs-comment">&lt;!-- ... up to field 5 --&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">fields</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">totaldiscount</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">discount</span>&gt;</span>5.00<span class="hljs-tag">&lt;/<span class="hljs-name">discount</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">from</span>&gt;</span>2023-01-01<span class="hljs-tag">&lt;/<span class="hljs-name">from</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">to</span>&gt;</span>2023-12-31<span class="hljs-tag">&lt;/<span class="hljs-name">to</span>&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">totaldiscount</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Anrede</span>&gt;</span>Herr<span class="hljs-tag">&lt;/<span class="hljs-name">Anrede</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Titel</span>&gt;</span>Dr.<span class="hljs-tag">&lt;/<span class="hljs-name">Titel</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Zuname</span>&gt;</span>Mustermann<span class="hljs-tag">&lt;/<span class="hljs-name">Zuname</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Vorname</span>&gt;</span>Max<span class="hljs-tag">&lt;/<span class="hljs-name">Vorname</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Namenszeile2</span>&gt;</span>Additional Name<span class="hljs-tag">&lt;/<span class="hljs-name">Namenszeile2</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Namenszeile3</span>&gt;</span>Company GmbH<span class="hljs-tag">&lt;/<span class="hljs-name">Namenszeile3</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Land</span>&gt;</span>Österreich<span class="hljs-tag">&lt;/<span class="hljs-name">Land</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">PLZ</span>&gt;</span>1010<span class="hljs-tag">&lt;/<span class="hljs-name">PLZ</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Ort</span>&gt;</span>Wien<span class="hljs-tag">&lt;/<span class="hljs-name">Ort</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Strasse</span>&gt;</span>Musterstraße 1<span class="hljs-tag">&lt;/<span class="hljs-name">Strasse</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">UID</span>&gt;</span>ATU12345678<span class="hljs-tag">&lt;/<span class="hljs-name">UID</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Mail</span>&gt;</span><EMAIL><span class="hljs-tag">&lt;/<span class="hljs-name">Mail</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Telefon1</span>&gt;</span>+43 1 1234567<span class="hljs-tag">&lt;/<span class="hljs-name">Telefon1</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Telefon2</span>&gt;</span>+43 664 1234567<span class="hljs-tag">&lt;/<span class="hljs-name">Telefon2</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Fax</span>&gt;</span>+43 1 1234568<span class="hljs-tag">&lt;/<span class="hljs-name">Fax</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Kreditlimit</span>&gt;</span>5000<span class="hljs-tag">&lt;/<span class="hljs-name">Kreditlimit</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">Liefersperre</span>&gt;</span>0<span class="hljs-tag">&lt;/<span class="hljs-name">Liefersperre</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">countryID</span>&gt;</span>22<span class="hljs-tag">&lt;/<span class="hljs-name">countryID</span>&gt;</span>
  <span class="hljs-tag">&lt;<span class="hljs-name">priceagreements</span>&gt;</span>
    <span class="hljs-tag">&lt;<span class="hljs-name">agreement</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">rzaArtikelID</span>&gt;</span>774<span class="hljs-tag">&lt;/<span class="hljs-name">rzaArtikelID</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">artGroupID</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">artGroupID</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">artSubGroupID</span>&gt;</span>1<span class="hljs-tag">&lt;/<span class="hljs-name">artSubGroupID</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">discount</span>&gt;</span>10.00<span class="hljs-tag">&lt;/<span class="hljs-name">discount</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">price</span>&gt;</span>9.50<span class="hljs-tag">&lt;/<span class="hljs-name">price</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">from</span>&gt;</span>5<span class="hljs-tag">&lt;/<span class="hljs-name">from</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">fromDate</span>&gt;</span>2023-01-01<span class="hljs-tag">&lt;/<span class="hljs-name">fromDate</span>&gt;</span>
      <span class="hljs-tag">&lt;<span class="hljs-name">toDate</span>&gt;</span>2023-12-31<span class="hljs-tag">&lt;/<span class="hljs-name">toDate</span>&gt;</span>
    <span class="hljs-tag">&lt;/<span class="hljs-name">agreement</span>&gt;</span>
  <span class="hljs-tag">&lt;/<span class="hljs-name">priceagreements</span>&gt;</span>
<span class="hljs-tag">&lt;/<span class="hljs-name">customer</span>&gt;</span>
</code></pre>
<h2 id="-testing-and-validation">🧪 Testing and Validation</h2>
<h3 id="test-suite">Test Suite</h3>
<p>The <code>testExport.ts</code> file provides comprehensive testing and demonstration functionality:</p>
<pre><code class="language-bash"><span class="hljs-comment"># Run TypeScript compilation test</span>
npx tsc --noEmit backend/sync/rza/export/testExport.ts

<span class="hljs-comment"># Run the test demonstrations</span>
node -r ts-node/register backend/sync/rza/export/testExport.ts
</code></pre>
<h3 id="test-functions">Test Functions</h3>
<p>The test suite includes the following test functions:</p>
<h4 id="article-mapping-test">Article Mapping Test</h4>
<pre><code class="language-typescript"><span class="hljs-keyword">async</span> <span class="hljs-keyword">function</span> <span class="hljs-title function_">testArticleMapping</span>(<span class="hljs-params"></span>) {
  <span class="hljs-keyword">const</span> testArticle = <span class="hljs-title function_">createTestArticle</span>();
  <span class="hljs-keyword">const</span> testCategories = <span class="hljs-title function_">createTestCategories</span>();

  <span class="hljs-keyword">const</span> <span class="hljs-attr">config</span>: <span class="hljs-title class_">Partial</span>&lt;<span class="hljs-title class_">ArticleMappingConfig</span>&gt; = {
    <span class="hljs-attr">defaultPriceGroup</span>: <span class="hljs-string">&#x27;VK-Preis&#x27;</span>,
    <span class="hljs-attr">includeInactiveProducts</span>: <span class="hljs-literal">false</span>,
    <span class="hljs-attr">stockThreshold</span>: <span class="hljs-number">5</span>
  };

  <span class="hljs-keyword">const</span> wooProduct = <span class="hljs-title function_">mapRzaArticleToWooProduct</span>(testArticle, testCategories, config);
  <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;Mapped Product:&#x27;</span>, wooProduct);
}
</code></pre>
<h4 id="customer-mapping-test">Customer Mapping Test</h4>
<pre><code class="language-typescript"><span class="hljs-keyword">async</span> <span class="hljs-keyword">function</span> <span class="hljs-title function_">testCustomerMapping</span>(<span class="hljs-params"></span>) {
  <span class="hljs-keyword">const</span> testCustomer = <span class="hljs-title function_">createTestCustomer</span>();

  <span class="hljs-keyword">const</span> <span class="hljs-attr">config</span>: <span class="hljs-title class_">Partial</span>&lt;<span class="hljs-title class_">CustomerMappingConfig</span>&gt; = {
    <span class="hljs-attr">defaultRole</span>: <span class="hljs-string">&#x27;customer&#x27;</span>,
    <span class="hljs-attr">createUsername</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">syncBillingAddress</span>: <span class="hljs-literal">true</span>
  };

  <span class="hljs-keyword">const</span> wooCustomer = <span class="hljs-title function_">mapRzaCustomerToWooCustomer</span>(testCustomer, config);
  <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;Mapped Customer:&#x27;</span>, wooCustomer);
}
</code></pre>
<h4 id="category-mapping-test">Category Mapping Test</h4>
<pre><code class="language-typescript"><span class="hljs-keyword">async</span> <span class="hljs-keyword">function</span> <span class="hljs-title function_">testCategoryMapping</span>(<span class="hljs-params"></span>) {
  <span class="hljs-keyword">const</span> testCategories = <span class="hljs-title function_">createTestCategories</span>();
  <span class="hljs-keyword">const</span> testGroups = <span class="hljs-title function_">createTestGroupDefinition</span>();

  <span class="hljs-keyword">const</span> <span class="hljs-attr">categoryConfig</span>: <span class="hljs-title class_">Partial</span>&lt;<span class="hljs-title class_">CategoryMappingConfig</span>&gt; = {
    <span class="hljs-attr">createHierarchy</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">slugPrefix</span>: <span class="hljs-string">&#x27;rza-&#x27;</span>,
    <span class="hljs-attr">includeDiscounts</span>: <span class="hljs-literal">true</span>
  };

  <span class="hljs-keyword">const</span> wooCategories = <span class="hljs-title function_">mapRzaCategoriesToWooCategories</span>(testCategories, categoryConfig);
  <span class="hljs-keyword">const</span> wooGroups = <span class="hljs-title function_">mapRzaGroupsToWooCategories</span>(testGroups, categoryConfig);

  <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;Mapped Categories:&#x27;</span>, wooCategories);
  <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;Mapped Groups:&#x27;</span>, wooGroups);
}
</code></pre>
<h4 id="export-service-test">Export Service Test</h4>
<pre><code class="language-typescript"><span class="hljs-keyword">async</span> <span class="hljs-keyword">function</span> <span class="hljs-title function_">testExportService</span>(<span class="hljs-params"></span>) {
  <span class="hljs-keyword">const</span> exportService = <span class="hljs-keyword">new</span> <span class="hljs-title class_">RzaExportService</span>({
    <span class="hljs-attr">xmlFilePath</span>: <span class="hljs-string">&#x27;./full_export.example.xml&#x27;</span>,
    <span class="hljs-attr">outputDirectory</span>: <span class="hljs-string">&#x27;./tmp/test-export&#x27;</span>,
    <span class="hljs-attr">exportArticles</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">exportCustomers</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">exportCategories</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">exportGroups</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">generateJsonFiles</span>: <span class="hljs-literal">true</span>,
    <span class="hljs-attr">logProgress</span>: <span class="hljs-literal">true</span>
  });

  <span class="hljs-keyword">const</span> result = <span class="hljs-keyword">await</span> exportService.<span class="hljs-title function_">executeExport</span>();
  <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">&#x27;Export Result:&#x27;</span>, result);
}
</code></pre>
<h3 id="validation-functions">Validation Functions</h3>
<p>The test suite includes validation functions to ensure data integrity:</p>
<ul>
<li><code>validateArticleMapping()</code> - Validates article field mappings</li>
<li><code>validateCustomerMapping()</code> - Validates customer field mappings</li>
<li><code>validateCategoryHierarchy()</code> - Validates category hierarchy structure</li>
<li><code>validatePriceCalculations()</code> - Validates price mapping logic</li>
<li><code>validateMetaDataPreservation()</code> - Validates meta data preservation</li>
</ul>
<h2 id="-integration-with-woocommerce">🔄 Integration with WooCommerce</h2>
<h3 id="woocommerce-rest-api-integration">WooCommerce REST API Integration</h3>
<p>The generated JSON files are designed for direct integration with the WooCommerce REST API:</p>
<pre><code class="language-typescript"><span class="hljs-comment">// Example: Import products to WooCommerce</span>
<span class="hljs-keyword">import</span> <span class="hljs-title class_">WooCommerceRestApi</span> <span class="hljs-keyword">from</span> <span class="hljs-string">&#x27;@woocommerce/woocommerce-rest-api&#x27;</span>;

<span class="hljs-keyword">const</span> <span class="hljs-title class_">WooCommerce</span> = <span class="hljs-keyword">new</span> <span class="hljs-title class_">WooCommerceRestApi</span>({
  <span class="hljs-attr">url</span>: <span class="hljs-string">&#x27;https://your-store.com&#x27;</span>,
  <span class="hljs-attr">consumerKey</span>: <span class="hljs-string">&#x27;ck_...&#x27;</span>,
  <span class="hljs-attr">consumerSecret</span>: <span class="hljs-string">&#x27;cs_...&#x27;</span>,
  <span class="hljs-attr">version</span>: <span class="hljs-string">&#x27;wc/v3&#x27;</span>
});

<span class="hljs-comment">// Import products</span>
<span class="hljs-keyword">const</span> products = <span class="hljs-title class_">JSON</span>.<span class="hljs-title function_">parse</span>(fs.<span class="hljs-title function_">readFileSync</span>(<span class="hljs-string">&#x27;./tmp/woocommerce-products.json&#x27;</span>, <span class="hljs-string">&#x27;utf8&#x27;</span>));
<span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> product <span class="hljs-keyword">of</span> products) {
  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">const</span> response = <span class="hljs-keyword">await</span> <span class="hljs-title class_">WooCommerce</span>.<span class="hljs-title function_">post</span>(<span class="hljs-string">&#x27;products&#x27;</span>, product);
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">`Created product: <span class="hljs-subst">${response.data.name}</span> (ID: <span class="hljs-subst">${response.data.id}</span>)`</span>);
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">error</span>(<span class="hljs-string">`Failed to create product <span class="hljs-subst">${product.name}</span>:`</span>, error.<span class="hljs-property">response</span>?.<span class="hljs-property">data</span>);
  }
}
</code></pre>
<h3 id="batch-processing">Batch Processing</h3>
<p>For large datasets, use WooCommerce batch processing:</p>
<pre><code class="language-typescript"><span class="hljs-comment">// Batch create products (up to 100 per batch)</span>
<span class="hljs-keyword">const</span> batchSize = <span class="hljs-number">100</span>;
<span class="hljs-keyword">const</span> batches = [];

<span class="hljs-keyword">for</span> (<span class="hljs-keyword">let</span> i = <span class="hljs-number">0</span>; i &lt; products.<span class="hljs-property">length</span>; i += batchSize) {
  batches.<span class="hljs-title function_">push</span>(products.<span class="hljs-title function_">slice</span>(i, i + batchSize));
}

<span class="hljs-keyword">for</span> (<span class="hljs-keyword">const</span> batch <span class="hljs-keyword">of</span> batches) {
  <span class="hljs-keyword">const</span> batchData = {
    <span class="hljs-attr">create</span>: batch
  };

  <span class="hljs-keyword">try</span> {
    <span class="hljs-keyword">const</span> response = <span class="hljs-keyword">await</span> <span class="hljs-title class_">WooCommerce</span>.<span class="hljs-title function_">post</span>(<span class="hljs-string">&#x27;products/batch&#x27;</span>, batchData);
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(<span class="hljs-string">`Batch processed: <span class="hljs-subst">${response.data.create.length}</span> products created`</span>);
  } <span class="hljs-keyword">catch</span> (error) {
    <span class="hljs-variable language_">console</span>.<span class="hljs-title function_">error</span>(<span class="hljs-string">&#x27;Batch processing failed:&#x27;</span>, error.<span class="hljs-property">response</span>?.<span class="hljs-property">data</span>);
  }
}
</code></pre>
<h2 id="-best-practices">📝 Best Practices</h2>
<h3 id="data-validation">Data Validation</h3>
<ul>
<li>Always validate RZA XML structure before processing</li>
<li>Check for required fields (name, ordernumber, etc.)</li>
<li>Validate price data and quantity breaks</li>
<li>Ensure category references exist</li>
</ul>
<h3 id="error-handling-1">Error Handling</h3>
<ul>
<li>Implement comprehensive error logging</li>
<li>Use try-catch blocks for individual item processing</li>
<li>Continue processing other items if one fails</li>
<li>Provide detailed error messages with context</li>
</ul>
<h3 id="performance-optimization">Performance Optimization</h3>
<ul>
<li>Process large datasets in batches</li>
<li>Use streaming for very large XML files</li>
<li>Implement progress reporting for long-running operations</li>
<li>Cache category lookups to avoid repeated processing</li>
</ul>
<h3 id="data-integrity">Data Integrity</h3>
<ul>
<li>Preserve all RZA-specific data in meta fields</li>
<li>Maintain bidirectional sync capability</li>
<li>Use consistent naming conventions for meta fields</li>
<li>Document all custom field mappings</li>
</ul>
<h3 id="configuration-management">Configuration Management</h3>
<ul>
<li>Use environment-specific configurations</li>
<li>Validate configuration parameters</li>
<li>Provide sensible defaults for optional settings</li>
<li>Document all configuration options</li>
</ul>
<h2 id="-common-issues-and-solutions">🚨 Common Issues and Solutions</h2>
<h3 id="xml-parsing-issues">XML Parsing Issues</h3>
<p><strong>Problem</strong>: XML parsing fails with encoding errors
<strong>Solution</strong>: Ensure XML file uses UTF-8 encoding and valid XML structure</p>
<h3 id="missing-categories">Missing Categories</h3>
<p><strong>Problem</strong>: Products reference non-existent categories
<strong>Solution</strong>: Process categories before products, or implement category auto-creation</p>
<h3 id="price-calculation-errors">Price Calculation Errors</h3>
<p><strong>Problem</strong>: Incorrect price mapping or missing price groups
<strong>Solution</strong>: Validate price group configuration and implement fallback pricing</p>
<h3 id="username-conflicts">Username Conflicts</h3>
<p><strong>Problem</strong>: Generated usernames already exist in WooCommerce
<strong>Solution</strong>: Implement username uniqueness checking and fallback generation</p>
<h3 id="memory-issues">Memory Issues</h3>
<p><strong>Problem</strong>: Out of memory errors with large XML files
<strong>Solution</strong>: Implement streaming XML parsing or process in smaller chunks</p>
<h2 id="-additional-resources">📚 Additional Resources</h2>
<h3 id="rza-documentation">RZA Documentation</h3>
<ul>
<li>RZA ERP System Documentation</li>
<li>XML Export Format Specification</li>
<li>Field Definitions and Data Types</li>
</ul>
<h3 id="woocommerce-documentation">WooCommerce Documentation</h3>
<ul>
<li><a href="https://woocommerce.github.io/woocommerce-rest-api-docs/">WooCommerce REST API Documentation</a></li>
<li><a href="https://woocommerce.github.io/woocommerce-rest-api-docs/#product-properties">Product Schema Reference</a></li>
<li><a href="https://woocommerce.github.io/woocommerce-rest-api-docs/#customer-properties">Customer Schema Reference</a></li>
<li><a href="https://woocommerce.github.io/woocommerce-rest-api-docs/#product-category-properties">Category Schema Reference</a></li>
</ul>
<h3 id="typescript-resources">TypeScript Resources</h3>
<ul>
<li><a href="https://www.typescriptlang.org/docs/">TypeScript Handbook</a></li>
<li><a href="https://nodejs.org/en/docs/guides/nodejs-docker-webapp/">Node.js TypeScript Best Practices</a></li>
</ul>
<hr>
<p><em>This documentation is maintained alongside the codebase. For the most current information, refer to the TypeScript interfaces and implementation files.</em></p>

            <script async src="https://cdn.jsdelivr.net/npm/katex-copytex@latest/dist/katex-copytex.min.js"></script>
            
        </body>
        </html>