/**
 * RZA Export Module Index
 * Exports all types, interfaces, and functions for RZA to WooCommerce synchronization
 * Updated according to API contract specifications
 */

// === MODELS ===
// Common models
export * from './models/common.model';

// Article models
export * from './models/article.model';

// Customer models  
export * from './models/customer.model';

// Category models
export * from './models/category.model';

// Article group models
export * from './models/articleGroup.model';

// === MAPPERS ===
// Article mappers
export * from './mappers/articles.mapper';

// Customer mappers
export * from './mappers/customers.mapper';

// Article group mappers
export * from './mappers/articleGroups.mapper';

// Note: Stock mappers moved to standalone lagerstand module

// === VALIDATION ===
// Validation functions according to API contract
export * from './validation';

// === SERVICES ===
// Export service
export * from './exportService';

// === TYPE DEFINITIONS ===
// Re-export key interfaces for convenience

// Main data structures
export type {
  RzaExport
} from './models/common.model';

export type {
  RzaArticle,
  WooCommerceProduct,
  ArticleMappingConfig
} from './models/article.model';

export type {
  RzaCustomer,
  WooCommerceCustomer,
  CustomerMappingConfig
} from './models/customer.model';

export type {
  RzaCategory,
  WooCommerceCategory,
  CategoryMappingConfig
} from './models/category.model';

export type {
  RzaGroupDefinition
} from './models/articleGroup.model';

// Note: Stock types moved to standalone lagerstand module

// Configuration interfaces are already exported above with their respective models

export type {
  ValidationResult
} from './validation';

export type {
  ExportServiceConfig,
  ExportResult
} from './exportService';

// === MAIN MAPPING FUNCTIONS ===
// Re-export main mapping functions for easy access
export {
  mapRzaArticleToWooProduct,
  mapRzaArticlesToWooProducts,
  findBestPrice,
  DEFAULT_ARTICLE_CONFIG
} from './mappers/articles.mapper';

// Note: Stock mapping functions moved to standalone lagerstand module

export {
  validateRzaArticle,
  validateRzaCustomer,
  validateRzaCategory,
  validateRzaArticles,
  validatePriceStructure
} from './validation';

// === DEFAULT CONFIGURATIONS ===
// Export default configurations for easy setup
export {
  DEFAULT_EXPORT_CONFIG
} from './exportService';

// === UTILITY FUNCTIONS ===
// Export utility functions that might be useful externally
export {
  generateProductSlug,
  cleanHtmlDescription
} from './mappers/articles.mapper';

// === API CONTRACT COMPLIANCE ===
/**
 * This module implements the RZA-WooCommerce API contract with the following features:
 * 
 * 1. Daily Product Sync (full_export):
 *    - Maps RZA articles to WooCommerce products
 *    - Handles categories, prices, stock, and attributes
 *    - Status logic: 'publish' when active=1 AND onlineshopstatus=1
 *
 * 2. Stock Sync (15-minute):
 *    - Now handled by standalone lagerstand module
 *    - Separate from this export module
 * 
 * 3. Validation according to API contract:
 *    - Article validation: ordernumber, name, rzaArtikelID required
 *    - Price validation: at least one price with from=1 required
 *    - Status validation: active and onlineshopstatus must be 0 or 1
 *    - Weight validation: must be numeric ≥ 0
 *    - Category validation: referenced categories must exist
 * 
 * 4. Field mappings as specified in API contract:
 *    - ordernumber -> sku (direct)
 *    - name -> name (direct)
 *    - description_long -> description (HTML cleanup)
 *    - instock -> stock_quantity (Max(0, instock))
 *    - weight -> weight (string conversion)
 *    - active + onlineshopstatus -> status (combined logic)
 *    - tax -> tax_status ('taxable' when tax > 0)
 *    - prices -> regular_price (price logic)
 *    - fields.field.[n] -> attributes[n] (direct mapping)
 */
