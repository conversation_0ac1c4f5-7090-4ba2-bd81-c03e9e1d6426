/**
 * Test file for the new RZA export structure
 * Demonstrates usage of the separated mappers and models
 */

import {
  // Models
  RzaArticle,
  RzaCustomer,
  RzaCategory,
  RzaGroupDefinition,
  ArticleMappingConfig,
  CustomerMappingConfig,
  CategoryMappingConfig,
  
  // Mappers
  mapRzaArticleToWooProduct,
  mapRzaCustomerToWooCustomer,
  mapRzaCategoriesToWooCategories,
  mapRzaGroupsToWooCategories,
  
  // Service
  RzaExportService
} from './index';

/**
 * Test data creation functions
 */
function createTestArticle(): RzaArticle {
  return {
    ordernumber: 'TEST-001',
    rzaArtikelID: 1001,
    artGroupID: 10,
    artSubGroupID: 101,
    categories: [1, 2, 3],
    name: 'Test Product',
    ean: '1234567890123',
    barcode: 'TEST001',
    unitID: 'stk',
    instock: 50,
    stock: 100,
    weight: 1.5,
    active: 1,
    onlineshopstatus: 1,
    description_long: '<p>This is a test product with <strong>HTML</strong> description.</p>',
    tax: 20,
    taxRates: [
      { countryID: 1, tax: 20 },
      { countryID: 2, tax: 19 }
    ],
    suppliernumbers: ['SUP001', 'SUP002'],
    shippingtime: '2-3 days',
    fields: [
      { fieldnumber: '1', value: 'Custom field 1' },
      { fieldnumber: '2', value: 'Custom field 2' }
    ],
    translations: [
      { languageId: 1, name: 'Test Product', longdescription: 'Test description' },
      { languageId: 2, name: 'Test Produkt', longdescription: 'Test Beschreibung' }
    ],
    prices: [
      { price: 29.99, pricegroup: 'VK-Preis', from: 1 },
      { price: 27.99, pricegroup: 'VK-Preis', from: 10 },
      { price: 25.99, pricegroup: 'VK-Preis', from: 50 }
    ],
    textilartikel: {
      kennzeichen: '1',
      textilartikelID: '1001',
      groessentabelleID: '1',
      groessenID: 'M',
      farbtabelleID: '1',
      farbenID: 'blue',
      saisonID: '2024'
    }
  };
}

function createTestCustomer(): RzaCustomer {
  return {
    rzaAddressId: 'ADDR001',
    customernumber: 'CUST001',
    pricegroup: 'Standard',
    fields: [
      { fieldnumber: '1', value: 'VIP Customer' }
    ],
    totaldiscount: {
      discount: '5',
      from: '2024-01-01',
      to: '2024-12-31'
    },
    Anrede: 'Herr',
    Titel: 'Dr.',
    Zuname: 'Mustermann',
    Vorname: 'Max',
    Namenszeile2: 'Mustermann GmbH',
    Namenszeile3: 'IT Department',
    Land: 'Österreich',
    PLZ: '1010',
    Ort: 'Wien',
    Strasse: 'Teststraße 123',
    UID: 'ATU12345678',
    Mail: '<EMAIL>',
    Telefon1: '+43 1 234567',
    Telefon2: '+43 664 1234567',
    Fax: '+43 1 234567-99',
    Kreditlimit: 10000,
    Liefersperre: 0,
    countryID: 1,
    priceagreements: [
      {
        rzaArtikelID: '1001',
        artGroupID: '10',
        artSubGroupID: '101',
        discount: '10',
        price: '25.99',
        from: '10',
        fromDate: '2024-01-01',
        toDate: '2024-12-31'
      }
    ]
  };
}

function createTestCategories(): RzaCategory[] {
  return [
    { ID: 1, name: 'Electronics' },
    { ID: 2, ParentID: 1, name: 'Computers' },
    { ID: 3, ParentID: 2, name: 'Laptops' },
    { ID: 4, name: 'Clothing' },
    { ID: 5, ParentID: 4, name: 'T-Shirts' }
  ];
}

function createTestGroupDefinition(): RzaGroupDefinition {
  return {
    artGroups: [
      {
        ID: 10,
        number: 1,
        name: 'Electronics',
        discounts: [
          {
            fromDate: '2024-01-01',
            toDate: '2024-12-31',
            percent: 10,
            pricegroup: 'VK-Preis'
          }
        ]
      }
    ],
    artSubGroups: [
      {
        ID: 101,
        number: 1,
        name: 'Computer Hardware',
        discounts: [
          {
            fromDate: '2024-01-01',
            toDate: '2024-06-30',
            percent: 5,
            pricegroup: 'VK-Preis'
          }
        ]
      }
    ]
  };
}

/**
 * Test functions
 */
async function testArticleMapping() {
  console.log('\n=== Testing Article Mapping ===');
  
  const testArticle = createTestArticle();
  const testCategories = createTestCategories();
  
  const config: Partial<ArticleMappingConfig> = {
    defaultPriceGroup: 'VK-Preis',
    includeInactiveProducts: false,
    stockThreshold: 5
  };
  
  const wooProduct = mapRzaArticleToWooProduct(testArticle, testCategories, config);
  
  console.log('RZA Article:', testArticle.name);
  console.log('WooCommerce Product:', {
    name: wooProduct.name,
    sku: wooProduct.sku,
    price: wooProduct.price,
    stock_quantity: wooProduct.stock_quantity,
    categories: wooProduct.categories,
    meta_data: wooProduct.meta_data?.slice(0, 3) // Show first 3 meta fields
  });
}

async function testCustomerMapping() {
  console.log('\n=== Testing Customer Mapping ===');
  
  const testCustomer = createTestCustomer();
  
  const config: Partial<CustomerMappingConfig> = {
    defaultRole: 'customer',
    createUsername: true,
    syncBillingAddress: true
  };
  
  const wooCustomer = mapRzaCustomerToWooCustomer(testCustomer, config);
  
  console.log('RZA Customer:', `${testCustomer.Vorname} ${testCustomer.Zuname}`);
  console.log('WooCommerce Customer:', {
    email: wooCustomer.email,
    first_name: wooCustomer.first_name,
    last_name: wooCustomer.last_name,
    username: wooCustomer.username,
    billing: wooCustomer.billing,
    meta_data: wooCustomer.meta_data?.slice(0, 3) // Show first 3 meta fields
  });
}

async function testCategoryMapping() {
  console.log('\n=== Testing Category Mapping ===');
  
  const testCategories = createTestCategories();
  
  const config: Partial<CategoryMappingConfig> = {
    createHierarchy: true,
    slugPrefix: 'rza-',
    defaultDisplay: 'default'
  };
  
  const wooCategories = mapRzaCategoriesToWooCategories(testCategories, config);
  
  console.log('RZA Categories:', testCategories.length);
  console.log('WooCommerce Categories:', wooCategories.map(cat => ({
    name: cat.name,
    slug: cat.slug,
    parent: cat.parent
  })));
}

async function testGroupMapping() {
  console.log('\n=== Testing Group Mapping ===');
  
  const testGroups = createTestGroupDefinition();
  
  const config: Partial<CategoryMappingConfig> = {
    createHierarchy: true,
    includeDiscounts: true,
    slugPrefix: 'group-'
  };
  
  const wooCategories = mapRzaGroupsToWooCategories(testGroups, config);
  
  console.log('RZA Groups:', testGroups.artGroups.length + testGroups.artSubGroups.length);
  console.log('WooCommerce Categories from Groups:', wooCategories.map(cat => ({
    name: cat.name,
    slug: cat.slug,
    parent: cat.parent,
    description: cat.description?.substring(0, 50) + '...'
  })));
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting RZA Export Tests');
  
  try {
    await testArticleMapping();
    await testCustomerMapping();
    await testCategoryMapping();
    await testGroupMapping();
    
    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

export {
  runTests,
  testArticleMapping,
  testCustomerMapping,
  testCategoryMapping,
  testGroupMapping
};
