/**
 * Integration tests for RZA sync according to API contract
 * Tests verify that the implementation matches the exact specifications in API_Contract.md
 */

import {
  RzaArticle,
  WooCommerceProduct,
  mapRzaArticleToWooProduct,
  validateRzaArticle,
  RzaStockArticle,
  mapRzaStockToWooStock,
  validateStockArticle,
  DEFAULT_ARTICLE_CONFIG
} from '../index';

describe('API Contract Compliance Tests', () => {
  
  describe('Article Export Field Mappings', () => {
    
    test('should map article fields according to API contract', () => {
      const rzaArticle: RzaArticle = {
        ordernumber: 'TEST-001',
        rzaArtikelID: 774,
        artGroupID: 1,
        artSubGroupID: 1,
        categories: [271],
        name: 'Test Product',
        ean: '1234567890123',
        barcode: 'BARCODE123',
        unitID: 'stk',
        instock: 50,
        stock: 100,
        weight: 1.5,
        active: 1,
        onlineshopstatus: 1,
        description_long: '<![CDATA[<p>Product description...</p>]]>',
        tax: 20.0,
        taxRates: [],
        suppliernumbers: [],
        shippingtime: '',
        fields: [
          { fieldnumber: '1', value: 'Additional info 1' },
          { fieldnumber: '2', value: 'Additional info 2' }
        ],
        translations: [],
        prices: [
          { price: 19.99, pricegroup: 'VK-Preis', from: 1 },
          { price: 17.99, pricegroup: 'VK-Preis', from: 10 }
        ],
        textilartikel: { kennzeichen: '1', textilartikelID: '123' }
      };

      const wooProduct = mapRzaArticleToWooProduct(rzaArticle, [], DEFAULT_ARTICLE_CONFIG);

      // API Contract field mappings verification
      expect(wooProduct.sku).toBe('TEST-001'); // ordernumber -> sku (direct)
      expect(wooProduct.name).toBe('Test Product'); // name -> name (direct)
      expect(wooProduct.description).toBe('<p>Product description...</p>'); // description_long -> description (HTML cleanup)
      expect(wooProduct.stock_quantity).toBe(50); // instock -> stock_quantity (Max(0, instock))
      expect(wooProduct.weight).toBe('1.5'); // weight -> weight (string conversion)
      expect(wooProduct.regular_price).toBe('19.99'); // prices -> regular_price (price logic)
      expect(wooProduct.tax_status).toBe('taxable'); // tax -> tax_status ('taxable' when tax > 0)
      expect(wooProduct.attributes).toHaveLength(2); // fields.field.[n] -> attributes[n] (direct mapping)
    });

    test('should determine product status according to API contract', () => {
      const baseArticle: Partial<RzaArticle> = {
        ordernumber: 'TEST-001',
        rzaArtikelID: 774,
        name: 'Test Product',
        prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
        fields: [],
        translations: [],
        taxRates: [],
        suppliernumbers: []
      };

      // API Contract: 'publish' when both active=1 AND onlineshopstatus=1
      const activeOnlineArticle = { ...baseArticle, active: 1, onlineshopstatus: 1 } as RzaArticle;
      const activeOnlineProduct = mapRzaArticleToWooProduct(activeOnlineArticle, [], DEFAULT_ARTICLE_CONFIG);
      expect(activeOnlineProduct.status).toBe('publish');

      // API Contract: 'draft' otherwise
      const inactiveArticle = { ...baseArticle, active: 0, onlineshopstatus: 1 } as RzaArticle;
      const inactiveProduct = mapRzaArticleToWooProduct(inactiveArticle, [], DEFAULT_ARTICLE_CONFIG);
      expect(inactiveProduct.status).toBe('draft');

      const offlineArticle = { ...baseArticle, active: 1, onlineshopstatus: 0 } as RzaArticle;
      const offlineProduct = mapRzaArticleToWooProduct(offlineArticle, [], DEFAULT_ARTICLE_CONFIG);
      expect(offlineProduct.status).toBe('draft');
    });

    test('should handle stock quantity according to API contract', () => {
      const baseArticle: Partial<RzaArticle> = {
        ordernumber: 'TEST-001',
        rzaArtikelID: 774,
        name: 'Test Product',
        prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
        fields: [],
        translations: [],
        taxRates: [],
        suppliernumbers: []
      };

      // API Contract: Max(0, instock) - ensure non-negative stock quantity
      const negativeStockArticle = { ...baseArticle, instock: -5 } as RzaArticle;
      const negativeStockProduct = mapRzaArticleToWooProduct(negativeStockArticle, [], DEFAULT_ARTICLE_CONFIG);
      expect(negativeStockProduct.stock_quantity).toBe(0);

      const positiveStockArticle = { ...baseArticle, instock: 25 } as RzaArticle;
      const positiveStockProduct = mapRzaArticleToWooProduct(positiveStockArticle, [], DEFAULT_ARTICLE_CONFIG);
      expect(positiveStockProduct.stock_quantity).toBe(25);
    });
  });

  describe('Stock Export (15-minute sync)', () => {
    
    test('should map only required fields for stock export', () => {
      const stockArticle: RzaStockArticle = {
        ordernumber: 'ABC123',
        rzaArtikelID: 1001,
        instock: 25,
        stock: 30,
        fields: [
          { fieldnumber: 1, value: 'Test1' },
          { fieldnumber: 2, value: '' }
        ]
      };

      const stockUpdate = mapRzaStockToWooStock(stockArticle);

      // API Contract: Only ordernumber, rzaArtikelID, instock fields are mapped
      expect(stockUpdate.sku).toBe('ABC123'); // ordernumber -> sku (direct)
      expect(stockUpdate.stock_quantity).toBe(25); // instock -> stock_quantity (Max(0, instock))
      expect(stockUpdate.manage_stock).toBe(true);
      expect(stockUpdate.stock_status).toBe('instock');
    });

    test('should handle negative stock in stock export', () => {
      const stockArticle: RzaStockArticle = {
        ordernumber: 'ABC123',
        rzaArtikelID: 1001,
        instock: -10
      };

      const stockUpdate = mapRzaStockToWooStock(stockArticle);

      // API Contract: Max(0, instock)
      expect(stockUpdate.stock_quantity).toBe(0);
      expect(stockUpdate.stock_status).toBe('outofstock');
    });
  });

  describe('Article Validation according to API Contract', () => {
    
    test('should validate required fields', () => {
      const invalidArticle: Partial<RzaArticle> = {
        // Missing ordernumber, name, rzaArtikelID
        prices: [],
        fields: [],
        translations: [],
        taxRates: [],
        suppliernumbers: []
      };

      const validation = validateRzaArticle(invalidArticle as RzaArticle);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Order number (ordernumber) is required');
      expect(validation.errors).toContain('Product name (name) is required');
      expect(validation.errors).toContain('RZA Article ID (rzaArtikelID) is required and must be positive');
    });

    test('should validate price structure', () => {
      const articleWithoutBasePrice: Partial<RzaArticle> = {
        ordernumber: 'TEST-001',
        name: 'Test Product',
        rzaArtikelID: 774,
        active: 1,
        onlineshopstatus: 1,
        weight: 1.0,
        prices: [
          { price: 17.99, pricegroup: 'VK-Preis', from: 10 } // Missing base price with from: 1
        ],
        fields: [],
        translations: [],
        taxRates: [],
        suppliernumbers: []
      };

      const validation = validateRzaArticle(articleWithoutBasePrice as RzaArticle);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('At least one price with from: 1 must be present');
    });

    test('should validate status values', () => {
      const invalidStatusArticle: Partial<RzaArticle> = {
        ordernumber: 'TEST-001',
        name: 'Test Product',
        rzaArtikelID: 774,
        active: 2, // Invalid: must be 0 or 1
        onlineshopstatus: -1, // Invalid: must be 0 or 1
        weight: 1.0,
        prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
        fields: [],
        translations: [],
        taxRates: [],
        suppliernumbers: []
      };

      const validation = validateRzaArticle(invalidStatusArticle as RzaArticle);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Active status must be 0 or 1');
      expect(validation.errors).toContain('Online shop status must be 0 or 1');
    });

    test('should validate weight', () => {
      const invalidWeightArticle: Partial<RzaArticle> = {
        ordernumber: 'TEST-001',
        name: 'Test Product',
        rzaArtikelID: 774,
        active: 1,
        onlineshopstatus: 1,
        weight: -1.5, // Invalid: must be ≥ 0
        prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
        fields: [],
        translations: [],
        taxRates: [],
        suppliernumbers: []
      };

      const validation = validateRzaArticle(invalidWeightArticle as RzaArticle);

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('Weight must be a numeric value ≥ 0');
    });
  });

  describe('Stock Validation according to API Contract', () => {
    
    test('should validate stock article required fields', () => {
      const invalidStockArticle: Partial<RzaStockArticle> = {
        // Missing ordernumber and rzaArtikelID
        instock: 25
      };

      const validation = validateStockArticle(invalidStockArticle as RzaStockArticle);

      expect(validation).toContain('Order number (ordernumber) is required');
      expect(validation).toContain('RZA Article ID (rzaArtikelID) is required and must be positive');
    });

    test('should validate stock article data types', () => {
      const invalidStockArticle = {
        ordernumber: 'ABC123',
        rzaArtikelID: 1001,
        instock: 'invalid' // Should be number
      };

      const validation = validateStockArticle(invalidStockArticle as any);

      expect(validation).toContain('Stock quantity (instock) must be a number');
    });
  });

  describe('Meta Data Preservation according to API Contract', () => {
    
    test('should preserve all required meta data fields', () => {
      const rzaArticle: RzaArticle = {
        ordernumber: 'TEST-001',
        rzaArtikelID: 774,
        artGroupID: 1,
        artSubGroupID: 1,
        categories: [],
        name: 'Test Product',
        ean: '1234567890123',
        barcode: 'BARCODE123',
        unitID: 'stk',
        instock: 50,
        stock: 100,
        weight: 1.5,
        active: 1,
        onlineshopstatus: 1,
        description_long: 'Test description',
        tax: 20.0,
        taxRates: [],
        suppliernumbers: ['SUP001', 'SUP002'],
        shippingtime: '2-3 days',
        fields: [
          { fieldnumber: '1', value: 'Custom field 1' },
          { fieldnumber: '2', value: 'Custom field 2' }
        ],
        translations: [],
        prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
        textilartikel: { kennzeichen: '1', textilartikelID: '123' }
      };

      const wooProduct = mapRzaArticleToWooProduct(rzaArticle, [], DEFAULT_ARTICLE_CONFIG);

      // Verify all required meta data fields are present
      const metaKeys = wooProduct.meta_data?.map(meta => meta.key) || [];
      
      expect(metaKeys).toContain('_rza_artikel_id');
      expect(metaKeys).toContain('_rza_art_group_id');
      expect(metaKeys).toContain('_rza_art_sub_group_id');
      expect(metaKeys).toContain('_rza_unit_id');
      expect(metaKeys).toContain('_rza_ean');
      expect(metaKeys).toContain('_rza_barcode');
      expect(metaKeys).toContain('_rza_tax_rate');
      expect(metaKeys).toContain('_rza_shipping_time');
      expect(metaKeys).toContain('_rza_stock');
      expect(metaKeys).toContain('_rza_supplier_numbers');
      expect(metaKeys).toContain('_rza_field_1');
      expect(metaKeys).toContain('_rza_field_2');
      expect(metaKeys).toContain('_rza_textile_info');
    });
  });
});
