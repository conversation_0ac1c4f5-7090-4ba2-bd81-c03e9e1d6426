/**
 * Customer model types for RZA export
 */

import { RzaField } from './common.model';

/**
 * Total discount configuration for customer
 */
export interface RzaTotalDiscount {
  discount: string; // Discount percentage or amount
  from: string; // Valid from date
  to: string; // Valid to date
}

/**
 * Price agreement for specific customer
 */
export interface RzaPriceAgreement {
  rzaArtikelID: string; // Article ID
  artGroupID: string; // Article group ID
  artSubGroupID: string; // Article sub-group ID
  discount: string; // Discount percentage
  price: string; // Special price
  from: string; // Minimum quantity
  fromDate: string; // Valid from date
  toDate: string; // Valid to date
}

/**
 * Complete RZA customer information
 * Maps to WooCommerce customers
 */
export interface RzaCustomer {
  // Identification
  rzaAddressId: string; // Unique RZA address/customer ID
  customernumber: string; // Shop customer number (if available)
  
  // Pricing and discounts
  pricegroup: string; // Customer price group
  totaldiscount: RzaTotalDiscount; // General discount configuration
  priceagreements: RzaPriceAgreement[]; // Special price agreements
  
  // Personal information
  Anrede: string; // Salutation (Mr., Mrs., etc.)
  Titel: string; // Title (Dr., Prof., etc.)
  Zuname: string; // Last name
  Vorname: string; // First name
  Namenszeile2: string; // Additional name line 2
  Namenszeile3: string; // Additional name line 3
  
  // Address information
  Land: string; // Country
  PLZ: string; // Postal code
  Ort: string; // City
  Strasse: string; // Street address
  
  // Contact information
  Mail: string; // Email address
  Telefon1: string; // Primary phone
  Telefon2: string; // Secondary phone
  Fax: string; // Fax number
  
  // Business information
  UID: string; // VAT number
  
  // Account settings
  Kreditlimit: number; // Credit limit
  Liefersperre: number; // Delivery block (0 = no block, 1 = blocked)
  countryID: number; // Country ID reference
  
  // Custom fields
  fields: RzaField[]; // Custom customer fields (max 5)
}

/**
 * WooCommerce customer interface for mapping
 */
export interface WooCommerceCustomer {
  id?: number;
  email: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  password?: string;
  role?: string;
  billing?: {
    first_name?: string;
    last_name?: string;
    company?: string;
    address_1?: string;
    address_2?: string;
    city?: string;
    state?: string;
    postcode?: string;
    country?: string;
    email?: string;
    phone?: string;
  };
  shipping?: {
    first_name?: string;
    last_name?: string;
    company?: string;
    address_1?: string;
    address_2?: string;
    city?: string;
    state?: string;
    postcode?: string;
    country?: string;
  };
  meta_data?: Array<{ key: string; value: string }>;
}

/**
 * Configuration for customer mapping
 */
export interface CustomerMappingConfig {
  defaultRole: string; // Default WooCommerce user role
  createUsername: boolean; // Whether to auto-generate usernames
  sendWelcomeEmail: boolean; // Whether to send welcome emails
  syncBillingAddress: boolean; // Whether to sync billing address
  syncShippingAddress: boolean; // Whether to sync shipping address
  defaultCountryCode: string; // Default country code if not provided
}
