/**
 * Category-specific model types for RZA export
 * This file focuses on product categories and their WooCommerce mapping
 */

/**
 * RZA product category (from categories section in XML)
 */
export interface RzaCategory {
  ID: number; // Unique category ID
  ParentID?: number; // Parent category ID for hierarchical structure
  name: string; // Category display name
}

/**
 * WooCommerce product category for API operations
 */
export interface WooCommerceCategory {
  id?: number;
  name: string;
  slug?: string;
  parent?: number; // Parent category ID
  description?: string;
  display?: 'default' | 'products' | 'subcategories' | 'both';
  image?: {
    src: string;
    alt?: string;
  };
  menu_order?: number;
  count?: number; // Number of products in category (read-only)
}

/**
 * Category hierarchy helper for building tree structures
 */
export interface CategoryHierarchy {
  category: RzaCategory;
  children: CategoryHierarchy[];
  level: number;
}

/**
 * Category mapping result with metadata
 */
export interface CategoryMappingResult {
  wooCategory: WooCommerceCategory;
  rzaCategory: RzaCategory;
  isNew: boolean;
  hasChildren: boolean;
  parentSlug?: string;
}

/**
 * Configuration for category operations
 */
export interface CategoryMappingConfig {
  // Hierarchy settings
  createHierarchy: boolean; // Whether to maintain parent-child relationships
  maxDepth: number; // Maximum category depth to process

  // Slug generation
  slugPrefix: string; // Prefix for generated slugs (e.g., 'rza-')
  slugSuffix: string; // Suffix for generated slugs
  includeIdInSlug: boolean; // Whether to include RZA ID in slug

  // Display settings
  defaultDisplay: 'default' | 'products' | 'subcategories' | 'both';
  defaultDescription: string; // Default description template
  includeDiscounts: boolean; // Whether to include discount information in descriptions

  // Sync behavior
  updateExisting: boolean; // Whether to update existing categories
  deleteOrphaned: boolean; // Whether to delete categories not in RZA
  preserveCustomFields: boolean; // Whether to preserve WooCommerce custom fields
}
