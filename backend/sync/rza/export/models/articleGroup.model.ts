/**
 * Article group and category model types for RZA export
 */

import { RzaDiscount } from './common.model';

/**
 * Article group (main category)
 */
export interface RzaArtGroup {
  ID: number; // Unique group ID
  number: number; // Group number
  name: string; // Group name/title
  discounts: RzaDiscount[]; // Active discounts for this group
}

/**
 * Article sub-group (subcategory)
 */
export interface RzaArtSubGroup {
  ID: number; // Unique sub-group ID
  number: number; // Sub-group number
  name: string; // Sub-group name/title
  discounts?: RzaDiscount[]; // Active discounts for this sub-group (optional)
}

/**
 * Group definition containing both main groups and sub-groups
 */
export interface RzaGroupDefinition {
  artGroups: RzaArtGroup[]; // Main article groups
  artSubGroups: RzaArtSubGroup[]; // Article sub-groups
}

/**
 * WooCommerce product category interface for mapping groups
 */
export interface WooCommerceGroupCategory {
  id?: number;
  name: string;
  slug?: string;
  parent?: number;
  description?: string;
  display?: 'default' | 'products' | 'subcategories' | 'both';
  image?: {
    src: string;
    alt?: string;
  };
  menu_order?: number;
  count?: number;
}

/**
 * Configuration for group category mapping
 */
export interface GroupCategoryMappingConfig {
  createHierarchy: boolean; // Whether to create hierarchical categories
  includeDiscounts: boolean; // Whether to include discount information
  defaultDisplay: 'default' | 'products' | 'subcategories' | 'both'; // Default display type
  slugPrefix: string; // Prefix for category slugs
}
