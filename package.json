{"name": "klab-woocommerce-connect", "version": "1.0.0", "description": "TypeScript web project for syncing data with WooCommerce", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "build": "npm run build --workspace=backend && npm run build --workspace=frontend", "build:backend": "npm run build --workspace=backend", "build:frontend": "npm run build --workspace=frontend", "start": "npm run start --workspace=backend", "lint": "npm run lint --workspace=backend && npm run lint --workspace=frontend", "test": "npm run test --workspace=backend && npm run test --workspace=frontend"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"dotenv": "^17.0.1"}}